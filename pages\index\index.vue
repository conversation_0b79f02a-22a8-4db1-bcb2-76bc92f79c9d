<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="header">
			<view class="header-content">
				<view class="header-info">
					<view class="header-title">Useful工具箱</view>
					<view class="header-subtitle">激活码管理与每日签到</view>
				</view>
				<view class="header-user" @click="showUserMenu">
					<view class="user-avatar">{{ userDisplayName.charAt(0) }}</view>
					<view class="user-name">{{ userDisplayName }}</view>
					<view class="user-arrow">▼</view>
				</view>
			</view>
		</view>

		<!-- 功能导航卡片 -->
		<view class="nav-cards">
			<!-- 激活码管理卡片 -->
			<view class="nav-card activation-card fade-in" @click="goToActivation">
				<view class="nav-card-icon">🔑</view>
				<view class="nav-card-title">激活码管理</view>
				<view class="nav-card-desc">获取激活码、验证激活码、查询额度</view>
				<view class="nav-card-arrow">→</view>
			</view>

			<!-- 每日签到卡片 -->
			<view class="nav-card signin-card fade-in" style="animation-delay: 0.1s;" @click="goToSignIn">
				<view class="nav-card-icon">📅</view>
				<view class="nav-card-title">每日签到</view>
				<view class="nav-card-desc">每日签到获取额度、查看签到历史</view>
				<view class="nav-card-arrow">→</view>
			</view>
		</view>

		<!-- 快速状态显示 -->
		<view v-if="userInfo" class="quick-status fade-in" style="animation-delay: 0.2s;">
			<view class="status-header">
				<view class="status-title">快速状态</view>
			</view>
			<view class="status-content">
				<view class="status-item">
					<view class="status-label">激活码状态:</view>
					<view class="status-value success">已验证</view>
				</view>
				<view class="status-item">
					<view class="status-label">今日签到:</view>
					<view class="status-value" :class="hasSigned ? 'success' : 'warning'">
						{{ hasSigned ? '已签到' : '未签到' }}
					</view>
				</view>
				<view class="status-item">
					<view class="status-label">剩余额度:</view>
					<view class="status-value">{{ remainingTotalQuota || 0 }}</view>
				</view>
			</view>
		</view>

		<!-- 未验证激活码提示 -->
		<view v-else class="no-user-tip fade-in" style="animation-delay: 0.2s;">
			<view class="tip-icon">⚠️</view>
			<view class="tip-text">您还未验证激活码，请先前往激活码管理页面验证激活码</view>
			<view class="tip-buttons">
				<button class="btn btn-primary" @click="goToActivation">立即验证</button>
				<button class="btn btn-secondary" @click="goToLogin">用户登录</button>
			</view>
		</view>

		<!-- 用户菜单弹窗 -->
		<view v-if="showUserMenuModal" class="modal-overlay" @click="closeUserMenu">
			<view class="user-menu-content" @click.stop="">
				<view class="user-menu-header">
					<view class="menu-avatar">{{ userDisplayName.charAt(0) }}</view>
					<view class="menu-user-info">
						<view class="menu-username">{{ userDisplayName }}</view>
						<view class="menu-user-id">手机: {{ currentUser.phonenumber || 'N/A' }}</view>
					</view>
				</view>
				<view class="user-menu-list">
					<view class="menu-item logout-item" @click="handleLogout">
						<view class="menu-icon">🚪</view>
						<view class="menu-text">退出登录</view>
						<view class="menu-arrow">→</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		signInApi,
		tokenApi,
		utils
	} from '@/api/index'
	import {
		getUserInfo
	} from '@/api/user'
	import {
		checkLogin,
		logout,
		getToken,
		isLoggedIn
	} from '@/utils/auth'

	export default {
		data() {
			return {
				// 用户信息
				userInfo: null,
				currentUser: null,
				userQuota: {
					baseLimit: 0,
					activityLimit: 0,
					tempLimit: 0,
					totalLimit: 0,
					currentUseNum: 0,
					remainingLimit: 0,
					permanentQuota: 0
				},

				// 签到状态
				hasSigned: false,

				// 弹窗状态
				showUserMenuModal: false
			}
		},

		computed: {
			remainingTotalQuota() {
				const totalQuota = (this.userQuota.totalLimit || 0) + (this.userQuota.permanentQuota || 0)
				const used = this.userQuota.currentUseNum || 0
				return Math.max(0, totalQuota - used)
			},

			// 用户显示名称
			userDisplayName() {
				if (this.currentUser) {
					return this.currentUser.nickName || this.currentUser.phone || '用户'
				}
				return '未登录'
			}
		},

		onLoad() {
			// 延迟检查登录状态，确保token已经保存
			setTimeout(() => {
				this.checkLoginAndInit()
			}, 100)
		},

		onShow() {
			// 延迟检查登录状态
			setTimeout(() => {
				this.checkLoginAndInit()
			}, 100)
		},

		methods: {
			// 检查登录状态并初始化页面
			checkLoginAndInit() {
				const token = getToken()
				const userInfo = getUserInfo()
				const loginStatus = isLoggedIn()

				if (!loginStatus) {
					if (!checkLogin(false)) {
						return
					}
				} else {
					this.initPage()
				}
			},

			// 初始化页面
			async initPage() {
				try {
					// 获取当前登录用户信息
					let user = await getUserInfo()
					this.currentUser = user.user
					let phone = String(this.currentUser.phonenumber)
					let len = phone.length;
					this.currentUser.phonenumber = phone.substring(0, 3) + "***" + phone.substring(len - 4, len);

					// 获取用户token状态
					const tokenRes = await tokenApi.getUserToken()
					if (tokenRes.code === 200 && tokenRes.data) {
						const tokenName = tokenRes.data.tokenName
						if (tokenName) {
							// 验证激活码获取用户信息
							const res = await tokenApi.getQuota(tokenName)
							if (res.code === 200) {
								this.userInfo = {
									tokenName: tokenName,
									tokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),
									tokenStatus: 0,
									tokenCompCode: '尚未绑定'
								}

								// 保存额度信息
								this.userQuota = res.data || {
									baseLimit: 0,
									activityLimit: 0,
									tempLimit: 0,
									totalLimit: 0,
									currentUseNum: 0,
									remainingLimit: 0,
									permanentQuota: 0
								}

								// 检查今日是否已签到
								try {
									const signRes = await signInApi.checkSignInToday(tokenName)
									if (signRes.code === 200) {
										this.hasSigned = signRes.data
									}
								} catch (error) {
									console.error('检查签到状态失败:', error)
								}
							}
						}
					}
				} catch (error) {
					console.error('获取用户信息失败:', error)
				}
			},

			// 前往激活码管理页面
			goToActivation() {
				uni.navigateTo({
					url: '/pages/activation/activation'
				})
			},

			// 前往签到页面
			goToSignIn() {
				uni.navigateTo({
					url: '/pages/signin/signin'
				})
			},

			// 前往登录页面
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			},

			// 显示用户菜单
			showUserMenu() {
				this.showUserMenuModal = true
			},

			// 关闭用户菜单
			closeUserMenu() {
				this.showUserMenuModal = false
			},

			// 前往个人信息页面
			goToUserProfile() {
				this.closeUserMenu()
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
			},

			// 前往设置页面
			goToSettings() {
				this.closeUserMenu()
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
			},

			// 前往关于页面
			goToAbout() {
				this.closeUserMenu()
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
			},

			// 处理退出登录
			async handleLogout() {
				this.closeUserMenu()
				await logout()
			}
		}
	}
</script>

<style scoped>
	/* 页面容器 */
	.container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 20rpx;
	}

	/* 页面头部 */
	.header {
		padding: 40rpx 0;
		color: #ffffff;
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
	}

	.header-info {
		flex: 1;
	}

	.header-title {
		font-size: 56rpx;
		font-weight: bold;
		margin-bottom: 15rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}

	.header-subtitle {
		font-size: 32rpx;
		opacity: 0.9;
	}

	/* 用户信息 */
	.header-user {
		display: flex;
		align-items: center;
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10px);
		border-radius: 50rpx;
		padding: 15rpx 25rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.header-user:hover {
		background: rgba(255, 255, 255, 0.2);
		transform: translateY(-2rpx);
	}

	.user-avatar {
		width: 60rpx;
		height: 60rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-weight: bold;
		color: #ffffff;
		margin-right: 15rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.user-name {
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 10rpx;
		max-width: 120rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.user-arrow {
		font-size: 20rpx;
		opacity: 0.8;
		transition: transform 0.3s ease;
	}

	.header-user:hover .user-arrow {
		transform: rotate(180deg);
	}

	/* 导航卡片容器 */
	.nav-cards {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
		margin-bottom: 40rpx;
	}

	/* 导航卡片 */
	.nav-card {
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.nav-card:hover {
		transform: translateY(-6rpx);
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
	}

	.nav-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	}

	.activation-card::before {
		background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
	}

	.signin-card::before {
		background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
	}

	.nav-card-icon {
		font-size: 60rpx;
		margin-bottom: 20rpx;
	}

	.nav-card-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 15rpx;
	}

	.nav-card-desc {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.5;
		margin-bottom: 20rpx;
	}

	.nav-card-arrow {
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 40rpx;
		color: #409EFF;
		font-weight: bold;
	}

	/* 快速状态 */
	.quick-status {
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
		overflow: hidden;
	}

	.status-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
	}

	.status-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.status-content {
		padding: 30rpx;
	}

	.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.status-item:last-child {
		border-bottom: none;
	}

	.status-label {
		font-size: 28rpx;
		color: #666666;
	}

	.status-value {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}

	.status-value.success {
		color: #67C23A;
	}

	.status-value.warning {
		color: #E6A23C;
	}

	/* 未验证提示 */
	.no-user-tip {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 50rpx 30rpx;
		text-align: center;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	}

	.tip-icon {
		font-size: 80rpx;
		margin-bottom: 30rpx;
	}

	.tip-text {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 40rpx;
		line-height: 1.6;
	}

	.tip-buttons {
		display: flex;
		gap: 20rpx;
		justify-content: center;
	}

	.tip-buttons .btn {
		flex: 1;
		max-width: 200rpx;
	}

	/* 按钮样式 */
	.btn {
		padding: 25rpx 50rpx;
		border-radius: 30rpx;
		border: none;
		font-size: 30rpx;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.3s ease;
		text-align: center;
		display: inline-block;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
	}

	.btn-primary {
		background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
		color: #ffffff;
	}

	.btn-secondary {
		background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
		color: #ffffff;
	}

	/* 动画效果 */
	.fade-in {
		animation: fadeInUp 0.6s ease-out;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(30rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 用户菜单弹窗 */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
		animation: fadeIn 0.3s ease-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}

		to {
			opacity: 1;
		}
	}

	.user-menu-content {
		background: #ffffff;
		border-radius: 20rpx;
		width: 90%;
		max-width: 500rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
		animation: slideInUp 0.3s ease-out;
		overflow: hidden;
	}

	@keyframes slideInUp {
		from {
			opacity: 0;
			transform: translateY(50rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.user-menu-header {
		display: flex;
		align-items: center;
		padding: 40rpx 30rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
	}

	.menu-avatar {
		width: 80rpx;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 36rpx;
		font-weight: bold;
		margin-right: 20rpx;
		border: 3rpx solid rgba(255, 255, 255, 0.3);
	}

	.menu-user-info {
		flex: 1;
	}

	.menu-username {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.menu-user-id {
		font-size: 24rpx;
		opacity: 0.8;
	}

	.user-menu-list {
		padding: 20rpx 0;
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		cursor: pointer;
		transition: all 0.3s ease;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.menu-item:hover {
		background-color: #f8f9fa;
	}

	.menu-item:last-child {
		border-bottom: none;
	}

	.menu-item.logout-item {
		color: #f56c6c;
	}

	.menu-item.logout-item:hover {
		background-color: #fef0f0;
	}

	.menu-icon {
		font-size: 32rpx;
		margin-right: 20rpx;
		width: 40rpx;
		text-align: center;
	}

	.menu-text {
		flex: 1;
		font-size: 28rpx;
		font-weight: 500;
	}

	.menu-arrow {
		font-size: 24rpx;
		color: #999999;
		transition: transform 0.3s ease;
	}

	.menu-item:hover .menu-arrow {
		transform: translateX(5rpx);
	}
</style>