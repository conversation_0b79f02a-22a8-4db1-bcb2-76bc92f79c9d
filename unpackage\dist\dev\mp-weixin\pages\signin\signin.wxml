<view class="container data-v-23701386"><view class="header data-v-23701386"><view class="header-title data-v-23701386">每日签到</view><view class="header-subtitle data-v-23701386">坚持签到，获得更多额度</view></view><view class="card fade-in data-v-23701386"><view class="card-header data-v-23701386"><view class="card-title data-v-23701386">每日签到</view><view class="{{['tag','data-v-23701386',hasSigned?'tag-success':'tag-info']}}">{{''+(hasSigned?'今日已签到':'未签到')+''}}</view></view><view class="card-content data-v-23701386"><view class="signin-info data-v-23701386"><view class="signin-stats data-v-23701386"><view class="stat-item data-v-23701386"><view class="stat-number data-v-23701386">{{userStats.continuousDays||0}}</view><view class="stat-label data-v-23701386">连续签到天数</view></view><view class="stat-item data-v-23701386"><view class="stat-number data-v-23701386">{{signInConfig.dailySignInReward||5}}</view><view class="stat-label data-v-23701386">每日奖励额度</view></view></view><block wx:if="{{signInConfig.enableContinuousReward}}"><view class="signin-tip data-v-23701386">{{'连续签到 '+(signInConfig.continuousDaysRequired||7)+" 天可额外获得\n\t\t\t\t\t"+(signInConfig.continuousReward||15)+' 额度'}}</view></block></view><view class="calendar-container data-v-23701386"><view class="calendar-header data-v-23701386"><view class="calendar-title data-v-23701386">{{currentYear+"年"+currentMonth+"月"}}</view></view><view class="calendar-weekdays data-v-23701386"><block wx:for="{{weekDays}}" wx:for-item="day" wx:for-index="__i0__" wx:key="*this"><view class="weekday data-v-23701386">{{day}}</view></block></view><view class="calendar-dates data-v-23701386"><block wx:for="{{$root.l0}}" wx:for-item="date" wx:for-index="__i1__" wx:key="g0"><view class="{{['calendar-date','data-v-23701386',(!date.$orig.day)?'empty':'',(date.$orig.signed)?'signed':'',(date.$orig.isToday)?'today':'',(date.$orig.disabled)?'disabled':'']}}"><block wx:if="{{date.$orig.day}}"><text class="data-v-23701386">{{date.$orig.day}}</text></block><block wx:if="{{date.$orig.signed}}"><view class="signed-mark data-v-23701386">✓</view></block></view></block></view></view><button class="{{['btn','signin-btn','data-v-23701386',hasSigned||!userInfo?'btn-disabled':'btn-primary']}}" disabled="{{hasSigned||!userInfo}}" data-event-opts="{{[['tap',[['handleSignIn',['$event']]]]]}}" bindtap="__e">{{''+(hasSigned?'今日已签到':'立即签到')+''}}</button><view class="signin-history data-v-23701386"><view class="history-header data-v-23701386"><view class="history-title data-v-23701386">签到历史</view><block wx:if="{{hasMoreHistory}}"><view data-event-opts="{{[['tap',[['loadMoreHistory',['$event']]]]]}}" class="history-more data-v-23701386" bindtap="__e">查看更多</view></block></view><view class="history-list data-v-23701386"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="__i2__" wx:key="id"><view class="{{['history-item','data-v-23701386',item.$orig.extraReward?'extra-reward':'']}}"><view class="history-content data-v-23701386"><view class="history-reward data-v-23701386">{{"获得"+item.$orig.rewardAmount+"额度"}}</view><block wx:if="{{item.$orig.extraReward}}"><view class="tag tag-success data-v-23701386">连续签到奖励</view></block></view><view class="history-time data-v-23701386">{{item.m0}}</view></view></block><block wx:if="{{$root.g1===0}}"><view class="no-data data-v-23701386">暂无签到记录</view></block></view></view></view></view><block wx:if="{{!userInfo}}"><view class="card fade-in data-v-23701386" style="animation-delay:0.1s;"><view class="card-content data-v-23701386"><view class="no-user-tip data-v-23701386"><view class="tip-icon data-v-23701386">🔑</view><view class="tip-text data-v-23701386">请先验证激活码后再进行签到</view><button data-event-opts="{{[['tap',[['goToActivation',['$event']]]]]}}" class="btn btn-primary data-v-23701386" bindtap="__e">前往激活码管理</button></view></view></view></block><view class="nav-buttons data-v-23701386"><button data-event-opts="{{[['tap',[['goToActivation',['$event']]]]]}}" class="btn btn-primary nav-btn data-v-23701386" bindtap="__e">激活码管理</button><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="btn btn-secondary nav-btn data-v-23701386" bindtap="__e">返回首页</button></view></view>