// 验证修复效果的简单脚本
import { base64Encode, base64Decode } from '@/utils/base64'
import { utils } from '@/api/index'

// 验证原始问题是否已修复
export function verifyOriginalProblem() {
	console.log('=== 验证原始问题修复 ===')
	
	// 这是导致问题的原始测试用例
	const problematicString = 'Hello World! 你好世界！'
	
	console.log('测试字符串:', problematicString)
	
	try {
		// 直接测试 base64.js
		const encoded = base64Encode(problematicString)
		console.log('Base64 编码结果:', encoded)
		
		// 检查是否包含 undefined 或 NaN
		if (encoded.includes('undefined') || encoded.includes('NaN')) {
			console.error('❌ 编码结果仍包含 undefined 或 NaN')
			return false
		}
		
		const decoded = base64Decode(encoded)
		console.log('Base64 解码结果:', decoded)
		
		const base64Success = problematicString === decoded
		console.log('Base64 测试:', base64Success ? '✅ 成功' : '❌ 失败')
		
		// 测试通过 utils 的加密解密
		const encrypted = utils.encrypt(problematicString)
		console.log('加密结果:', encrypted)
		
		const decrypted = utils.decrypt(encrypted)
		console.log('解密结果:', decrypted)
		
		const encryptSuccess = problematicString === decrypted
		console.log('加密解密测试:', encryptSuccess ? '✅ 成功' : '❌ 失败')
		
		const overallSuccess = base64Success && encryptSuccess
		console.log('总体验证结果:', overallSuccess ? '✅ 问题已修复' : '❌ 问题仍存在')
		
		return overallSuccess
		
	} catch (error) {
		console.error('验证过程出错:', error)
		return false
	}
}

// 验证各种字符类型
export function verifyCharacterTypes() {
	console.log('\n=== 验证各种字符类型 ===')
	
	const testCases = [
		{ name: '英文', text: 'Hello World' },
		{ name: '数字', text: '1234567890' },
		{ name: '中文', text: '你好世界' },
		{ name: '日文', text: 'こんにちは' },
		{ name: '韩文', text: '안녕하세요' },
		{ name: '俄文', text: 'Привет мир' },
		{ name: 'Emoji', text: '🌟🎉🚀💖' },
		{ name: '特殊符号', text: '!@#$%^&*()_+-=[]{}|;:,.<>?' },
		{ name: '混合', text: 'Hello 你好 🌟 123 !@#' }
	]
	
	let allPassed = true
	
	testCases.forEach(testCase => {
		try {
			const encoded = base64Encode(testCase.text)
			const decoded = base64Decode(testCase.text)
			const success = testCase.text === decoded
			
			console.log(`${testCase.name}: ${success ? '✅' : '❌'} "${testCase.text}"`)
			
			if (!success) {
				console.log(`  期望: "${testCase.text}"`)
				console.log(`  实际: "${decoded}"`)
				allPassed = false
			}
			
		} catch (error) {
			console.error(`${testCase.name} 测试出错:`, error)
			allPassed = false
		}
	})
	
	console.log('字符类型验证结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
	return allPassed
}

// 运行完整验证
export function runFullVerification() {
	console.log('=== 开始完整验证 ===')
	
	const originalProblemFixed = verifyOriginalProblem()
	const characterTypesOk = verifyCharacterTypes()
	
	const allGood = originalProblemFixed && characterTypesOk
	
	console.log('\n=== 验证总结 ===')
	console.log('原始问题修复:', originalProblemFixed ? '✅' : '❌')
	console.log('字符类型支持:', characterTypesOk ? '✅' : '❌')
	console.log('总体验证结果:', allGood ? '✅ 修复成功' : '❌ 仍有问题')
	
	return {
		originalProblemFixed,
		characterTypesOk,
		allGood
	}
}

// 在开发环境自动运行验证
if (process.env.NODE_ENV === 'development') {
	setTimeout(() => {
		try {
			runFullVerification()
		} catch (error) {
			console.error('验证脚本运行失败:', error)
		}
	}, 2000)
}
