import http from '@/utils/request'

// 登录方法
export function loginApi(username, password, code, uuid) {
	const data = {
		username,
		password,
		code,
		uuid
	}

	return http.post('/login', data, {
		header: {
			isToken: false,
			repeatSubmit: false
		}
	})
}

// 注册方法
export function register(data) {
	return http.post('/register', data, {
		header: {
			isToken: false
		}
	})
}

// 获取用户详细信息
export function getUserInfo() {
	return http.get('/getInfo')
}

// 退出方法
export function logout() {
	return http.post('/logout')
}

// 获取验证码
export function getCodeImg() {
	return http.get('/captchaImage', {}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

// 得到登录页面配置
export function loginInfo() {
	return http.get('/login/info', {}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

// 发送短信验证码
export function sendSmsCode(phone) {
	return http.get('/login/captcha', {
		phone
	}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

// 手机登录发送短信验证码
export function sendPhoneCode(phone) {
	return http.get('/login/loginCaptcha', {
		phone
	}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

//手机登录验证码验证
export function phoneLogin(phone, code) {
	return http.post('/login/smsLogin', {
		phoneNumber: phone,
		smsCode: code
	}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}