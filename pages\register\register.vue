<template>
	<view class="register-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<!-- 注册卡片 -->
		<view class="register-card fade-in">
			<!-- 头部 -->
			<view class="register-header">
				<view class="logo">📝</view>
				<view class="title">创建新账户</view>
				<view class="subtitle">请填写以下信息完成注册</view>
			</view>

			<!-- 注册表单 -->
			<view class="form-container">
				<!-- 用户名 -->
				<view class="input-group">
					<view class="input-icon">👤</view>
					<input v-model="registerForm.username" placeholder="请输入用户名" class="input-field" />
				</view>

				<!-- 手机号 -->
				<view class="input-group">
					<view class="input-icon">📱</view>
					<input v-model="registerForm.phone" placeholder="请输入手机号" class="input-field" type="number" />
				</view>

				<!-- 短信验证码 -->
				<view class="input-group sms-group">
					<view class="input-icon">💬</view>
					<input v-model="registerForm.phoneCode" placeholder="请输入短信验证码" class="input-field sms-input" />
					<button class="sms-btn" :disabled="smsDisabled" @click="sendSmsCode">
						{{ smsButtonText }}
					</button>
				</view>

				<!-- 密码 -->
				<view class="input-group">
					<view class="input-icon">🔒</view>
					<input v-model="registerForm.password" placeholder="请输入密码" class="input-field"
						:password="!showPassword" />
					<view class="input-suffix" @click="togglePassword">
						{{ showPassword ? '🙈' : '👁️' }}
					</view>
				</view>

				<!-- 确认密码 -->
				<view class="input-group">
					<view class="input-icon">🔐</view>
					<input v-model="registerForm.confirmPassword" placeholder="请确认密码" class="input-field"
						:password="!showConfirmPassword" />
					<view class="input-suffix" @click="toggleConfirmPassword">
						{{ showConfirmPassword ? '🙈' : '👁️' }}
					</view>
				</view>

				<!-- 图形验证码 -->
				<view v-if="captchaEnabled" class="input-group captcha-group">
					<view class="input-icon">🔢</view>
					<input v-model="registerForm.code" placeholder="请输入验证码" class="input-field captcha-input" />
					<view class="captcha-image" @click="refreshCaptcha">
						<image v-if="codeUrl" :src="codeUrl" class="captcha-img" mode="aspectFit" />
						<view v-else class="captcha-placeholder">点击获取</view>
					</view>
				</view>

				<!-- 密码强度提示 -->
				<view v-if="registerForm.password" class="password-strength">
					<view class="strength-label">密码强度：</view>
					<view class="strength-bar">
						<view class="strength-item" :class="{ active: passwordStrength >= 1 }"></view>
						<view class="strength-item" :class="{ active: passwordStrength >= 2 }"></view>
						<view class="strength-item" :class="{ active: passwordStrength >= 3 }"></view>
					</view>
					<view class="strength-text">{{ passwordStrengthText }}</view>
				</view>

				<!-- 用户协议 -->
				<view class="agreement-group">
					<view class="checkbox" :class="{ checked: registerForm.agreement }" @click="toggleAgreement">
						<view class="checkbox-icon">{{ registerForm.agreement ? '✓' : '' }}</view>
					</view>
					<text class="agreement-text">
						我已阅读并同意
						<text class="agreement-link" @click="showAgreement">《用户协议》</text>
						和
						<text class="agreement-link" @click="showPrivacy">《隐私政策》</text>
					</text>
				</view>

				<!-- 注册按钮 -->
				<button class="register-btn" :class="{ loading: registerLoading }"
					:disabled="registerLoading || !canRegister" @click="handleRegister">
					<view v-if="registerLoading" class="loading-icon">⏳</view>
					{{ registerLoading ? '注册中...' : '立即注册' }}
				</button>
			</view>

			<!-- 底部操作 -->
			<view class="register-footer">
				<view class="footer-link" @click="goToLogin">
					已有账号？立即登录
				</view>
			</view>
		</view>

		<!-- 用户协议弹窗 -->
		<view v-if="showAgreementModal" class="modal-overlay" @click="closeAgreementModal">
			<view class="modal-content" @click.stop="">
				<view class="modal-header">
					<view class="modal-title">用户协议</view>
					<view class="modal-close" @click="closeAgreementModal">×</view>
				</view>
				<view class="modal-body">
					<view class="agreement-content">
						<text>1. 用户在使用本服务时，必须遵守相关法律法规。</text>
						<text>2. 用户应当保护好自己的账号和密码，不得将账号借给他人使用。</text>
						<text>3. 用户不得利用本服务进行任何违法违规活动。</text>
						<text>4. 本服务保留对用户行为进行监督和管理的权利。</text>
						<text>5. 如有违反本协议的行为，本服务有权终止用户的使用权限。</text>
					</view>
				</view>
				<view class="modal-footer">
					<button class="btn btn-primary" @click="closeAgreementModal">我知道了</button>
				</view>
			</view>
		</view>

		<!-- 隐私政策弹窗 -->
		<view v-if="showPrivacyModal" class="modal-overlay" @click="closePrivacyModal">
			<view class="modal-content" @click.stop="">
				<view class="modal-header">
					<view class="modal-title">隐私政策</view>
					<view class="modal-close" @click="closePrivacyModal">×</view>
				</view>
				<view class="modal-body">
					<view class="privacy-content">
						<text>1. 我们承诺保护用户的个人隐私信息。</text>
						<text>2. 用户的个人信息仅用于提供更好的服务体验。</text>
						<text>3. 我们不会向第三方泄露用户的个人信息。</text>
						<text>4. 用户有权查看、修改或删除自己的个人信息。</text>
						<text>5. 如有隐私相关问题，请联系我们的客服团队。</text>
					</view>
				</view>
				<view class="modal-footer">
					<button class="btn btn-primary" @click="closePrivacyModal">我知道了</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		register,
		getCodeImg,
		sendSmsCode
	} from '@/api/user'
	import {
		utils
	} from '@/api/index'

	export default {
		data() {
			return {
				// 注册表单
				registerForm: {
					username: '',
					phone: '',
					phoneCode: '',
					password: '',
					confirmPassword: '',
					code: '',
					uuid: '',
					agreement: false
				},

				// 状态控制
				registerLoading: false,
				showPassword: false,
				showConfirmPassword: false,
				captchaEnabled: true,
				codeUrl: '',

				// 短信验证码
				smsDisabled: false,
				smsButtonText: '获取验证码',
				smsCountdown: 0,

				// 弹窗状态
				showAgreementModal: false,
				showPrivacyModal: false
			}
		},

		computed: {
			// 密码强度
			passwordStrength() {
				const password = this.registerForm.password
				if (!password) return 0

				let strength = 0
				// 长度检查
				if (password.length >= 8) strength++
				// 包含数字和字母
				if (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++
				// 包含特殊字符
				if (/[^a-zA-Z0-9]/.test(password)) strength++

				return strength
			},

			// 密码强度文本
			passwordStrengthText() {
				switch (this.passwordStrength) {
					case 0:
					case 1:
						return '弱'
					case 2:
						return '中'
					case 3:
						return '强'
					default:
						return ''
				}
			},

			// 是否可以注册
			canRegister() {
				return this.registerForm.username &&
					this.registerForm.phone &&
					this.registerForm.phoneCode &&
					this.registerForm.password &&
					this.registerForm.confirmPassword &&
					this.registerForm.agreement &&
					(!this.captchaEnabled || this.registerForm.code)
			}
		},

		onLoad() {
			this.initPage()
		},

		methods: {
			// 初始化页面
			async initPage() {
				// 获取验证码
				await this.refreshCaptcha()
			},

			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword
			},

			// 切换确认密码显示
			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword
			},

			// 切换协议同意
			toggleAgreement() {
				this.registerForm.agreement = !this.registerForm.agreement
			},

			// 刷新验证码
			async refreshCaptcha() {
				try {
					const res = await getCodeImg()
					if (res.code === 200) {
						this.captchaEnabled = res.captchaEnabled !== false
						if (this.captchaEnabled) {
							this.codeUrl = "data:image/gif;base64," + res.img
							this.registerForm.uuid = res.uuid
						}
					}
				} catch (error) {
					console.error('获取验证码失败:', error)
				}
			},

			// 发送短信验证码
			async sendSmsCode() {
				if (!this.registerForm.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return
				}

				if (!/^1[3-9]\d{9}$/.test(this.registerForm.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}

				try {
					const res = await sendSmsCode(this.registerForm.phone)
					if (res.code === 200) {
						uni.showToast({
							title: '发送成功',
							icon: 'success'
						})
						this.startSmsCountdown()
					} else {
						uni.showToast({
							title: res.msg || '发送失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('发送短信失败:', error)
					uni.showToast({
						title: '发送失败，请稍后再试',
						icon: 'none'
					})
				}
			},

			// 开始短信倒计时
			startSmsCountdown() {
				this.smsCountdown = 60
				this.smsDisabled = true
				this.smsButtonText = `${this.smsCountdown}s`

				const timer = setInterval(() => {
					this.smsCountdown--
					this.smsButtonText = `${this.smsCountdown}s`

					if (this.smsCountdown <= 0) {
						clearInterval(timer)
						this.smsDisabled = false
						this.smsButtonText = '获取验证码'
					}
				}, 1000)
			},

			// 处理注册
			async handleRegister() {
				// 表单验证
				if (!this.validateForm()) {
					return
				}

				this.registerLoading = true

				try {
					const res = await register({
						username: this.registerForm.username,
						phone: this.registerForm.phone,
						phoneCode: this.registerForm.phoneCode,
						password: this.registerForm.password,
						code: this.registerForm.code,
						uuid: this.registerForm.uuid
					})

					console.log("注册==", res)

					if (res.code === 200) {
						uni.showModal({
							title: '注册成功',
							content: '恭喜您注册成功！请前往登录页面登录。',
							showCancel: false,
							success: () => {
								this.goToLogin()
							}
						})
					} else {
						uni.showToast({
							title: res.msg || '注册失败',
							icon: 'none'
						})
						// 刷新验证码
						if (this.captchaEnabled) {
							await this.refreshCaptcha()
						}
					}
				} catch (error) {
					console.error('注册失败:', error)
					uni.showToast({
						title: '注册失败，请稍后再试',
						icon: 'none'
					})
					// 刷新验证码
					if (this.captchaEnabled) {
						await this.refreshCaptcha()
					}
				} finally {
					this.registerLoading = false
				}
			},

			// 表单验证
			validateForm() {
				if (!this.registerForm.username) {
					uni.showToast({
						title: '请输入用户名',
						icon: 'none'
					})
					return false
				}

				if (this.registerForm.username.length < 3) {
					uni.showToast({
						title: '用户名至少3个字符',
						icon: 'none'
					})
					return false
				}

				if (!this.registerForm.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return false
				}

				if (!/^1[3-9]\d{9}$/.test(this.registerForm.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return false
				}

				if (!this.registerForm.phoneCode) {
					uni.showToast({
						title: '请输入短信验证码',
						icon: 'none'
					})
					return false
				}

				if (!this.registerForm.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					})
					return false
				}

				if (this.registerForm.password.length < 6) {
					uni.showToast({
						title: '密码至少6个字符',
						icon: 'none'
					})
					return false
				}

				if (!this.registerForm.confirmPassword) {
					uni.showToast({
						title: '请确认密码',
						icon: 'none'
					})
					return false
				}

				if (this.registerForm.password !== this.registerForm.confirmPassword) {
					uni.showToast({
						title: '两次密码输入不一致',
						icon: 'none'
					})
					return false
				}

				if (this.captchaEnabled && !this.registerForm.code) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none'
					})
					return false
				}

				if (!this.registerForm.agreement) {
					uni.showToast({
						title: '请同意用户协议和隐私政策',
						icon: 'none'
					})
					return false
				}

				return true
			},

			// 显示用户协议
			showAgreement() {
				this.showAgreementModal = true
			},

			// 关闭用户协议
			closeAgreementModal() {
				this.showAgreementModal = false
			},

			// 显示隐私政策
			showPrivacy() {
				this.showPrivacyModal = true
			},

			// 关闭隐私政策
			closePrivacyModal() {
				this.showPrivacyModal = false
			},

			// 前往登录页面
			goToLogin() {
				uni.navigateBack()
			}
		}
	}
</script>

<style scoped>
	/* 注册容器 */
	.register-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		position: relative;
		overflow: hidden;
	}

	/* 背景装饰 */
	.bg-decoration {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
	}

	.circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		animation: float 6s ease-in-out infinite;
	}

	.circle-1 {
		width: 200rpx;
		height: 200rpx;
		top: 10%;
		left: 10%;
		animation-delay: 0s;
	}

	.circle-2 {
		width: 150rpx;
		height: 150rpx;
		top: 60%;
		right: 15%;
		animation-delay: 2s;
	}

	.circle-3 {
		width: 100rpx;
		height: 100rpx;
		bottom: 20%;
		left: 20%;
		animation-delay: 4s;
	}

	@keyframes float {

		0%,
		100% {
			transform: translateY(0px) rotate(0deg);
		}

		50% {
			transform: translateY(-20px) rotate(180deg);
		}
	}

	/* 注册卡片 */
	.register-card {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		border-radius: 24rpx;
		padding: 60rpx 40rpx;
		width: 100%;
		max-width: 600rpx;
		max-height: 90vh;
		overflow-y: auto;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
		position: relative;
		z-index: 1;
	}

	.fade-in {
		animation: fadeInUp 0.8s ease-out;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(50rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 注册头部 */
	.register-header {
		text-align: center;
		margin-bottom: 50rpx;
	}

	.logo {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 48rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}

	.subtitle {
		font-size: 28rpx;
		color: #666666;
	}

	/* 表单容器 */
	.form-container {
		margin-bottom: 40rpx;
	}

	/* 输入组 */
	.input-group {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border-radius: 16rpx;
		padding: 0 20rpx;
		margin-bottom: 30rpx;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
	}

	.input-group:focus-within {
		border-color: #667eea;
		background: #ffffff;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	.input-icon {
		font-size: 32rpx;
		margin-right: 20rpx;
		color: #999999;
	}

	.input-field {
		flex: 1;
		height: 80rpx;
		font-size: 28rpx;
		color: #333333;
		background: transparent;
		border: none;
	}

	.input-suffix {
		font-size: 32rpx;
		color: #999999;
		cursor: pointer;
		padding: 10rpx;
	}

	/* 验证码相关 */
	.captcha-group {
		padding-right: 0;
	}

	.captcha-input {
		flex: 1;
		margin-right: 20rpx;
	}

	.captcha-image {
		width: 160rpx;
		height: 80rpx;
		border-radius: 8rpx;
		overflow: hidden;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #e9ecef;
	}

	.captcha-img {
		width: 100%;
		height: 100%;
	}

	.captcha-placeholder {
		font-size: 24rpx;
		color: #999999;
	}

	/* 短信验证码 */
	.sms-group {
		padding-right: 0;
	}

	.sms-input {
		flex: 1;
		margin-right: 20rpx;
	}

	.sms-btn {
		padding: 20rpx 30rpx;
		background: #667eea;
		color: #ffffff;
		border: none;
		border-radius: 12rpx;
		font-size: 24rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.sms-btn:disabled {
		background: #c0c4cc;
		cursor: not-allowed;
	}

	/* 密码强度 */
	.password-strength {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		padding: 0 20rpx;
	}

	.strength-label {
		font-size: 24rpx;
		color: #666666;
		margin-right: 15rpx;
	}

	.strength-bar {
		display: flex;
		gap: 8rpx;
		margin-right: 15rpx;
	}

	.strength-item {
		width: 40rpx;
		height: 8rpx;
		background: #e9ecef;
		border-radius: 4rpx;
		transition: all 0.3s ease;
	}

	.strength-item.active:nth-child(1) {
		background: #f56c6c;
	}

	.strength-item.active:nth-child(2) {
		background: #e6a23c;
	}

	.strength-item.active:nth-child(3) {
		background: #67c23a;
	}

	.strength-text {
		font-size: 24rpx;
		color: #666666;
	}

	/* 协议组 */
	.agreement-group {
		display: flex;
		align-items: flex-start;
		margin-bottom: 40rpx;
		padding: 0 20rpx;
	}

	.checkbox {
		width: 36rpx;
		height: 36rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 15rpx;
		margin-top: 4rpx;
		cursor: pointer;
		transition: all 0.3s ease;
		flex-shrink: 0;
	}

	.checkbox.checked {
		background: #667eea;
		border-color: #667eea;
	}

	.checkbox-icon {
		color: #ffffff;
		font-size: 20rpx;
		font-weight: bold;
	}

	.agreement-text {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.6;
	}

	.agreement-link {
		color: #667eea;
		cursor: pointer;
	}

	/* 注册按钮 */
	.register-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		border: none;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.3s ease;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	}

	.register-btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
	}

	.register-btn.loading,
	.register-btn:disabled {
		background: #c0c4cc;
		cursor: not-allowed;
		transform: none;
		box-shadow: 0 8rpx 24rpx rgba(192, 196, 204, 0.3);
	}

	.loading-icon {
		margin-right: 15rpx;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	/* 底部 */
	.register-footer {
		text-align: center;
		margin-top: 40rpx;
	}

	.footer-link {
		color: #667eea;
		font-size: 26rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.footer-link:hover {
		color: #764ba2;
	}

	/* 弹窗样式 */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.modal-content {
		background-color: #ffffff;
		border-radius: 16rpx;
		width: 90%;
		max-width: 600rpx;
		max-height: 80vh;
		overflow-y: auto;
		box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
		animation: modalSlideIn 0.3s ease-out;
	}

	@keyframes modalSlideIn {
		from {
			opacity: 0;
			transform: translateY(-50rpx) scale(0.9);
		}

		to {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.modal-close {
		font-size: 48rpx;
		color: #999999;
		cursor: pointer;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		transition: all 0.3s ease;
	}

	.modal-close:hover {
		background-color: #f5f5f5;
		color: #666666;
	}

	.modal-body {
		padding: 30rpx;
	}

	.modal-footer {
		padding: 30rpx;
		border-top: 1rpx solid #f0f0f0;
		display: flex;
		justify-content: center;
	}

	.agreement-content,
	.privacy-content {
		line-height: 1.8;
	}

	.agreement-content text,
	.privacy-content text {
		display: block;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		color: #333333;
	}

	.btn {
		padding: 20rpx 40rpx;
		border-radius: 25rpx;
		border: none;
		font-size: 28rpx;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.3s ease;
		text-align: center;
		display: inline-block;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.btn-primary {
		background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
		color: #ffffff;
	}

	/* 响应式设计 */
	@media screen and (max-width: 750rpx) {
		.register-card {
			padding: 40rpx 30rpx;
		}

		.title {
			font-size: 42rpx;
		}

		.subtitle {
			font-size: 26rpx;
		}

		.agreement-text {
			font-size: 24rpx;
		}
	}
</style>