{"version": 3, "sources": ["webpack:///./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js?3223", "webpack:///./node_modules/@dcloudio/uni-mp-weixin/dist/index.js?df3c", null, "webpack:///./node_modules/@babel/runtime/helpers/interopRequireDefault.js?47a9", "webpack:///./node_modules/@babel/runtime/helpers/slicedToArray.js?34cf", "webpack:///./node_modules/@babel/runtime/helpers/arrayWithHoles.js?ed45", "webpack:///./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js?7172", "webpack:///./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js?6382", "webpack:///./node_modules/@babel/runtime/helpers/arrayLikeToArray.js?6454", "webpack:///./node_modules/@babel/runtime/helpers/nonIterableRest.js?dd3e", "webpack:///./node_modules/@babel/runtime/helpers/defineProperty.js?7ca3", "webpack:///./node_modules/@babel/runtime/helpers/toPropertyKey.js?d551", "webpack:///./node_modules/@babel/runtime/helpers/typeof.js?3b2d", "webpack:///./node_modules/@babel/runtime/helpers/toPrimitive.js?e6db", "webpack:///./node_modules/@babel/runtime/helpers/construct.js?931d", "webpack:///./node_modules/@babel/runtime/helpers/setPrototypeOf.js?7647", "webpack:///./node_modules/@babel/runtime/helpers/isNativeReflectConstruct.js?011a", "webpack:///./node_modules/@babel/runtime/helpers/toConsumableArray.js?af34", "webpack:///./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js?a708", "webpack:///./node_modules/@babel/runtime/helpers/iterableToArray.js?b893", "webpack:///./node_modules/@babel/runtime/helpers/nonIterableSpread.js?9008", "webpack:///./node_modules/@dcloudio/uni-i18n/dist/uni-i18n.es.js?d3b4", "webpack:///./node_modules/@babel/runtime/helpers/classCallCheck.js?67ad", "webpack:///./node_modules/@babel/runtime/helpers/createClass.js?0bdb", "webpack:///./node_modules/@dcloudio/vue-cli-plugin-uni/packages/mp-vue/dist/mp.runtime.esm.js?3240", "uni-app:///utils/auth.js", "webpack:///./node_modules/@dcloudio/vue-cli-plugin-uni/packages/@babel/runtime/regenerator/index.js?7eb4", "webpack:///./node_modules/@babel/runtime/helpers/regeneratorRuntime.js?9fc1", "webpack:///./node_modules/@babel/runtime/helpers/asyncToGenerator.js?ee10", "webpack:///./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js?828b", "uni-app:///uni.promisify.adaptor.js", "uni-app:///api/index.js", "uni-app:///utils/request.js", "uni-app:///config/index.js", "uni-app:///utils/base64.js", "uni-app:///api/user.js"], "names": ["objectKeys", "singlePageDisableKey", "target", "globalThis", "key", "join", "oldWx", "launchOption", "getLaunchOptionsSync", "isWxKey", "scene", "includes", "indexOf", "initWx", "newWx", "realAtob", "b64", "b64re", "atob", "str", "String", "replace", "test", "Error", "slice", "length", "bitmap", "result", "r1", "r2", "i", "char<PERSON>t", "fromCharCode", "b64DecodeUnicode", "decodeURIComponent", "split", "map", "c", "charCodeAt", "toString", "getCurrentUserInfo", "token", "wx", "getStorageSync", "tokenArr", "uid", "role", "permission", "tokenExpired", "userInfo", "JSON", "parse", "error", "message", "exp", "iat", "uniIdMixin", "<PERSON><PERSON>", "prototype", "uniIDHasRole", "roleId", "uniIDHasPermission", "permissionId", "uniIDTokenValid", "Date", "now", "_toString", "Object", "hasOwnProperty", "isFn", "fn", "isStr", "isObject", "obj", "isPlainObject", "call", "hasOwn", "noop", "cached", "cache", "create", "cachedFn", "hit", "camelizeRE", "camelize", "_", "toUpperCase", "sortObject", "sortObj", "keys", "sort", "for<PERSON>ach", "HOOKS", "globalInterceptors", "scopedInterceptors", "mergeHook", "parentVal", "childVal", "res", "concat", "Array", "isArray", "dedupe<PERSON><PERSON>s", "hooks", "push", "removeH<PERSON>", "hook", "index", "splice", "mergeInterceptorHook", "interceptor", "option", "removeInterceptorHook", "addInterceptor", "method", "removeInterceptor", "wrapperHook", "params", "data", "isPromise", "then", "queue", "promise", "Promise", "resolve", "callback", "wrapperOptions", "options", "name", "oldCallback", "callbackInterceptor", "wrapperReturnValue", "returnValue", "returnValueHooks", "getApiInterceptorHooks", "scopedInterceptor", "invokeApi", "api", "invoke", "promiseInterceptor", "reject", "SYNC_API_RE", "CONTEXT_API_RE", "CONTEXT_API_RE_EXC", "ASYNC_API", "CALLBACK_API_RE", "isContextApi", "isSyncApi", "isCallbackApi", "handlePromise", "catch", "err", "shouldPromise", "finally", "constructor", "value", "reason", "promisify", "promiseApi", "success", "fail", "complete", "assign", "EPS", "BASE_DEVICE_WIDTH", "isIOS", "deviceWidth", "deviceDPR", "checkDeviceWidth", "getWindowInfo", "platform", "getDeviceInfo", "windowWidth", "pixelRatio", "upx2px", "number", "newDeviceWidth", "Number", "Math", "floor", "LOCALE_ZH_HANS", "LOCALE_ZH_HANT", "LOCALE_EN", "LOCALE_FR", "LOCALE_ES", "messages", "locale", "normalizeLocale", "getAppBaseInfo", "language", "initI18nMessages", "isEnableLocale", "localeKeys", "__uniConfig", "locales", "curMessages", "userMessages", "i18n", "initVueI18n", "t", "i18nMixin", "mixin", "beforeCreate", "unwatch", "watchLocale", "$forceUpdate", "$once", "methods", "$$t", "values", "setLocale", "getLocale", "initAppLocale", "appVm", "state", "observable", "localeWatchers", "$watchLocale", "defineProperty", "get", "set", "v", "watch", "include", "parts", "find", "part", "startsWith", "trim", "toLowerCase", "lang", "getLocale$1", "getApp", "app", "allowDefault", "$vm", "$locale", "setLocale$1", "oldLocale", "onLocaleChangeCallbacks", "onLocaleChange", "global", "interceptors", "baseApi", "freeze", "__proto__", "rpx2px", "findExistsPageIndex", "url", "pages", "getCurrentPages", "len", "page", "$page", "fullPath", "redirectTo", "fromArgs", "exists", "delta", "args", "existsPageIndex", "previewImage", "currentIndex", "parseInt", "current", "isNaN", "urls", "filter", "item", "indicator", "loop", "UUID_KEY", "deviceId", "useDeviceId", "random", "setStorage", "addSafeAreaInsets", "safeArea", "safeAreaInsets", "top", "left", "right", "bottom", "screenHeight", "populateParameters", "brand", "model", "system", "theme", "version", "fontSizeSetting", "SDKVersion", "deviceOrientation", "extraParam", "osName", "osVersion", "hostVersion", "deviceType", "getGetDeviceType", "deviceBrand", "getDevice<PERSON>rand", "_hostName", "getHostName", "_deviceOrientation", "_devicePixelRatio", "_SDKVersion", "hostLanguage", "parameters", "appId", "process", "appName", "appVersion", "appVersionCode", "appLanguage", "getAppLanguage", "uniCompileVersion", "uniCompilerVersion", "uniRuntimeVersion", "uniPlatform", "deviceModel", "devicePixelRatio", "toLocaleLowerCase", "hostTheme", "hostName", "hostSDKVersion", "hostFontSizeSetting", "windowTop", "windowBottom", "osLanguage", "undefined", "osTheme", "ua", "hostPackageName", "browserName", "browserVersion", "isUniAppX", "deviceTypeMaps", "ipad", "windows", "mac", "deviceTypeMapsKeys", "_model", "_m", "defaultLanguage", "_platform", "environment", "host", "env", "getSystemInfo", "showActionSheet", "alertText", "title", "UNI_COMPILER_VERSION", "getAppAuthorizeSetting", "locationReducedAccuracy", "locationAccuracy", "compressImage", "compressedHeight", "compressHeight", "compressedWidth", "compressWidth", "protocols", "getSystemInfoSync", "todos", "canIUses", "CALLBACKS", "processCallback", "methodName", "processReturnValue", "processArgs", "argsOption", "keepFromArgs", "<PERSON><PERSON><PERSON><PERSON>", "keyOption", "console", "warn", "keepReturnValue", "wrapper", "protocol", "arg1", "arg2", "apply", "todo<PERSON><PERSON>", "TODOS", "createTodoApi", "todoApi", "errMsg", "providers", "o<PERSON>h", "share", "payment", "get<PERSON><PERSON><PERSON>", "service", "provider", "extraApi", "getEmitter", "Emitter", "getUniEmitter", "ctx", "$on", "arguments", "$off", "$emit", "eventApi", "tryCatch", "e", "getApiCallbacks", "apiCallbacks", "param", "cid", "cidErrMsg", "enabled", "normalizePushMessage", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "invokeGetPushCidCallbacks", "onPushMessageCallbacks", "stopped", "getPushCidCallbacks", "getPushClientId", "hasSuccess", "hasFail", "hasComplete", "onPushMessage", "offPushMessage", "baseInfo", "shareVideoMessage", "miniapp", "mocks", "findVmByVueId", "vm", "vuePid", "$children", "childVm", "$scope", "_$vueId", "parentVm", "init<PERSON>eh<PERSON>or", "Behavior", "isPage", "route", "initRelation", "detail", "triggerEvent", "selectAllComponents", "mpInstance", "selector", "$refs", "components", "component", "ref", "dataset", "toSkip", "vueGeneric", "scopedComponent", "syncRefs", "refs", "newRefs", "oldKeys", "Set", "newKeys", "oldValue", "newValue", "every", "delete", "initRefs", "forComponents", "handleLink", "event", "vueOptions", "parent", "markMPComponent", "IS_MP", "configurable", "enumerable", "OB", "SKIP", "isExtensible", "WORKLET_RE", "initWorkletMethods", "mpMethods", "vueMethods", "matches", "match", "workletName", "MPPage", "Page", "MPComponent", "Component", "customizeRE", "customize", "initTriggerEvent", "oldTriggerEvent", "newTriggerEvent", "comType", "newEvent", "_triggerEvent", "initHook", "isComponent", "oldHook", "__$wrappered", "after", "PAGE_EVENT_HOOKS", "initMocks", "$mp", "mpType", "mock", "hasH<PERSON>", "default", "extendOptions", "super", "mixins", "initHooks", "mpOptions", "__call_hook", "initUnknownHooks", "excludes", "find<PERSON>ooks", "initHook$1", "initVueComponent", "VueComponent", "extend", "initSlots", "vueSlots", "$slots", "slotName", "$scopedSlots", "initVueIds", "vueIds", "_$vuePid", "initData", "context", "VUE_APP_DEBUG", "stringify", "__lifecycle_hooks__", "PROP_TYPES", "Boolean", "createObserver", "observer", "newVal", "oldVal", "initBehaviors", "vueBehaviors", "behaviors", "vueExtends", "extends", "vueMixins", "vueProps", "props", "behavior", "properties", "initProperties", "vueMixin", "parsePropType", "defaultValue", "file", "is<PERSON>eh<PERSON>or", "vueId", "virtualHost", "virtualHostStyle", "virtualHostClass", "scopedSlotsCompiler", "setData", "opts", "wrapper$1", "mp", "stopPropagation", "preventDefault", "markerId", "getExtraValue", "dataPathsArray", "dataPathArray", "dataPath", "prop<PERSON>ath", "valuePath", "vFor", "isInteger", "substr", "__get_value", "vForItem", "vForKey", "processEventExtra", "extra", "__args__", "extraObj", "getObjByArray", "arr", "element", "processEventArgs", "isCustom", "isCustomMPEvent", "currentTarget", "ret", "arg", "ONCE", "CUSTOM", "isMatchEventType", "eventType", "optType", "getContextVm", "$parent", "$options", "generic", "handleEvent", "eventOpts", "eventOpt", "eventsArray", "isOnce", "eventArray", "handlerCtx", "handler", "path", "is", "once", "eventChannels", "getEventChannel", "id", "eventChannel", "initEventChannel", "getOpenerEventChannel", "callHook", "__id__", "__eventChannel__", "initScopedSlotsParams", "center", "parents", "currentId", "propsData", "$hasSSP", "slot", "$getSSP", "needAll", "$setSSP", "$initSSP", "$callSSP", "destroyed", "parseBaseApp", "store", "$store", "mpHost", "$i18n", "_i18n", "appOptions", "onLaunch", "canIUse", "globalData", "_isMounted", "parseApp", "createApp", "App", "encodeReserveRE", "encodeReserveReplacer", "commaRE", "encode", "encodeURIComponent", "stringifyQuery", "encodeStr", "val", "val2", "x", "parseBaseComponent", "vueComponentOptions", "needVueOptions", "multipleSlots", "addGlobalClass", "componentOptions", "__file", "lifetimes", "attached", "$mount", "ready", "detached", "$destroy", "pageLifetimes", "show", "hide", "resize", "size", "__l", "__e", "externalClasses", "wxsCallMethods", "callMethod", "parseComponent", "hooks$1", "parseBasePage", "vuePageOptions", "pageOptions", "onLoad", "query", "copyQuery", "parsePage", "createPage", "createComponent", "createSubpackageApp", "onShow", "onAppShow", "onHide", "onAppHide", "createPlugin", "canIUseApi", "apiName", "uni", "Proxy", "uni$1", "_interopRequireDefault", "__esModule", "module", "exports", "arrayWithHoles", "require", "iterableToArrayLimit", "unsupportedIterableToArray", "nonIterableRest", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "r", "l", "Symbol", "iterator", "n", "u", "a", "f", "o", "next", "done", "arrayLikeToArray", "_unsupportedIterableToArray", "minLen", "from", "_arrayLikeToArray", "arr2", "_nonIterableRest", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "writable", "_typeof", "toPrimitive", "setPrototypeOf", "isNativeReflectConstruct", "_construct", "Reflect", "construct", "p", "bind", "_setPrototypeOf", "_isNativeReflectConstruct", "valueOf", "arrayWithoutHoles", "iterableToArray", "nonIterableSpread", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "iter", "_nonIterableSpread", "defaultDelimiters", "BaseFormatter", "_caches", "delimiters", "tokens", "compile", "RE_TOKEN_LIST_VALUE", "RE_TOKEN_NAMED_VALUE", "format", "startDelimiter", "endDelimiter", "position", "text", "char", "sub", "isClosed", "compiled", "mode", "defaultFormatter", "I18n", "fallback<PERSON><PERSON><PERSON>", "watcher", "formater", "watchers", "override", "interpolate", "watchAppLocale", "newLocale", "$watch", "getDefaultLocale", "isWatchedAppLocale", "add", "isString", "hasI18nJson", "jsonObj", "walkJsonObj", "isI18nStr", "parseI18nJson", "compileStr", "compileI18nJsonStr", "jsonStr", "localeValues", "unshift", "compileJsonObj", "compileValue", "valueLocales", "localValue", "walk", "resolveLocale", "resolveLocaleChain", "chain", "pop", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "descriptor", "_createClass", "protoProps", "staticProps", "getToken", "setToken", "setStorageSync", "log", "removeToken", "removeStorageSync", "getUserInfo", "setUserInfo", "isLoggedIn", "checkLogin", "showToast", "icon", "duration", "setTimeout", "navigateTo", "logout", "showModal", "content", "confirm", "reLaunch", "loginCheckMixin", "checkLoginStatus", "handleLogout", "protectedPages", "isProtectedPage", "pagePath", "some", "guardDisabled", "disable<PERSON><PERSON>", "setupNavigationGuard", "originalNavigateTo", "originalSwitchTab", "switchTab", "originalReLaunch", "originalRedirectTo", "autoLoginCheck", "runtime", "_regeneratorRuntime", "asyncIterator", "toStringTag", "define", "wrap", "Generator", "Context", "makeInvokeMethod", "h", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "d", "getPrototypeOf", "g", "defineIteratorMethods", "_invoke", "AsyncIterator", "__await", "callInvokeWithMethodAndArg", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "resultName", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "displayName", "isGeneratorFunction", "mark", "awrap", "async", "reverse", "prev", "stop", "rval", "handle", "finish", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "info", "_asyncToGenerator", "self", "signInApi", "getSetting", "http", "checkSignInToday", "tokenName", "getHistory", "pageNum", "pageSize", "doSignIn", "post", "header", "tokenApi", "<PERSON><PERSON><PERSON><PERSON>", "getTokenInfo", "refresh<PERSON><PERSON><PERSON>", "addToken", "tokenTime", "put", "getUserToken", "utils", "base64Encode", "customBase64Encode", "base64Decode", "customBase64Decode", "simpleEncode", "hex", "padStart", "simpleDecode", "hexStr", "charCode", "encrypt", "iv", "dataStr", "keyStr", "encrypted", "dataChar", "keyChar", "encryptedChar", "fallback<PERSON><PERSON>r", "decrypt", "encryptedData", "decoded", "base64Error", "decrypted", "decryptedChar", "getCurrentTimeString", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "copyText", "setClipboardData", "formatDate", "date", "BASE_URL", "config", "baseURL", "request", "showLoading", "mask", "hideLoading", "errorMsg", "showError", "showCancel", "requestConfig", "timeout", "formData", "statusCode", "code", "msg", "development", "debug", "production", "getConfig", "stringToUtf8Bytes", "bytes", "hi", "lo", "utf8BytesToString", "byte1", "byte2", "byte3", "byte4", "codePoint", "base64", "Base64", "decode", "base64Chars", "utf8Bytes", "b", "a1", "a2", "a3", "a4", "originalStr", "hasPadding1", "hasPadding2", "loginApi", "username", "password", "uuid", "isToken", "repeatSubmit", "register", "getCodeImg", "loginInfo", "sendSmsCode", "phone", "sendPhoneCode", "phoneLogin", "phoneNumber", "smsCode"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAMA,UAAU,GAAG,CACjB,IAAI,EACJ,KAAK,EACL,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,eAAe,EACf,QAAQ,EACR,SAAS,EACT,mCAAmC,CACpC;AACD,IAAMC,oBAAoB,GAAG,CAC3B,UAAU,EACV,QAAQ,EACR,SAAS,CACV;AACD,IAAMC,MAAM,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAI,YAAY;EAC3E,OAAO,IAAI;AACb,CAAC,EAAG;AAEJ,IAAMC,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAC/B,IAAMC,KAAK,GAAGJ,MAAM,CAACE,GAAG,CAAC;AACzB,IAAMG,YAAY,GAAGD,KAAK,CAACE,oBAAoB,GAAGF,KAAK,CAACE,oBAAoB,EAAE,GAAG,IAAI;AAErF,SAASC,OAAO,CAAEL,GAAG,EAAE;EACrB,IAAIG,YAAY,IAAIA,YAAY,CAACG,KAAK,KAAK,IAAI,IAAIT,oBAAoB,CAACU,QAAQ,CAACP,GAAG,CAAC,EAAE;IACrF,OAAO,KAAK;EACd;EACA,OAAOJ,UAAU,CAACY,OAAO,CAACR,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,OAAOE,KAAK,CAACF,GAAG,CAAC,KAAK,UAAU;AACzE;AAEA,SAASS,MAAM,GAAI;EACjB,IAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,KAAK,IAAMV,IAAG,IAAIE,KAAK,EAAE;IACvB,IAAIG,OAAO,CAACL,IAAG,CAAC,EAAE;MAChB;MACAU,KAAK,CAACV,IAAG,CAAC,GAAGE,KAAK,CAACF,IAAG,CAAC;IACzB;EACF;EACA,OAAOU,KAAK;AACd;AACAZ,MAAM,CAACE,GAAG,CAAC,GAAGS,MAAM,EAAE;AAAA,eACPX,MAAM,CAACE,GAAG,CAAC;AAAA,2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3C1B;AACA;AAAsB;AAAA;AAEtB,IAAIW,QAAQ;AAEZ,IAAMC,GAAG,GAAG,mEAAmE;AAC/E,IAAMC,KAAK,GAAG,sEAAsE;AAEpF,IAAI,OAAOC,IAAI,KAAK,UAAU,EAAE;EAC9BH,QAAQ,GAAG,kBAAUI,GAAG,EAAE;IACxBA,GAAG,GAAGC,MAAM,CAACD,GAAG,CAAC,CAACE,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IAC9C,IAAI,CAACJ,KAAK,CAACK,IAAI,CAACH,GAAG,CAAC,EAAE;MAAE,MAAM,IAAII,KAAK,CAAC,0FAA0F,CAAC;IAAC;;IAEpI;IACAJ,GAAG,IAAI,IAAI,CAACK,KAAK,CAAC,CAAC,IAAIL,GAAG,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,IAAIC,MAAM;IAAE,IAAIC,MAAM,GAAG,EAAE;IAAE,IAAIC,EAAE;IAAE,IAAIC,EAAE;IAAE,IAAIC,CAAC,GAAG,CAAC;IACtD,OAAOA,CAAC,GAAGX,GAAG,CAACM,MAAM,GAAG;MACtBC,MAAM,GAAGV,GAAG,CAACJ,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAGd,GAAG,CAACJ,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAClE,CAACF,EAAE,GAAGZ,GAAG,CAACJ,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAID,EAAE,GAAGb,GAAG,CAACJ,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC,CAAC;MAE5FH,MAAM,IAAIC,EAAE,KAAK,EAAE,GAAGR,MAAM,CAACY,YAAY,CAACN,MAAM,IAAI,EAAE,GAAG,GAAG,CAAC,GACzDG,EAAE,KAAK,EAAE,GAAGT,MAAM,CAACY,YAAY,CAACN,MAAM,IAAI,EAAE,GAAG,GAAG,EAAEA,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,GACpEN,MAAM,CAACY,YAAY,CAACN,MAAM,IAAI,EAAE,GAAG,GAAG,EAAEA,MAAM,IAAI,CAAC,GAAG,GAAG,EAAEA,MAAM,GAAG,GAAG,CAAC;IAChF;IACA,OAAOC,MAAM;EACf,CAAC;AACH,CAAC,MAAM;EACL;EACAZ,QAAQ,GAAGG,IAAI;AACjB;AAEA,SAASe,gBAAgB,CAAEd,GAAG,EAAE;EAC9B,OAAOe,kBAAkB,CAACnB,QAAQ,CAACI,GAAG,CAAC,CAACgB,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;IACjE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEf,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,CAACnB,IAAI,CAAC,EAAE,CAAC,CAAC;AACd;AAEA,SAASmC,kBAAkB,GAAI;EAC7B,IAAMC,KAAK,GAAKC,EAAE,CAAEC,cAAc,CAAC,cAAc,CAAC,IAAI,EAAE;EACxD,IAAMC,QAAQ,GAAGH,KAAK,CAACN,KAAK,CAAC,GAAG,CAAC;EACjC,IAAI,CAACM,KAAK,IAAIG,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO;MACLoB,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,IAAIC,QAAQ;EACZ,IAAI;IACFA,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAClB,gBAAgB,CAACW,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACd,MAAM,IAAI7B,KAAK,CAAC,qBAAqB,GAAG6B,KAAK,CAACC,OAAO,CAAC;EACxD;EACAJ,QAAQ,CAACD,YAAY,GAAGC,QAAQ,CAACK,GAAG,GAAG,IAAI;EAC3C,OAAOL,QAAQ,CAACK,GAAG;EACnB,OAAOL,QAAQ,CAACM,GAAG;EACnB,OAAON,QAAQ;AACjB;AAEA,SAASO,UAAU,CAAEC,GAAG,EAAE;EACxBA,GAAG,CAACC,SAAS,CAACC,YAAY,GAAG,UAAUC,MAAM,EAAE;IAC7C,0BAEIpB,kBAAkB,EAAE;MADtBM,IAAI,uBAAJA,IAAI;IAEN,OAAOA,IAAI,CAAClC,OAAO,CAACgD,MAAM,CAAC,GAAG,CAAC,CAAC;EAClC,CAAC;EACDH,GAAG,CAACC,SAAS,CAACG,kBAAkB,GAAG,UAAUC,YAAY,EAAE;IACzD,2BAEItB,kBAAkB,EAAE;MADtBO,UAAU,wBAAVA,UAAU;IAEZ,OAAO,IAAI,CAACY,YAAY,CAAC,OAAO,CAAC,IAAIZ,UAAU,CAACnC,OAAO,CAACkD,YAAY,CAAC,GAAG,CAAC,CAAC;EAC5E,CAAC;EACDL,GAAG,CAACC,SAAS,CAACK,eAAe,GAAG,YAAY;IAC1C,2BAEIvB,kBAAkB,EAAE;MADtBQ,YAAY,wBAAZA,YAAY;IAEd,OAAOA,YAAY,GAAGgB,IAAI,CAACC,GAAG,EAAE;EAClC,CAAC;AACH;AAEA,IAAMC,SAAS,GAAGC,MAAM,CAACT,SAAS,CAACnB,QAAQ;AAC3C,IAAM6B,cAAc,GAAGD,MAAM,CAACT,SAAS,CAACU,cAAc;AAEtD,SAASC,IAAI,CAAEC,EAAE,EAAE;EACjB,OAAO,OAAOA,EAAE,KAAK,UAAU;AACjC;AAEA,SAASC,KAAK,CAAEpD,GAAG,EAAE;EACnB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;AAEA,SAASqD,QAAQ,CAAEC,GAAG,EAAE;EACtB,OAAOA,GAAG,KAAK,IAAI,IAAI,sBAAOA,GAAG,MAAK,QAAQ;AAChD;AAEA,SAASC,aAAa,CAAED,GAAG,EAAE;EAC3B,OAAOP,SAAS,CAACS,IAAI,CAACF,GAAG,CAAC,KAAK,iBAAiB;AAClD;AAEA,SAASG,MAAM,CAAEH,GAAG,EAAErE,GAAG,EAAE;EACzB,OAAOgE,cAAc,CAACO,IAAI,CAACF,GAAG,EAAErE,GAAG,CAAC;AACtC;AAEA,SAASyE,IAAI,GAAI,CAAC;;AAElB;AACA;AACA;AACA,SAASC,MAAM,CAAER,EAAE,EAAE;EACnB,IAAMS,KAAK,GAAGZ,MAAM,CAACa,MAAM,CAAC,IAAI,CAAC;EACjC,OAAO,SAASC,QAAQ,CAAE9D,GAAG,EAAE;IAC7B,IAAM+D,GAAG,GAAGH,KAAK,CAAC5D,GAAG,CAAC;IACtB,OAAO+D,GAAG,KAAKH,KAAK,CAAC5D,GAAG,CAAC,GAAGmD,EAAE,CAACnD,GAAG,CAAC,CAAC;EACtC,CAAC;AACH;;AAEA;AACA;AACA;AACA,IAAMgE,UAAU,GAAG,QAAQ;AAC3B,IAAMC,QAAQ,GAAGN,MAAM,CAAC,UAAC3D,GAAG,EAAK;EAC/B,OAAOA,GAAG,CAACE,OAAO,CAAC8D,UAAU,EAAE,UAACE,CAAC,EAAEhD,CAAC;IAAA,OAAKA,CAAC,GAAGA,CAAC,CAACiD,WAAW,EAAE,GAAG,EAAE;EAAA,EAAC;AACpE,CAAC,CAAC;AAEF,SAASC,UAAU,CAAEd,GAAG,EAAE;EACxB,IAAMe,OAAO,GAAG,CAAC,CAAC;EAClB,IAAId,aAAa,CAACD,GAAG,CAAC,EAAE;IACtBN,MAAM,CAACsB,IAAI,CAAChB,GAAG,CAAC,CAACiB,IAAI,EAAE,CAACC,OAAO,CAAC,UAAAvF,GAAG,EAAI;MACrCoF,OAAO,CAACpF,GAAG,CAAC,GAAGqE,GAAG,CAACrE,GAAG,CAAC;IACzB,CAAC,CAAC;EACJ;EACA,OAAO,CAAC+D,MAAM,CAACsB,IAAI,CAACD,OAAO,CAAC,GAAGf,GAAG,GAAGe,OAAO;AAC9C;AAEA,IAAMI,KAAK,GAAG,CACZ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,UAAU,EACV,aAAa,CACd;AAED,IAAMC,kBAAkB,GAAG,CAAC,CAAC;AAC7B,IAAMC,kBAAkB,GAAG,CAAC,CAAC;AAE7B,SAASC,SAAS,CAAEC,SAAS,EAAEC,QAAQ,EAAE;EACvC,IAAMC,GAAG,GAAGD,QAAQ,GAChBD,SAAS,GACPA,SAAS,CAACG,MAAM,CAACF,QAAQ,CAAC,GAC1BG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,GACrBA,QAAQ,GAAG,CAACA,QAAQ,CAAC,GACzBD,SAAS;EACb,OAAOE,GAAG,GACNI,WAAW,CAACJ,GAAG,CAAC,GAChBA,GAAG;AACT;AAEA,SAASI,WAAW,CAAEC,KAAK,EAAE;EAC3B,IAAML,GAAG,GAAG,EAAE;EACd,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyE,KAAK,CAAC9E,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrC,IAAIoE,GAAG,CAACtF,OAAO,CAAC2F,KAAK,CAACzE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAChCoE,GAAG,CAACM,IAAI,CAACD,KAAK,CAACzE,CAAC,CAAC,CAAC;IACpB;EACF;EACA,OAAOoE,GAAG;AACZ;AAEA,SAASO,UAAU,CAAEF,KAAK,EAAEG,IAAI,EAAE;EAChC,IAAMC,KAAK,GAAGJ,KAAK,CAAC3F,OAAO,CAAC8F,IAAI,CAAC;EACjC,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBJ,KAAK,CAACK,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EACxB;AACF;AAEA,SAASE,oBAAoB,CAAEC,WAAW,EAAEC,MAAM,EAAE;EAClD5C,MAAM,CAACsB,IAAI,CAACsB,MAAM,CAAC,CAACpB,OAAO,CAAC,UAAAe,IAAI,EAAI;IAClC,IAAId,KAAK,CAAChF,OAAO,CAAC8F,IAAI,CAAC,KAAK,CAAC,CAAC,IAAIrC,IAAI,CAAC0C,MAAM,CAACL,IAAI,CAAC,CAAC,EAAE;MACpDI,WAAW,CAACJ,IAAI,CAAC,GAAGX,SAAS,CAACe,WAAW,CAACJ,IAAI,CAAC,EAAEK,MAAM,CAACL,IAAI,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;AACJ;AAEA,SAASM,qBAAqB,CAAEF,WAAW,EAAEC,MAAM,EAAE;EACnD,IAAI,CAACD,WAAW,IAAI,CAACC,MAAM,EAAE;IAC3B;EACF;EACA5C,MAAM,CAACsB,IAAI,CAACsB,MAAM,CAAC,CAACpB,OAAO,CAAC,UAAAe,IAAI,EAAI;IAClC,IAAId,KAAK,CAAChF,OAAO,CAAC8F,IAAI,CAAC,KAAK,CAAC,CAAC,IAAIrC,IAAI,CAAC0C,MAAM,CAACL,IAAI,CAAC,CAAC,EAAE;MACpDD,UAAU,CAACK,WAAW,CAACJ,IAAI,CAAC,EAAEK,MAAM,CAACL,IAAI,CAAC,CAAC;IAC7C;EACF,CAAC,CAAC;AACJ;AAEA,SAASO,cAAc,CAAEC,MAAM,EAAEH,MAAM,EAAE;EACvC,IAAI,OAAOG,MAAM,KAAK,QAAQ,IAAIxC,aAAa,CAACqC,MAAM,CAAC,EAAE;IACvDF,oBAAoB,CAACf,kBAAkB,CAACoB,MAAM,CAAC,KAAKpB,kBAAkB,CAACoB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EAC/F,CAAC,MAAM,IAAIrC,aAAa,CAACwC,MAAM,CAAC,EAAE;IAChCL,oBAAoB,CAAChB,kBAAkB,EAAEqB,MAAM,CAAC;EAClD;AACF;AAEA,SAASC,iBAAiB,CAAED,MAAM,EAAEH,MAAM,EAAE;EAC1C,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAIxC,aAAa,CAACqC,MAAM,CAAC,EAAE;MACzBC,qBAAqB,CAAClB,kBAAkB,CAACoB,MAAM,CAAC,EAAEH,MAAM,CAAC;IAC3D,CAAC,MAAM;MACL,OAAOjB,kBAAkB,CAACoB,MAAM,CAAC;IACnC;EACF,CAAC,MAAM,IAAIxC,aAAa,CAACwC,MAAM,CAAC,EAAE;IAChCF,qBAAqB,CAACnB,kBAAkB,EAAEqB,MAAM,CAAC;EACnD;AACF;AAEA,SAASE,WAAW,CAAEV,IAAI,EAAEW,MAAM,EAAE;EAClC,OAAO,UAAUC,IAAI,EAAE;IACrB,OAAOZ,IAAI,CAACY,IAAI,EAAED,MAAM,CAAC,IAAIC,IAAI;EACnC,CAAC;AACH;AAEA,SAASC,SAAS,CAAE9C,GAAG,EAAE;EACvB,OAAO,CAAC,CAACA,GAAG,KAAK,sBAAOA,GAAG,MAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOA,GAAG,CAAC+C,IAAI,KAAK,UAAU;AAC1G;AAEA,SAASC,KAAK,CAAElB,KAAK,EAAEe,IAAI,EAAED,MAAM,EAAE;EACnC,IAAIK,OAAO,GAAG,KAAK;EACnB,KAAK,IAAI5F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyE,KAAK,CAAC9E,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrC,IAAM4E,IAAI,GAAGH,KAAK,CAACzE,CAAC,CAAC;IACrB,IAAI4F,OAAO,EAAE;MACXA,OAAO,GAAGC,OAAO,CAACC,OAAO,CAACR,WAAW,CAACV,IAAI,EAAEW,MAAM,CAAC,CAAC;IACtD,CAAC,MAAM;MACL,IAAMnB,GAAG,GAAGQ,IAAI,CAACY,IAAI,EAAED,MAAM,CAAC;MAC9B,IAAIE,SAAS,CAACrB,GAAG,CAAC,EAAE;QAClBwB,OAAO,GAAGC,OAAO,CAACC,OAAO,CAAC1B,GAAG,CAAC;MAChC;MACA,IAAIA,GAAG,KAAK,KAAK,EAAE;QACjB,OAAO;UACLsB,IAAI,kBAAI,CAAE;QACZ,CAAC;MACH;IACF;EACF;EACA,OAAOE,OAAO,IAAI;IAChBF,IAAI,gBAAEK,QAAQ,EAAE;MACd,OAAOA,QAAQ,CAACP,IAAI,CAAC;IACvB;EACF,CAAC;AACH;AAEA,SAASQ,cAAc,CAAEhB,WAAW,EAAgB;EAAA,IAAdiB,OAAO,uEAAG,CAAC,CAAC;EAChD,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAACpC,OAAO,CAAC,UAAAqC,IAAI,EAAI;IAC9C,IAAI5B,KAAK,CAACC,OAAO,CAACS,WAAW,CAACkB,IAAI,CAAC,CAAC,EAAE;MACpC,IAAMC,WAAW,GAAGF,OAAO,CAACC,IAAI,CAAC;MACjCD,OAAO,CAACC,IAAI,CAAC,GAAG,SAASE,mBAAmB,CAAEhC,GAAG,EAAE;QACjDuB,KAAK,CAACX,WAAW,CAACkB,IAAI,CAAC,EAAE9B,GAAG,EAAE6B,OAAO,CAAC,CAACP,IAAI,CAAC,UAACtB,GAAG,EAAK;UACnD;UACA,OAAO7B,IAAI,CAAC4D,WAAW,CAAC,IAAIA,WAAW,CAAC/B,GAAG,CAAC,IAAIA,GAAG;QACrD,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO6B,OAAO;AAChB;AAEA,SAASI,kBAAkB,CAAEjB,MAAM,EAAEkB,WAAW,EAAE;EAChD,IAAMC,gBAAgB,GAAG,EAAE;EAC3B,IAAIjC,KAAK,CAACC,OAAO,CAACR,kBAAkB,CAACuC,WAAW,CAAC,EAAE;IACjDC,gBAAgB,CAAC7B,IAAI,OAArB6B,gBAAgB,mCAASxC,kBAAkB,CAACuC,WAAW,EAAC;EAC1D;EACA,IAAMtB,WAAW,GAAGhB,kBAAkB,CAACoB,MAAM,CAAC;EAC9C,IAAIJ,WAAW,IAAIV,KAAK,CAACC,OAAO,CAACS,WAAW,CAACsB,WAAW,CAAC,EAAE;IACzDC,gBAAgB,CAAC7B,IAAI,OAArB6B,gBAAgB,mCAASvB,WAAW,CAACsB,WAAW,EAAC;EACnD;EACAC,gBAAgB,CAAC1C,OAAO,CAAC,UAAAe,IAAI,EAAI;IAC/B0B,WAAW,GAAG1B,IAAI,CAAC0B,WAAW,CAAC,IAAIA,WAAW;EAChD,CAAC,CAAC;EACF,OAAOA,WAAW;AACpB;AAEA,SAASE,sBAAsB,CAAEpB,MAAM,EAAE;EACvC,IAAMJ,WAAW,GAAG3C,MAAM,CAACa,MAAM,CAAC,IAAI,CAAC;EACvCb,MAAM,CAACsB,IAAI,CAACI,kBAAkB,CAAC,CAACF,OAAO,CAAC,UAAAe,IAAI,EAAI;IAC9C,IAAIA,IAAI,KAAK,aAAa,EAAE;MAC1BI,WAAW,CAACJ,IAAI,CAAC,GAAGb,kBAAkB,CAACa,IAAI,CAAC,CAAClF,KAAK,EAAE;IACtD;EACF,CAAC,CAAC;EACF,IAAM+G,iBAAiB,GAAGzC,kBAAkB,CAACoB,MAAM,CAAC;EACpD,IAAIqB,iBAAiB,EAAE;IACrBpE,MAAM,CAACsB,IAAI,CAAC8C,iBAAiB,CAAC,CAAC5C,OAAO,CAAC,UAAAe,IAAI,EAAI;MAC7C,IAAIA,IAAI,KAAK,aAAa,EAAE;QAC1BI,WAAW,CAACJ,IAAI,CAAC,GAAG,CAACI,WAAW,CAACJ,IAAI,CAAC,IAAI,EAAE,EAAEP,MAAM,CAACoC,iBAAiB,CAAC7B,IAAI,CAAC,CAAC;MAC/E;IACF,CAAC,CAAC;EACJ;EACA,OAAOI,WAAW;AACpB;AAEA,SAAS0B,SAAS,CAAEtB,MAAM,EAAEuB,GAAG,EAAEV,OAAO,EAAa;EAAA,kCAARV,MAAM;IAANA,MAAM;EAAA;EACjD,IAAMP,WAAW,GAAGwB,sBAAsB,CAACpB,MAAM,CAAC;EAClD,IAAIJ,WAAW,IAAI3C,MAAM,CAACsB,IAAI,CAACqB,WAAW,CAAC,CAACrF,MAAM,EAAE;IAClD,IAAI2E,KAAK,CAACC,OAAO,CAACS,WAAW,CAAC4B,MAAM,CAAC,EAAE;MACrC,IAAMxC,GAAG,GAAGuB,KAAK,CAACX,WAAW,CAAC4B,MAAM,EAAEX,OAAO,CAAC;MAC9C,OAAO7B,GAAG,CAACsB,IAAI,CAAC,UAACO,OAAO,EAAK;QAC3B;QACA,OAAOU,GAAG,gBACRX,cAAc,CAACQ,sBAAsB,CAACpB,MAAM,CAAC,EAAEa,OAAO,CAAC,SACpDV,MAAM,EACV;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOoB,GAAG,gBAACX,cAAc,CAAChB,WAAW,EAAEiB,OAAO,CAAC,SAAKV,MAAM,EAAC;IAC7D;EACF;EACA,OAAOoB,GAAG,gBAACV,OAAO,SAAKV,MAAM,EAAC;AAChC;AAEA,IAAMsB,kBAAkB,GAAG;EACzBP,WAAW,uBAAElC,GAAG,EAAE;IAChB,IAAI,CAACqB,SAAS,CAACrB,GAAG,CAAC,EAAE;MACnB,OAAOA,GAAG;IACZ;IACA,OAAO,IAAIyB,OAAO,CAAC,UAACC,OAAO,EAAEgB,MAAM,EAAK;MACtC1C,GAAG,CAACsB,IAAI,CAAC,UAAAtB,GAAG,EAAI;QACd,IAAI,CAACA,GAAG,EAAE;UACR0B,OAAO,CAAC1B,GAAG,CAAC;UACZ;QACF;QACA,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE;UACV0C,MAAM,CAAC1C,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UACL0B,OAAO,CAAC1B,GAAG,CAAC,CAAC,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAM2C,WAAW,GACf,scAAsc;AAExc,IAAMC,cAAc,GAAG,kBAAkB;;AAEzC;AACA,IAAMC,kBAAkB,GAAG,CAAC,qBAAqB,CAAC;;AAElD;AACA,IAAMC,SAAS,GAAG,CAAC,qBAAqB,EAAE,mBAAmB,CAAC;AAE9D,IAAMC,eAAe,GAAG,UAAU;AAElC,SAASC,YAAY,CAAElB,IAAI,EAAE;EAC3B,OAAOc,cAAc,CAACxH,IAAI,CAAC0G,IAAI,CAAC,IAAIe,kBAAkB,CAACnI,OAAO,CAACoH,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7E;AACA,SAASmB,SAAS,CAAEnB,IAAI,EAAE;EACxB,OAAOa,WAAW,CAACvH,IAAI,CAAC0G,IAAI,CAAC,IAAIgB,SAAS,CAACpI,OAAO,CAACoH,IAAI,CAAC,KAAK,CAAC,CAAC;AACjE;AAEA,SAASoB,aAAa,CAAEpB,IAAI,EAAE;EAC5B,OAAOiB,eAAe,CAAC3H,IAAI,CAAC0G,IAAI,CAAC,IAAIA,IAAI,KAAK,QAAQ;AACxD;AAEA,SAASqB,aAAa,CAAE3B,OAAO,EAAE;EAC/B,OAAOA,OAAO,CAACF,IAAI,CAAC,UAAAF,IAAI,EAAI;IAC1B,OAAO,CAAC,IAAI,EAAEA,IAAI,CAAC;EACrB,CAAC,CAAC,CACCgC,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAI,CAACA,GAAG,CAAC;EAAA,EAAC;AACxB;AAEA,SAASC,aAAa,CAAExB,IAAI,EAAE;EAC5B,IACEkB,YAAY,CAAClB,IAAI,CAAC,IAClBmB,SAAS,CAACnB,IAAI,CAAC,IACfoB,aAAa,CAACpB,IAAI,CAAC,EACnB;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;;AAEA;AACA,IAAI,CAACL,OAAO,CAACjE,SAAS,CAAC+F,OAAO,EAAE;EAC9B9B,OAAO,CAACjE,SAAS,CAAC+F,OAAO,GAAG,UAAU5B,QAAQ,EAAE;IAC9C,IAAMH,OAAO,GAAG,IAAI,CAACgC,WAAW;IAChC,OAAO,IAAI,CAAClC,IAAI,CACd,UAAAmC,KAAK;MAAA,OAAIjC,OAAO,CAACE,OAAO,CAACC,QAAQ,EAAE,CAAC,CAACL,IAAI,CAAC;QAAA,OAAMmC,KAAK;MAAA,EAAC;IAAA,GACtD,UAAAC,MAAM;MAAA,OAAIlC,OAAO,CAACE,OAAO,CAACC,QAAQ,EAAE,CAAC,CAACL,IAAI,CAAC,YAAM;QAC/C,MAAMoC,MAAM;MACd,CAAC,CAAC;IAAA,EACH;EACH,CAAC;AACH;AAEA,SAASC,SAAS,CAAE7B,IAAI,EAAES,GAAG,EAAE;EAC7B,IAAI,CAACe,aAAa,CAACxB,IAAI,CAAC,IAAI,CAAC3D,IAAI,CAACoE,GAAG,CAAC,EAAE;IACtC,OAAOA,GAAG;EACZ;EACA,OAAO,SAASqB,UAAU,GAA2B;IAAA,IAAzB/B,OAAO,uEAAG,CAAC,CAAC;IAAA,mCAAKV,MAAM;MAANA,MAAM;IAAA;IACjD,IAAIhD,IAAI,CAAC0D,OAAO,CAACgC,OAAO,CAAC,IAAI1F,IAAI,CAAC0D,OAAO,CAACiC,IAAI,CAAC,IAAI3F,IAAI,CAAC0D,OAAO,CAACkC,QAAQ,CAAC,EAAE;MACzE,OAAO9B,kBAAkB,CAACH,IAAI,EAAEQ,SAAS,gBAACR,IAAI,EAAES,GAAG,EAAEV,OAAO,SAAKV,MAAM,EAAC,CAAC;IAC3E;IACA,OAAOc,kBAAkB,CAACH,IAAI,EAAEqB,aAAa,CAAC,IAAI1B,OAAO,CAAC,UAACC,OAAO,EAAEgB,MAAM,EAAK;MAC7EJ,SAAS,gBAACR,IAAI,EAAES,GAAG,EAAEtE,MAAM,CAAC+F,MAAM,CAAC,CAAC,CAAC,EAAEnC,OAAO,EAAE;QAC9CgC,OAAO,EAAEnC,OAAO;QAChBoC,IAAI,EAAEpB;MACR,CAAC,CAAC,SAAKvB,MAAM,EAAC;IAChB,CAAC,CAAC,CAAC,CAAC;EACN,CAAC;AACH;AAEA,IAAM8C,GAAG,GAAG,IAAI;AAChB,IAAMC,iBAAiB,GAAG,GAAG;AAC7B,IAAIC,KAAK,GAAG,KAAK;AACjB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,SAAS,GAAG,CAAC;AAEjB,SAASC,gBAAgB,GAAI;EAC3B,qBAA+CrG,MAAM,CAAC+F,MAAM,CAAC,CAAC,CAAC,EAAExH,EAAE,CAAC+H,aAAa,EAAE,EAAE;MACjFC,QAAQ,EAAEhI,EAAE,CAACiI,aAAa,EAAE,CAACD;IAC/B,CAAC,CAAC;IAFIE,WAAW,kBAAXA,WAAW;IAAEC,UAAU,kBAAVA,UAAU;IAAEH,QAAQ,kBAARA,QAAQ,CAGtC,CAAC;;EAEJJ,WAAW,GAAGM,WAAW;EACzBL,SAAS,GAAGM,UAAU;EACtBR,KAAK,GAAGK,QAAQ,KAAK,KAAK;AAC5B;AAEA,SAASI,MAAM,CAAEC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIV,WAAW,KAAK,CAAC,EAAE;IACrBE,gBAAgB,EAAE;EACpB;EAEAO,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC;EACvB,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,OAAO,CAAC;EACV;EACA,IAAIpJ,MAAM,GAAIoJ,MAAM,GAAGX,iBAAiB,IAAKY,cAAc,IAAIV,WAAW,CAAC;EAC3E,IAAI3I,MAAM,GAAG,CAAC,EAAE;IACdA,MAAM,GAAG,CAACA,MAAM;EAClB;EACAA,MAAM,GAAGuJ,IAAI,CAACC,KAAK,CAACxJ,MAAM,GAAGwI,GAAG,CAAC;EACjC,IAAIxI,MAAM,KAAK,CAAC,EAAE;IAChB,IAAI4I,SAAS,KAAK,CAAC,IAAI,CAACF,KAAK,EAAE;MAC7B1I,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,GAAG;IACd;EACF;EACA,OAAOoJ,MAAM,GAAG,CAAC,GAAG,CAACpJ,MAAM,GAAGA,MAAM;AACtC;AAEA,IAAMyJ,cAAc,GAAG,SAAS;AAChC,IAAMC,cAAc,GAAG,SAAS;AAChC,IAAMC,SAAS,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAG,IAAI;AAEtB,IAAMC,QAAQ,GAAG,CAAC,CAAC;AAEnB,IAAIC,MAAM;AAEV;EACEA,MAAM,GAAGC,eAAe,CAAEjJ,EAAE,CAACkJ,cAAc,EAAE,CAACC,QAAQ,CAAE,IAAIP,SAAS;AACvE;AAEA,SAASQ,gBAAgB,GAAI;EAC3B,IAAI,CAACC,cAAc,EAAE,EAAE;IACrB;EACF;EACA,IAAMC,UAAU,GAAG7H,MAAM,CAACsB,IAAI,CAACwG,WAAW,CAACC,OAAO,CAAC;EACnD,IAAIF,UAAU,CAACvK,MAAM,EAAE;IACrBuK,UAAU,CAACrG,OAAO,CAAC,UAAC+F,MAAM,EAAK;MAC7B,IAAMS,WAAW,GAAGV,QAAQ,CAACC,MAAM,CAAC;MACpC,IAAMU,YAAY,GAAGH,WAAW,CAACC,OAAO,CAACR,MAAM,CAAC;MAChD,IAAIS,WAAW,EAAE;QACfhI,MAAM,CAAC+F,MAAM,CAACiC,WAAW,EAAEC,YAAY,CAAC;MAC1C,CAAC,MAAM;QACLX,QAAQ,CAACC,MAAM,CAAC,GAAGU,YAAY;MACjC;IACF,CAAC,CAAC;EACJ;AACF;AAEAN,gBAAgB,EAAE;AAElB,IAAMO,IAAI,GAAG,IAAAC,oBAAW,EACtBZ,MAAM,EACL,CAAC,CAAC,CACJ;AACD,IAAMa,CAAC,GAAGF,IAAI,CAACE,CAAC;AAChB,IAAMC,SAAS,GAAIH,IAAI,CAACI,KAAK,GAAG;EAC9BC,YAAY,0BAAI;IAAA;IACd,IAAMC,OAAO,GAAGN,IAAI,CAACA,IAAI,CAACO,WAAW,CAAC,YAAM;MAC1C,KAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC;IACF,IAAI,CAACC,KAAK,CAAC,oBAAoB,EAAE,YAAY;MAC3CH,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDI,OAAO,EAAE;IACPC,GAAG,eAAE5M,GAAG,EAAE6M,MAAM,EAAE;MAChB,OAAOV,CAAC,CAACnM,GAAG,EAAE6M,MAAM,CAAC;IACvB;EACF;AACF,CAAE;AACF,IAAMC,SAAS,GAAGb,IAAI,CAACa,SAAS;AAChC,IAAMC,SAAS,GAAGd,IAAI,CAACc,SAAS;AAEhC,SAASC,aAAa,CAAE3J,GAAG,EAAE4J,KAAK,EAAE3B,MAAM,EAAE;EAC1C,IAAM4B,KAAK,GAAG7J,GAAG,CAAC8J,UAAU,CAAC;IAC3B7B,MAAM,EAAEA,MAAM,IAAIW,IAAI,CAACc,SAAS;EAClC,CAAC,CAAC;EACF,IAAMK,cAAc,GAAG,EAAE;EACzBH,KAAK,CAACI,YAAY,GAAG,UAAAnJ,EAAE,EAAI;IACzBkJ,cAAc,CAAChH,IAAI,CAAClC,EAAE,CAAC;EACzB,CAAC;EACDH,MAAM,CAACuJ,cAAc,CAACL,KAAK,EAAE,SAAS,EAAE;IACtCM,GAAG,iBAAI;MACL,OAAOL,KAAK,CAAC5B,MAAM;IACrB,CAAC;IACDkC,GAAG,eAAEC,CAAC,EAAE;MACNP,KAAK,CAAC5B,MAAM,GAAGmC,CAAC;MAChBL,cAAc,CAAC7H,OAAO,CAAC,UAAAmI,KAAK;QAAA,OAAIA,KAAK,CAACD,CAAC,CAAC;MAAA,EAAC;IAC3C;EACF,CAAC,CAAC;AACJ;AAEA,SAAS9B,cAAc,GAAI;EACzB,OAAO,OAAOE,WAAW,KAAK,WAAW,IAAIA,WAAW,CAACC,OAAO,IAAI,CAAC,CAAC/H,MAAM,CAACsB,IAAI,CAACwG,WAAW,CAACC,OAAO,CAAC,CAACzK,MAAM;AAC/G;AAEA,SAASsM,OAAO,CAAE5M,GAAG,EAAE6M,KAAK,EAAE;EAC5B,OAAO,CAAC,CAACA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAK/M,GAAG,CAACP,OAAO,CAACsN,IAAI,CAAC,KAAK,CAAC,CAAC;EAAA,EAAC;AACzD;AAEA,SAASC,UAAU,CAAEhN,GAAG,EAAE6M,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAK/M,GAAG,CAACP,OAAO,CAACsN,IAAI,CAAC,KAAK,CAAC;EAAA,EAAC;AACtD;AAEA,SAASvC,eAAe,CAAED,MAAM,EAAED,QAAQ,EAAE;EAC1C,IAAI,CAACC,MAAM,EAAE;IACX;EACF;EACAA,MAAM,GAAGA,MAAM,CAAC0C,IAAI,EAAE,CAAC/M,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACzC,IAAIoK,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,EAAE;IAChC,OAAOA,MAAM;EACf;EACAA,MAAM,GAAGA,MAAM,CAAC2C,WAAW,EAAE;EAC7B,IAAI3C,MAAM,KAAK,SAAS,EAAE;IACxB;IACA,OAAON,cAAc;EACvB;EACA,IAAIM,MAAM,CAAC9K,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAC9B,IAAI8K,MAAM,CAAC9K,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAChC,OAAOwK,cAAc;IACvB;IACA,IAAIM,MAAM,CAAC9K,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAChC,OAAOyK,cAAc;IACvB;IACA,IAAI0C,OAAO,CAACrC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;MAClD,OAAOL,cAAc;IACvB;IACA,OAAOD,cAAc;EACvB;EACA,IAAMkD,IAAI,GAAGH,UAAU,CAACzC,MAAM,EAAE,CAACJ,SAAS,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAC;EAClE,IAAI8C,IAAI,EAAE;IACR,OAAOA,IAAI;EACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAW,GAAI;EACtB;EACA,IAAIlK,IAAI,CAACmK,MAAM,CAAC,EAAE;IAChB,IAAMC,GAAG,GAAGD,MAAM,CAAC;MACjBE,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,IAAID,GAAG,IAAIA,GAAG,CAACE,GAAG,EAAE;MAClB,OAAOF,GAAG,CAACE,GAAG,CAACC,OAAO;IACxB;EACF;EACA,OAAOjD,eAAe,CAAEjJ,EAAE,CAACkJ,cAAc,EAAE,CAACC,QAAQ,CAAE,IAAIP,SAAS;AACrE;AAEA,SAASuD,WAAW,CAAEnD,MAAM,EAAE;EAC5B,IAAM+C,GAAG,GAAGpK,IAAI,CAACmK,MAAM,CAAC,GAAGA,MAAM,EAAE,GAAG,KAAK;EAC3C,IAAI,CAACC,GAAG,EAAE;IACR,OAAO,KAAK;EACd;EACA,IAAMK,SAAS,GAAGL,GAAG,CAACE,GAAG,CAACC,OAAO;EACjC,IAAIE,SAAS,KAAKpD,MAAM,EAAE;IACxB+C,GAAG,CAACE,GAAG,CAACC,OAAO,GAAGlD,MAAM;IACxBqD,uBAAuB,CAACpJ,OAAO,CAAC,UAACrB,EAAE;MAAA,OAAKA,EAAE,CAAC;QACzCoH,MAAM,EAANA;MACF,CAAC,CAAC;IAAA,EAAC;IACH,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEA,IAAMqD,uBAAuB,GAAG,EAAE;AAClC,SAASC,cAAc,CAAE1K,EAAE,EAAE;EAC3B,IAAIyK,uBAAuB,CAACnO,OAAO,CAAC0D,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9CyK,uBAAuB,CAACvI,IAAI,CAAClC,EAAE,CAAC;EAClC;AACF;AAEA,IAAI,OAAO2K,MAAM,KAAK,WAAW,EAAE;EACjCA,MAAM,CAAC9B,SAAS,GAAGoB,WAAW;AAChC;AAEA,IAAMW,YAAY,GAAG;EACnBvG,kBAAkB,EAAlBA;AACF,CAAC;AAED,IAAIwG,OAAO,GAAG,aAAahL,MAAM,CAACiL,MAAM,CAAC;EACvCC,SAAS,EAAE,IAAI;EACfvE,MAAM,EAAEA,MAAM;EACdwE,MAAM,EAAExE,MAAM;EACdqC,SAAS,EAAEoB,WAAW;EACtBrB,SAAS,EAAE2B,WAAW;EACtBG,cAAc,EAAEA,cAAc;EAC9B/H,cAAc,EAAEA,cAAc;EAC9BE,iBAAiB,EAAEA,iBAAiB;EACpC+H,YAAY,EAAEA;AAChB,CAAC,CAAC;AAEF,SAASK,mBAAmB,CAAEC,GAAG,EAAE;EACjC,IAAMC,KAAK,GAAGC,eAAe,EAAE;EAC/B,IAAIC,GAAG,GAAGF,KAAK,CAAChO,MAAM;EACtB,OAAOkO,GAAG,EAAE,EAAE;IACZ,IAAMC,IAAI,GAAGH,KAAK,CAACE,GAAG,CAAC;IACvB,IAAIC,IAAI,CAACC,KAAK,IAAID,IAAI,CAACC,KAAK,CAACC,QAAQ,KAAKN,GAAG,EAAE;MAC7C,OAAOG,GAAG;IACZ;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEA,IAAII,UAAU,GAAG;EACf/H,IAAI,gBAAEgI,QAAQ,EAAE;IACd,IAAIA,QAAQ,CAACC,MAAM,KAAK,MAAM,IAAID,QAAQ,CAACE,KAAK,EAAE;MAChD,OAAO,cAAc;IACvB;IACA,OAAO,YAAY;EACrB,CAAC;EACDC,IAAI,gBAAEH,QAAQ,EAAE;IACd,IAAIA,QAAQ,CAACC,MAAM,KAAK,MAAM,IAAID,QAAQ,CAACR,GAAG,EAAE;MAC9C,IAAMY,eAAe,GAAGb,mBAAmB,CAACS,QAAQ,CAACR,GAAG,CAAC;MACzD,IAAIY,eAAe,KAAK,CAAC,CAAC,EAAE;QAC1B,IAAMF,KAAK,GAAGR,eAAe,EAAE,CAACjO,MAAM,GAAG,CAAC,GAAG2O,eAAe;QAC5D,IAAIF,KAAK,GAAG,CAAC,EAAE;UACbF,QAAQ,CAACE,KAAK,GAAGA,KAAK;QACxB;MACF;IACF;EACF;AACF,CAAC;AAED,IAAIG,YAAY,GAAG;EACjBF,IAAI,gBAAEH,QAAQ,EAAE;IACd,IAAIM,YAAY,GAAGC,QAAQ,CAACP,QAAQ,CAACQ,OAAO,CAAC;IAC7C,IAAIC,KAAK,CAACH,YAAY,CAAC,EAAE;MACvB;IACF;IACA,IAAMI,IAAI,GAAGV,QAAQ,CAACU,IAAI;IAC1B,IAAI,CAACtK,KAAK,CAACC,OAAO,CAACqK,IAAI,CAAC,EAAE;MACxB;IACF;IACA,IAAMf,GAAG,GAAGe,IAAI,CAACjP,MAAM;IACvB,IAAI,CAACkO,GAAG,EAAE;MACR;IACF;IACA,IAAIW,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM,IAAIA,YAAY,IAAIX,GAAG,EAAE;MAC9BW,YAAY,GAAGX,GAAG,GAAG,CAAC;IACxB;IACA,IAAIW,YAAY,GAAG,CAAC,EAAE;MACpBN,QAAQ,CAACQ,OAAO,GAAGE,IAAI,CAACJ,YAAY,CAAC;MACrCN,QAAQ,CAACU,IAAI,GAAGA,IAAI,CAACC,MAAM,CACzB,UAACC,IAAI,EAAEjK,KAAK;QAAA,OAAKA,KAAK,GAAG2J,YAAY,GAAGM,IAAI,KAAKF,IAAI,CAACJ,YAAY,CAAC,GAAG,IAAI;MAAA,EAC3E;IACH,CAAC,MAAM;MACLN,QAAQ,CAACQ,OAAO,GAAGE,IAAI,CAAC,CAAC,CAAC;IAC5B;IACA,OAAO;MACLG,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE;IACR,CAAC;EACH;AACF,CAAC;AAED,IAAMC,QAAQ,GAAG,gBAAgB;AACjC,IAAIC,QAAQ;AACZ,SAASC,WAAW,CAAEtP,MAAM,EAAE;EAC5BqP,QAAQ,GAAGA,QAAQ,IAAItO,EAAE,CAACC,cAAc,CAACoO,QAAQ,CAAC;EAClD,IAAI,CAACC,QAAQ,EAAE;IACbA,QAAQ,GAAGhN,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAGiH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACgG,MAAM,EAAE,GAAG,GAAG,CAAC;IAC5DxO,EAAE,CAACyO,UAAU,CAAC;MACZ/Q,GAAG,EAAE2Q,QAAQ;MACbzJ,IAAI,EAAE0J;IACR,CAAC,CAAC;EACJ;EACArP,MAAM,CAACqP,QAAQ,GAAGA,QAAQ;AAC5B;AAEA,SAASI,iBAAiB,CAAEzP,MAAM,EAAE;EAClC,IAAIA,MAAM,CAAC0P,QAAQ,EAAE;IACnB,IAAMA,QAAQ,GAAG1P,MAAM,CAAC0P,QAAQ;IAChC1P,MAAM,CAAC2P,cAAc,GAAG;MACtBC,GAAG,EAAEF,QAAQ,CAACE,GAAG;MACjBC,IAAI,EAAEH,QAAQ,CAACG,IAAI;MACnBC,KAAK,EAAE9P,MAAM,CAACiJ,WAAW,GAAGyG,QAAQ,CAACI,KAAK;MAC1CC,MAAM,EAAE/P,MAAM,CAACgQ,YAAY,GAAGN,QAAQ,CAACK;IACzC,CAAC;EACH;AACF;AAEA,SAASE,kBAAkB,CAAEjQ,MAAM,EAAE;EACnC,oBAKIA,MAAM,CAJRkQ,KAAK;IAALA,KAAK,8BAAG,EAAE;IAAA,gBAIRlQ,MAAM,CAJImQ,KAAK;IAALA,KAAK,8BAAG,EAAE;IAAA,iBAIpBnQ,MAAM,CAJgBoQ,MAAM;IAANA,MAAM,+BAAG,EAAE;IAAA,mBAIjCpQ,MAAM,CAHRkK,QAAQ;IAARA,QAAQ,iCAAG,EAAE;IAAEmG,KAAK,GAGlBrQ,MAAM,CAHOqQ,KAAK;IAAEC,OAAO,GAG3BtQ,MAAM,CAHcsQ,OAAO;IAC7BvH,QAAQ,GAEN/I,MAAM,CAFR+I,QAAQ;IAAEwH,eAAe,GAEvBvQ,MAAM,CAFEuQ,eAAe;IACzBC,UAAU,GACRxQ,MAAM,CADRwQ,UAAU;IAAEtH,UAAU,GACpBlJ,MAAM,CADIkJ,UAAU;IAAEuH,iBAAiB,GACvCzQ,MAAM,CADgByQ,iBAAiB;EAE3C;;EAEA,IAAMC,UAAU,GAAG,CAAC,CAAC;;EAErB;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,SAAS,GAAG,EAAE;EAClB;IACED,MAAM,GAAGP,MAAM,CAAC5P,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;IACnCoQ,SAAS,GAAGR,MAAM,CAAC5P,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;EACxC;EACA,IAAIqQ,WAAW,GAAGP,OAAO;;EAEzB;EACA,IAAMQ,UAAU,GAAGC,gBAAgB,CAAC/Q,MAAM,EAAEmQ,KAAK,CAAC;;EAElD;EACA,IAAMa,WAAW,GAAGC,cAAc,CAACf,KAAK,CAAC;;EAEzC;EACA,IAAMgB,SAAS,GAAGC,WAAW,CAACnR,MAAM,CAAC;;EAErC;EACA,IAAIoR,kBAAkB,GAAGX,iBAAiB,CAAC,CAAC;;EAE5C;EACA,IAAIY,iBAAiB,GAAGnI,UAAU;;EAElC;EACA,IAAIoI,WAAW,GAAGd,UAAU;;EAE5B;EACA,IAAMe,YAAY,GAAG,CAACrH,QAAQ,IAAI,EAAE,EAAExK,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;;EAExD;;EAEA,IAAM8R,UAAU,GAAG;IACjBC,KAAK,EAAEC,gBAAsB;IAC7BC,OAAO,EAAED,mBAAwB;IACjCE,UAAU,EAAEF,OAAgC;IAC5CG,cAAc,EAAEH,KAAgC;IAChDI,WAAW,EAAEC,cAAc,CAACR,YAAY,CAAC;IACzCS,iBAAiB,EAAEN,MAAgC;IACnDO,kBAAkB,EAAEP,MAAgC;IACpDQ,iBAAiB,EAAER,MAAgC;IACnDS,WAAW,EAAET,SAA4B,IAAIA,WAAwB;IACrEV,WAAW,EAAXA,WAAW;IACXoB,WAAW,EAAEjC,KAAK;IAClBW,UAAU,EAAVA,UAAU;IACVuB,gBAAgB,EAAEhB,iBAAiB;IACnCZ,iBAAiB,EAAEW,kBAAkB;IACrCT,MAAM,EAAEA,MAAM,CAAC2B,iBAAiB,EAAE;IAClC1B,SAAS,EAATA,SAAS;IACT2B,SAAS,EAAElC,KAAK;IAChBQ,WAAW,EAAXA,WAAW;IACXU,YAAY,EAAZA,YAAY;IACZiB,QAAQ,EAAEtB,SAAS;IACnBuB,cAAc,EAAEnB,WAAW;IAC3BoB,mBAAmB,EAAEnC,eAAe;IACpCoC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,CAAC;IACf;IACAC,UAAU,EAAEC,SAAS;IACrBC,OAAO,EAAED,SAAS;IAClBE,EAAE,EAAEF,SAAS;IACbG,eAAe,EAAEH,SAAS;IAC1BI,WAAW,EAAEJ,SAAS;IACtBK,cAAc,EAAEL,SAAS;IACzBM,SAAS,EAAE;EACb,CAAC;EAED5Q,MAAM,CAAC+F,MAAM,CAACvI,MAAM,EAAEwR,UAAU,EAAEd,UAAU,CAAC;AAC/C;AAEA,SAASK,gBAAgB,CAAE/Q,MAAM,EAAEmQ,KAAK,EAAE;EACxC,IAAIW,UAAU,GAAG9Q,MAAM,CAAC8Q,UAAU,IAAI,OAAO;EAC7C;IACE,IAAMuC,cAAc,GAAG;MACrBC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE,IAAI;MACbC,GAAG,EAAE;IACP,CAAC;IACD,IAAMC,kBAAkB,GAAGjR,MAAM,CAACsB,IAAI,CAACuP,cAAc,CAAC;IACtD,IAAMK,MAAM,GAAGvD,KAAK,CAACmC,iBAAiB,EAAE;IACxC,KAAK,IAAItN,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGyO,kBAAkB,CAAC3T,MAAM,EAAEkF,KAAK,EAAE,EAAE;MAC9D,IAAM2O,EAAE,GAAGF,kBAAkB,CAACzO,KAAK,CAAC;MACpC,IAAI0O,MAAM,CAACzU,OAAO,CAAC0U,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7B7C,UAAU,GAAGuC,cAAc,CAACM,EAAE,CAAC;QAC/B;MACF;IACF;EACF;EACA,OAAO7C,UAAU;AACnB;AAEA,SAASG,cAAc,CAAEf,KAAK,EAAE;EAC9B,IAAIc,WAAW,GAAGd,KAAK;EACvB,IAAIc,WAAW,EAAE;IACfA,WAAW,GAAGd,KAAK,CAACoC,iBAAiB,EAAE;EACzC;EACA,OAAOtB,WAAW;AACpB;AAEA,SAASe,cAAc,CAAE6B,eAAe,EAAE;EACxC,OAAOhH,WAAW,GACdA,WAAW,EAAE,GACbgH,eAAe;AACrB;AAEA,SAASzC,WAAW,CAAEnR,MAAM,EAAE;EAC5B,IAAM6T,SAAS,GAAI,QAAQ;EAC3B,IAAI3C,SAAS,GAAGlR,MAAM,CAACwS,QAAQ,IAAIqB,SAAS,CAAC,CAAC;EAC9C;IACE,IAAI7T,MAAM,CAAC8T,WAAW,EAAE;MACtB5C,SAAS,GAAGlR,MAAM,CAAC8T,WAAW;IAChC,CAAC,MAAM,IAAI9T,MAAM,CAAC+T,IAAI,IAAI/T,MAAM,CAAC+T,IAAI,CAACC,GAAG,EAAE;MACzC9C,SAAS,GAAGlR,MAAM,CAAC+T,IAAI,CAACC,GAAG;IAC7B;EACF;EAEA,OAAO9C,SAAS;AAClB;AAEA,IAAI+C,aAAa,GAAG;EAClBxN,WAAW,EAAE,qBAAUzG,MAAM,EAAE;IAC7BsP,WAAW,CAACtP,MAAM,CAAC;IACnByP,iBAAiB,CAACzP,MAAM,CAAC;IACzBiQ,kBAAkB,CAACjQ,MAAM,CAAC;EAC5B;AACF,CAAC;AAED,IAAIkU,eAAe,GAAG;EACpB1F,IAAI,gBAAEH,QAAQ,EAAE;IACd,IAAI,sBAAOA,QAAQ,MAAK,QAAQ,EAAE;MAChCA,QAAQ,CAAC8F,SAAS,GAAG9F,QAAQ,CAAC+F,KAAK;IACrC;EACF;AACF,CAAC;AAED,IAAInK,cAAc,GAAG;EACnBxD,WAAW,EAAE,qBAAUzG,MAAM,EAAE;IAC7B,cAAiDA,MAAM;MAA/CsQ,OAAO,WAAPA,OAAO;MAAEpG,QAAQ,WAARA,QAAQ;MAAEsG,UAAU,WAAVA,UAAU;MAAEH,KAAK,WAALA,KAAK;IAE5C,IAAMa,SAAS,GAAGC,WAAW,CAACnR,MAAM,CAAC;IAErC,IAAMuR,YAAY,GAAG,CAACrH,QAAQ,IAAI,EAAE,EAAExK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAEvDM,MAAM,GAAG4D,UAAU,CAACpB,MAAM,CAAC+F,MAAM,CAACvI,MAAM,EAAE;MACxCyR,KAAK,EAAEC,gBAAsB;MAC7BC,OAAO,EAAED,mBAAwB;MACjCE,UAAU,EAAEF,OAAgC;MAC5CG,cAAc,EAAEH,KAAgC;MAChDI,WAAW,EAAEC,cAAc,CAACR,YAAY,CAAC;MACzCV,WAAW,EAAEP,OAAO;MACpBiB,YAAY,EAAZA,YAAY;MACZiB,QAAQ,EAAEtB,SAAS;MACnBuB,cAAc,EAAEjC,UAAU;MAC1B+B,SAAS,EAAElC,KAAK;MAChB+C,SAAS,EAAE,KAAK;MAChBjB,WAAW,EAAET,SAA4B,IAAIA,WAAwB;MACrEM,iBAAiB,EAAEN,MAAgC;MACnDO,kBAAkB,EAAEP,MAAgC;MACpDQ,iBAAiB,EAAER,MAAgC2C;IACrD,CAAC,CAAC,CAAC;EACL;AACF,CAAC;AAED,IAAIrL,aAAa,GAAG;EAClBvC,WAAW,EAAE,qBAAUzG,MAAM,EAAE;IAC7B,eAAyBA,MAAM;MAAvBkQ,KAAK,YAALA,KAAK;MAAEC,KAAK,YAALA,KAAK;IACpB,IAAMW,UAAU,GAAGC,gBAAgB,CAAC/Q,MAAM,EAAEmQ,KAAK,CAAC;IAClD,IAAMa,WAAW,GAAGC,cAAc,CAACf,KAAK,CAAC;IACzCZ,WAAW,CAACtP,MAAM,CAAC;IAEnBA,MAAM,GAAG4D,UAAU,CAACpB,MAAM,CAAC+F,MAAM,CAACvI,MAAM,EAAE;MACxC8Q,UAAU,EAAVA,UAAU;MACVE,WAAW,EAAXA,WAAW;MACXoB,WAAW,EAAEjC;IACf,CAAC,CAAC,CAAC;EACL;AACF,CAAC;AAED,IAAIrH,aAAa,GAAG;EAClBrC,WAAW,EAAE,qBAAUzG,MAAM,EAAE;IAC7ByP,iBAAiB,CAACzP,MAAM,CAAC;IAEzBA,MAAM,GAAG4D,UAAU,CAACpB,MAAM,CAAC+F,MAAM,CAACvI,MAAM,EAAE;MACxC2S,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE;IAChB,CAAC,CAAC,CAAC;EACL;AACF,CAAC;AAED,IAAI0B,sBAAsB,GAAG;EAC3B7N,WAAW,EAAE,qBAAUzG,MAAM,EAAE;IAC7B,IAAQuU,uBAAuB,GAAKvU,MAAM,CAAlCuU,uBAAuB;IAE/BvU,MAAM,CAACwU,gBAAgB,GAAG,aAAa;IACvC,IAAID,uBAAuB,KAAK,IAAI,EAAE;MACpCvU,MAAM,CAACwU,gBAAgB,GAAG,SAAS;IACrC,CAAC,MAAM,IAAID,uBAAuB,KAAK,KAAK,EAAE;MAC5CvU,MAAM,CAACwU,gBAAgB,GAAG,MAAM;IAClC;EACF;AACF,CAAC;;AAED;;AAEA,IAAMC,aAAa,GAAG;EACpBjG,IAAI,gBAAEH,QAAQ,EAAE;IACd;IACA,IAAIA,QAAQ,CAACqG,gBAAgB,IAAI,CAACrG,QAAQ,CAACsG,cAAc,EAAE;MACzDtG,QAAQ,CAACsG,cAAc,GAAGtG,QAAQ,CAACqG,gBAAgB;IACrD;IACA,IAAIrG,QAAQ,CAACuG,eAAe,IAAI,CAACvG,QAAQ,CAACwG,aAAa,EAAE;MACvDxG,QAAQ,CAACwG,aAAa,GAAGxG,QAAQ,CAACuG,eAAe;IACnD;EACF;AACF,CAAC;AAED,IAAME,SAAS,GAAG;EAChB1G,UAAU,EAAVA,UAAU;EACV;EACAM,YAAY,EAAZA,YAAY;EACZuF,aAAa,EAAbA,aAAa;EACbc,iBAAiB,EAAEd,aAAa;EAChCC,eAAe,EAAfA,eAAe;EACfjK,cAAc,EAAdA,cAAc;EACdjB,aAAa,EAAbA,aAAa;EACbF,aAAa,EAAbA,aAAa;EACbwL,sBAAsB,EAAtBA,sBAAsB;EACtBG,aAAa,EAAbA;AACF,CAAC;AACD,IAAMO,KAAK,GAAG,CACZ,SAAS,EACT,aAAa,EACb,eAAe,EACf,gBAAgB,CACjB;AACD,IAAMC,QAAQ,GAAG,EAAE;AAEnB,IAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC;AAE3D,SAASC,eAAe,CAAEC,UAAU,EAAE7P,MAAM,EAAEkB,WAAW,EAAE;EACzD,OAAO,UAAUlC,GAAG,EAAE;IACpB,OAAOgB,MAAM,CAAC8P,kBAAkB,CAACD,UAAU,EAAE7Q,GAAG,EAAEkC,WAAW,CAAC,CAAC;EACjE,CAAC;AACH;AAEA,SAAS6O,WAAW,CAAEF,UAAU,EAAE/G,QAAQ,EAA2D;EAAA,IAAzDkH,UAAU,uEAAG,CAAC,CAAC;EAAA,IAAE9O,WAAW,uEAAG,CAAC,CAAC;EAAA,IAAE+O,YAAY,uEAAG,KAAK;EACjG,IAAIzS,aAAa,CAACsL,QAAQ,CAAC,EAAE;IAAE;IAC7B,IAAMoH,MAAM,GAAGD,YAAY,KAAK,IAAI,GAAGnH,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,IAAI3L,IAAI,CAAC6S,UAAU,CAAC,EAAE;MACpBA,UAAU,GAAGA,UAAU,CAAClH,QAAQ,EAAEoH,MAAM,CAAC,IAAI,CAAC,CAAC;IACjD;IACA,KAAK,IAAMhX,GAAG,IAAI4P,QAAQ,EAAE;MAC1B,IAAIpL,MAAM,CAACsS,UAAU,EAAE9W,GAAG,CAAC,EAAE;QAC3B,IAAIiX,SAAS,GAAGH,UAAU,CAAC9W,GAAG,CAAC;QAC/B,IAAIiE,IAAI,CAACgT,SAAS,CAAC,EAAE;UACnBA,SAAS,GAAGA,SAAS,CAACrH,QAAQ,CAAC5P,GAAG,CAAC,EAAE4P,QAAQ,EAAEoH,MAAM,CAAC;QACxD;QACA,IAAI,CAACC,SAAS,EAAE;UAAE;UAChBC,OAAO,CAACC,IAAI,gBAASR,UAAU,4FAAyD3W,GAAG,OAAI;QACjG,CAAC,MAAM,IAAImE,KAAK,CAAC8S,SAAS,CAAC,EAAE;UAAE;UAC7BD,MAAM,CAACC,SAAS,CAAC,GAAGrH,QAAQ,CAAC5P,GAAG,CAAC;QACnC,CAAC,MAAM,IAAIsE,aAAa,CAAC2S,SAAS,CAAC,EAAE;UAAE;UACrCD,MAAM,CAACC,SAAS,CAACrP,IAAI,GAAGqP,SAAS,CAACrP,IAAI,GAAG5H,GAAG,CAAC,GAAGiX,SAAS,CAAC1N,KAAK;QACjE;MACF,CAAC,MAAM,IAAIkN,SAAS,CAACjW,OAAO,CAACR,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACxC,IAAIiE,IAAI,CAAC2L,QAAQ,CAAC5P,GAAG,CAAC,CAAC,EAAE;UACvBgX,MAAM,CAAChX,GAAG,CAAC,GAAG0W,eAAe,CAACC,UAAU,EAAE/G,QAAQ,CAAC5P,GAAG,CAAC,EAAEgI,WAAW,CAAC;QACvE;MACF,CAAC,MAAM;QACL,IAAI,CAAC+O,YAAY,EAAE;UACjBC,MAAM,CAAChX,GAAG,CAAC,GAAG4P,QAAQ,CAAC5P,GAAG,CAAC;QAC7B;MACF;IACF;IACA,OAAOgX,MAAM;EACf,CAAC,MAAM,IAAI/S,IAAI,CAAC2L,QAAQ,CAAC,EAAE;IACzBA,QAAQ,GAAG8G,eAAe,CAACC,UAAU,EAAE/G,QAAQ,EAAE5H,WAAW,CAAC;EAC/D;EACA,OAAO4H,QAAQ;AACjB;AAEA,SAASgH,kBAAkB,CAAED,UAAU,EAAE7Q,GAAG,EAAEkC,WAAW,EAA2B;EAAA,IAAzBoP,eAAe,uEAAG,KAAK;EAChF,IAAInT,IAAI,CAACoS,SAAS,CAACrO,WAAW,CAAC,EAAE;IAAE;IACjClC,GAAG,GAAGuQ,SAAS,CAACrO,WAAW,CAAC2O,UAAU,EAAE7Q,GAAG,CAAC;EAC9C;EACA,OAAO+Q,WAAW,CAACF,UAAU,EAAE7Q,GAAG,EAAEkC,WAAW,EAAE,CAAC,CAAC,EAAEoP,eAAe,CAAC;AACvE;AAEA,SAASC,OAAO,CAAEV,UAAU,EAAE7P,MAAM,EAAE;EACpC,IAAItC,MAAM,CAAC6R,SAAS,EAAEM,UAAU,CAAC,EAAE;IACjC,IAAMW,QAAQ,GAAGjB,SAAS,CAACM,UAAU,CAAC;IACtC,IAAI,CAACW,QAAQ,EAAE;MAAE;MACf,OAAO,YAAY;QACjBJ,OAAO,CAAClU,KAAK,uEAAuC2T,UAAU,QAAK;MACrE,CAAC;IACH;IACA,OAAO,UAAUY,IAAI,EAAEC,IAAI,EAAE;MAAE;MAC7B,IAAI7P,OAAO,GAAG2P,QAAQ;MACtB,IAAIrT,IAAI,CAACqT,QAAQ,CAAC,EAAE;QAClB3P,OAAO,GAAG2P,QAAQ,CAACC,IAAI,CAAC;MAC1B;MAEAA,IAAI,GAAGV,WAAW,CAACF,UAAU,EAAEY,IAAI,EAAE5P,OAAO,CAACoI,IAAI,EAAEpI,OAAO,CAACK,WAAW,CAAC;MAEvE,IAAM+H,IAAI,GAAG,CAACwH,IAAI,CAAC;MACnB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;QAC/BzH,IAAI,CAAC3J,IAAI,CAACoR,IAAI,CAAC;MACjB;MACA,IAAIvT,IAAI,CAAC0D,OAAO,CAACC,IAAI,CAAC,EAAE;QACtB+O,UAAU,GAAGhP,OAAO,CAACC,IAAI,CAAC2P,IAAI,CAAC;MACjC,CAAC,MAAM,IAAIpT,KAAK,CAACwD,OAAO,CAACC,IAAI,CAAC,EAAE;QAC9B+O,UAAU,GAAGhP,OAAO,CAACC,IAAI;MAC3B;MACA,IAAMI,WAAW,GAAG1F,EAAE,CAACqU,UAAU,CAAC,CAACc,KAAK,CAACnV,EAAE,EAAEyN,IAAI,CAAC;MAClD,IAAIhH,SAAS,CAAC4N,UAAU,CAAC,EAAE;QAAE;QAC3B,OAAOC,kBAAkB,CAACD,UAAU,EAAE3O,WAAW,EAAEL,OAAO,CAACK,WAAW,EAAEc,YAAY,CAAC6N,UAAU,CAAC,CAAC;MACnG;MACA,OAAO3O,WAAW;IACpB,CAAC;EACH;EACA,OAAOlB,MAAM;AACf;AAEA,IAAM4Q,QAAQ,GAAG3T,MAAM,CAACa,MAAM,CAAC,IAAI,CAAC;AAEpC,IAAM+S,KAAK,GAAG,CACZ,sBAAsB,EACtB,eAAe,EACf,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,OAAO,CACR;AAED,SAASC,aAAa,CAAEhQ,IAAI,EAAE;EAC5B,OAAO,SAASiQ,OAAO,OAGpB;IAAA,IAFDjO,IAAI,QAAJA,IAAI;MACJC,QAAQ,QAARA,QAAQ;IAER,IAAM/D,GAAG,GAAG;MACVgS,MAAM,YAAKlQ,IAAI,2BAAiBA,IAAI;IACtC,CAAC;IACD3D,IAAI,CAAC2F,IAAI,CAAC,IAAIA,IAAI,CAAC9D,GAAG,CAAC;IACvB7B,IAAI,CAAC4F,QAAQ,CAAC,IAAIA,QAAQ,CAAC/D,GAAG,CAAC;EACjC,CAAC;AACH;AAEA6R,KAAK,CAACpS,OAAO,CAAC,UAAUqC,IAAI,EAAE;EAC5B8P,QAAQ,CAAC9P,IAAI,CAAC,GAAGgQ,aAAa,CAAChQ,IAAI,CAAC;AACtC,CAAC,CAAC;AAEF,IAAImQ,SAAS,GAAG;EACdC,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB9R,IAAI,EAAE,CAAC,QAAQ;AACjB,CAAC;AAED,SAAS+R,WAAW,QAKjB;EAAA,IAJDC,OAAO,SAAPA,OAAO;IACPzO,OAAO,SAAPA,OAAO;IACPC,IAAI,SAAJA,IAAI;IACJC,QAAQ,SAARA,QAAQ;EAER,IAAI/D,GAAG,GAAG,KAAK;EACf,IAAIiS,SAAS,CAACK,OAAO,CAAC,EAAE;IACtBtS,GAAG,GAAG;MACJgS,MAAM,EAAE,gBAAgB;MACxBM,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAAEN,SAAS,CAACK,OAAO;IAC7B,CAAC;IACDnU,IAAI,CAAC0F,OAAO,CAAC,IAAIA,OAAO,CAAC7D,GAAG,CAAC;EAC/B,CAAC,MAAM;IACLA,GAAG,GAAG;MACJgS,MAAM,EAAE;IACV,CAAC;IACD7T,IAAI,CAAC2F,IAAI,CAAC,IAAIA,IAAI,CAAC9D,GAAG,CAAC;EACzB;EACA7B,IAAI,CAAC4F,QAAQ,CAAC,IAAIA,QAAQ,CAAC/D,GAAG,CAAC;AACjC;AAEA,IAAIwS,QAAQ,GAAG,aAAavU,MAAM,CAACiL,MAAM,CAAC;EACxCC,SAAS,EAAE,IAAI;EACfkJ,WAAW,EAAEA;AACf,CAAC,CAAC;AAEF,IAAMI,UAAU,GAAI,YAAY;EAC9B,IAAIC,OAAO;EACX,OAAO,SAASC,aAAa,GAAI;IAC/B,IAAI,CAACD,OAAO,EAAE;MACZA,OAAO,GAAG,IAAInV,YAAG,EAAE;IACrB;IACA,OAAOmV,OAAO;EAChB,CAAC;AACH,CAAC,EAAG;AAEJ,SAASf,KAAK,CAAEiB,GAAG,EAAE5R,MAAM,EAAEiJ,IAAI,EAAE;EACjC,OAAO2I,GAAG,CAAC5R,MAAM,CAAC,CAAC2Q,KAAK,CAACiB,GAAG,EAAE3I,IAAI,CAAC;AACrC;AAEA,SAAS4I,GAAG,GAAI;EACd,OAAOlB,KAAK,CAACc,UAAU,EAAE,EAAE,KAAK,6BAAMK,SAAS,EAAE;AACnD;AACA,SAASC,IAAI,GAAI;EACf,OAAOpB,KAAK,CAACc,UAAU,EAAE,EAAE,MAAM,6BAAMK,SAAS,EAAE;AACpD;AACA,SAASlM,KAAK,GAAI;EAChB,OAAO+K,KAAK,CAACc,UAAU,EAAE,EAAE,OAAO,6BAAMK,SAAS,EAAE;AACrD;AACA,SAASE,KAAK,GAAI;EAChB,OAAOrB,KAAK,CAACc,UAAU,EAAE,EAAE,OAAO,6BAAMK,SAAS,EAAE;AACrD;AAEA,IAAIG,QAAQ,GAAG,aAAahV,MAAM,CAACiL,MAAM,CAAC;EACxCC,SAAS,EAAE,IAAI;EACf0J,GAAG,EAAEA,GAAG;EACRE,IAAI,EAAEA,IAAI;EACVnM,KAAK,EAAEA,KAAK;EACZoM,KAAK,EAAEA;AACT,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,QAAQ,CAAE9U,EAAE,EAAE;EACrB,OAAO,YAAY;IACjB,IAAI;MACF,OAAOA,EAAE,CAACuT,KAAK,CAACvT,EAAE,EAAE0U,SAAS,CAAC;IAChC,CAAC,CAAC,OAAOK,CAAC,EAAE;MACV;MACA/B,OAAO,CAAClU,KAAK,CAACiW,CAAC,CAAC;IAClB;EACF,CAAC;AACH;AAEA,SAASC,eAAe,CAAEjS,MAAM,EAAE;EAChC,IAAMkS,YAAY,GAAG,CAAC,CAAC;EACvB,KAAK,IAAMvR,IAAI,IAAIX,MAAM,EAAE;IACzB,IAAMmS,KAAK,GAAGnS,MAAM,CAACW,IAAI,CAAC;IAC1B,IAAI3D,IAAI,CAACmV,KAAK,CAAC,EAAE;MACfD,YAAY,CAACvR,IAAI,CAAC,GAAGoR,QAAQ,CAACI,KAAK,CAAC;MACpC,OAAOnS,MAAM,CAACW,IAAI,CAAC;IACrB;EACF;EACA,OAAOuR,YAAY;AACrB;AAEA,IAAIE,GAAG;AACP,IAAIC,SAAS;AACb,IAAIC,OAAO;AAEX,SAASC,oBAAoB,CAAEvW,OAAO,EAAE;EACtC,IAAI;IACF,OAAOH,IAAI,CAACC,KAAK,CAACE,OAAO,CAAC;EAC5B,CAAC,CAAC,OAAOgW,CAAC,EAAE,CAAC;EACb,OAAOhW,OAAO;AAChB;AAEA,SAASwW,kBAAkB,CACzB1J,IAAI,EACJ;EACA,IAAIA,IAAI,CAAC2J,IAAI,KAAK,SAAS,EAAE;IAC3BH,OAAO,GAAG,IAAI;EAChB,CAAC,MAAM,IAAIxJ,IAAI,CAAC2J,IAAI,KAAK,UAAU,EAAE;IACnCL,GAAG,GAAGtJ,IAAI,CAACsJ,GAAG;IACdC,SAAS,GAAGvJ,IAAI,CAAC+H,MAAM;IACvB6B,yBAAyB,CAACN,GAAG,EAAEtJ,IAAI,CAAC+H,MAAM,CAAC;EAC7C,CAAC,MAAM,IAAI/H,IAAI,CAAC2J,IAAI,KAAK,SAAS,EAAE;IAClC,IAAMzW,OAAO,GAAG;MACdyW,IAAI,EAAE,SAAS;MACfxS,IAAI,EAAEsS,oBAAoB,CAACzJ,IAAI,CAAC9M,OAAO;IACzC,CAAC;IACD,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkY,sBAAsB,CAACvY,MAAM,EAAEK,CAAC,EAAE,EAAE;MACtD,IAAM+F,QAAQ,GAAGmS,sBAAsB,CAAClY,CAAC,CAAC;MAC1C+F,QAAQ,CAACxE,OAAO,CAAC;MACjB;MACA,IAAIA,OAAO,CAAC4W,OAAO,EAAE;QACnB;MACF;IACF;EACF,CAAC,MAAM,IAAI9J,IAAI,CAAC2J,IAAI,KAAK,OAAO,EAAE;IAChCE,sBAAsB,CAACrU,OAAO,CAAC,UAACkC,QAAQ,EAAK;MAC3CA,QAAQ,CAAC;QACPiS,IAAI,EAAE,OAAO;QACbxS,IAAI,EAAEsS,oBAAoB,CAACzJ,IAAI,CAAC9M,OAAO;MACzC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;AAEA,IAAM6W,mBAAmB,GAAG,EAAE;AAE9B,SAASH,yBAAyB,CAAEN,GAAG,EAAEvB,MAAM,EAAE;EAC/CgC,mBAAmB,CAACvU,OAAO,CAAC,UAACkC,QAAQ,EAAK;IACxCA,QAAQ,CAAC4R,GAAG,EAAEvB,MAAM,CAAC;EACvB,CAAC,CAAC;EACFgC,mBAAmB,CAACzY,MAAM,GAAG,CAAC;AAChC;AAEA,SAAS0Y,eAAe,CAAEhK,IAAI,EAAE;EAC9B,IAAI,CAACzL,aAAa,CAACyL,IAAI,CAAC,EAAE;IACxBA,IAAI,GAAG,CAAC,CAAC;EACX;EACA,uBAIImJ,eAAe,CAACnJ,IAAI,CAAC;IAHvBpG,OAAO,oBAAPA,OAAO;IACPC,IAAI,oBAAJA,IAAI;IACJC,QAAQ,oBAARA,QAAQ;EAEV,IAAMmQ,UAAU,GAAG/V,IAAI,CAAC0F,OAAO,CAAC;EAChC,IAAMsQ,OAAO,GAAGhW,IAAI,CAAC2F,IAAI,CAAC;EAC1B,IAAMsQ,WAAW,GAAGjW,IAAI,CAAC4F,QAAQ,CAAC;EAElCtC,OAAO,CAACC,OAAO,EAAE,CAACJ,IAAI,CAAC,YAAM;IAC3B,IAAI,OAAOmS,OAAO,KAAK,WAAW,EAAE;MAClCA,OAAO,GAAG,KAAK;MACfF,GAAG,GAAG,EAAE;MACRC,SAAS,GAAG,wBAAwB;IACtC;IACAQ,mBAAmB,CAAC1T,IAAI,CAAC,UAACiT,GAAG,EAAEvB,MAAM,EAAK;MACxC,IAAIhS,GAAG;MACP,IAAIuT,GAAG,EAAE;QACPvT,GAAG,GAAG;UACJgS,MAAM,EAAE,oBAAoB;UAC5BuB,GAAG,EAAHA;QACF,CAAC;QACDW,UAAU,IAAIrQ,OAAO,CAAC7D,GAAG,CAAC;MAC5B,CAAC,MAAM;QACLA,GAAG,GAAG;UACJgS,MAAM,EAAE,sBAAsB,IAAIA,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE;QAC9D,CAAC;QACDmC,OAAO,IAAIrQ,IAAI,CAAC9D,GAAG,CAAC;MACtB;MACAoU,WAAW,IAAIrQ,QAAQ,CAAC/D,GAAG,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,OAAOuT,GAAG,KAAK,WAAW,EAAE;MAC9BM,yBAAyB,CAACN,GAAG,EAAEC,SAAS,CAAC;IAC3C;EACF,CAAC,CAAC;AACJ;AAEA,IAAMM,sBAAsB,GAAG,EAAE;AACjC;AACA,IAAMO,aAAa,GAAG,SAAhBA,aAAa,CAAIjW,EAAE,EAAK;EAC5B,IAAI0V,sBAAsB,CAACpZ,OAAO,CAAC0D,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7C0V,sBAAsB,CAACxT,IAAI,CAAClC,EAAE,CAAC;EACjC;AACF,CAAC;AAED,IAAMkW,cAAc,GAAG,SAAjBA,cAAc,CAAIlW,EAAE,EAAK;EAC7B,IAAI,CAACA,EAAE,EAAE;IACP0V,sBAAsB,CAACvY,MAAM,GAAG,CAAC;EACnC,CAAC,MAAM;IACL,IAAMkF,KAAK,GAAGqT,sBAAsB,CAACpZ,OAAO,CAAC0D,EAAE,CAAC;IAChD,IAAIqC,KAAK,GAAG,CAAC,CAAC,EAAE;MACdqT,sBAAsB,CAACpT,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACzC;EACF;AACF,CAAC;AAED,IAAI8T,QAAQ,GAAG/X,EAAE,CAACkJ,cAAc,IAAIlJ,EAAE,CAACkJ,cAAc,EAAE;AACvD,IAAI,CAAC6O,QAAQ,EAAE;EACbA,QAAQ,GAAG/X,EAAE,CAACgU,iBAAiB,EAAE;AACnC;AACA,IAAMhB,IAAI,GAAG+E,QAAQ,GAAGA,QAAQ,CAAC/E,IAAI,GAAG,IAAI;AAC5C,IAAMgF,iBAAiB,GACrBhF,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAK,SAAS,GAAGjT,EAAE,CAACiY,OAAO,CAACD,iBAAiB,GAAGhY,EAAE,CAACgY,iBAAiB;AAEtF,IAAIjS,GAAG,GAAG,aAAatE,MAAM,CAACiL,MAAM,CAAC;EACnCC,SAAS,EAAE,IAAI;EACfqL,iBAAiB,EAAEA,iBAAiB;EACpCP,eAAe,EAAEA,eAAe;EAChCI,aAAa,EAAEA,aAAa;EAC5BC,cAAc,EAAEA,cAAc;EAC9BX,kBAAkB,EAAEA;AACtB,CAAC,CAAC;AAEF,IAAMe,KAAK,GAAG,CAAC,WAAW,EAAE,sBAAsB,EAAE,iBAAiB,CAAC;AAEtE,SAASC,aAAa,CAAEC,EAAE,EAAEC,MAAM,EAAE;EAClC,IAAMC,SAAS,GAAGF,EAAE,CAACE,SAAS;EAC9B;EACA,KAAK,IAAIlZ,CAAC,GAAGkZ,SAAS,CAACvZ,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAMmZ,OAAO,GAAGD,SAAS,CAAClZ,CAAC,CAAC;IAC5B,IAAImZ,OAAO,CAACC,MAAM,CAACC,OAAO,KAAKJ,MAAM,EAAE;MACrC,OAAOE,OAAO;IAChB;EACF;EACA;EACA,IAAIG,QAAQ;EACZ,KAAK,IAAItZ,EAAC,GAAGkZ,SAAS,CAACvZ,MAAM,GAAG,CAAC,EAAEK,EAAC,IAAI,CAAC,EAAEA,EAAC,EAAE,EAAE;IAC9CsZ,QAAQ,GAAGP,aAAa,CAACG,SAAS,CAAClZ,EAAC,CAAC,EAAEiZ,MAAM,CAAC;IAC9C,IAAIK,QAAQ,EAAE;MACZ,OAAOA,QAAQ;IACjB;EACF;AACF;AAEA,SAASC,YAAY,CAAEtT,OAAO,EAAE;EAC9B,OAAOuT,QAAQ,CAACvT,OAAO,CAAC;AAC1B;AAEA,SAASwT,MAAM,GAAI;EACjB,OAAO,CAAC,CAAC,IAAI,CAACC,KAAK;AACrB;AAEA,SAASC,YAAY,CAAEC,MAAM,EAAE;EAC7B,IAAI,CAACC,YAAY,CAAC,KAAK,EAAED,MAAM,CAAC;AAClC;AAEA,SAASE,mBAAmB,CAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EACzD,IAAMC,UAAU,GAAGH,UAAU,CAACD,mBAAmB,CAACE,QAAQ,CAAC,IAAI,EAAE;EACjEE,UAAU,CAACrW,OAAO,CAAC,UAAAsW,SAAS,EAAI;IAC9B,IAAMC,GAAG,GAAGD,SAAS,CAACE,OAAO,CAACD,GAAG;IACjCH,KAAK,CAACG,GAAG,CAAC,GAAGD,SAAS,CAACtN,GAAG,IAAIyN,MAAM,CAACH,SAAS,CAAC;IAC/C;MACE,IAAIA,SAAS,CAACE,OAAO,CAACE,UAAU,KAAK,QAAQ,EAAE;QAC7CJ,SAAS,CAACL,mBAAmB,CAAC,aAAa,CAAC,CAACjW,OAAO,CAAC,UAAA2W,eAAe,EAAI;UACtEV,mBAAmB,CAACU,eAAe,EAAER,QAAQ,EAAEC,KAAK,CAAC;QACvD,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASQ,QAAQ,CAAEC,IAAI,EAAEC,OAAO,EAAE;EAChC,IAAMC,OAAO,4BAAOC,GAAG,mCAAIxY,MAAM,CAACsB,IAAI,CAAC+W,IAAI,CAAC,EAAC;EAC7C,IAAMI,OAAO,GAAGzY,MAAM,CAACsB,IAAI,CAACgX,OAAO,CAAC;EACpCG,OAAO,CAACjX,OAAO,CAAC,UAAAvF,GAAG,EAAI;IACrB,IAAMyc,QAAQ,GAAGL,IAAI,CAACpc,GAAG,CAAC;IAC1B,IAAM0c,QAAQ,GAAGL,OAAO,CAACrc,GAAG,CAAC;IAC7B,IAAIgG,KAAK,CAACC,OAAO,CAACwW,QAAQ,CAAC,IAAIzW,KAAK,CAACC,OAAO,CAACyW,QAAQ,CAAC,IAAID,QAAQ,CAACpb,MAAM,KAAKqb,QAAQ,CAACrb,MAAM,IAAIqb,QAAQ,CAACC,KAAK,CAAC,UAAApT,KAAK;MAAA,OAAIkT,QAAQ,CAAClc,QAAQ,CAACgJ,KAAK,CAAC;IAAA,EAAC,EAAE;MAClJ;IACF;IACA6S,IAAI,CAACpc,GAAG,CAAC,GAAG0c,QAAQ;IACpBJ,OAAO,CAACM,MAAM,CAAC5c,GAAG,CAAC;EACrB,CAAC,CAAC;EACFsc,OAAO,CAAC/W,OAAO,CAAC,UAAAvF,GAAG,EAAI;IACrB,OAAOoc,IAAI,CAACpc,GAAG,CAAC;EAClB,CAAC,CAAC;EACF,OAAOoc,IAAI;AACb;AAEA,SAASS,QAAQ,CAAEnC,EAAE,EAAE;EACrB,IAAMe,UAAU,GAAGf,EAAE,CAACI,MAAM;EAC5B,IAAMsB,IAAI,GAAG,CAAC,CAAC;EACfrY,MAAM,CAACuJ,cAAc,CAACoN,EAAE,EAAE,OAAO,EAAE;IACjCnN,GAAG,iBAAI;MACL,IAAMoO,KAAK,GAAG,CAAC,CAAC;MAChBH,mBAAmB,CAACC,UAAU,EAAE,UAAU,EAAEE,KAAK,CAAC;MAClD;MACA,IAAMmB,aAAa,GAAGrB,UAAU,CAACD,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,EAAE;MAC7EsB,aAAa,CAACvX,OAAO,CAAC,UAAAsW,SAAS,EAAI;QACjC,IAAMC,GAAG,GAAGD,SAAS,CAACE,OAAO,CAACD,GAAG;QACjC,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC,EAAE;UACfH,KAAK,CAACG,GAAG,CAAC,GAAG,EAAE;QACjB;QACAH,KAAK,CAACG,GAAG,CAAC,CAAC1V,IAAI,CAACyV,SAAS,CAACtN,GAAG,IAAIyN,MAAM,CAACH,SAAS,CAAC,CAAC;MACrD,CAAC,CAAC;MACF,OAAOM,QAAQ,CAACC,IAAI,EAAET,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;AACJ;AAEA,SAASoB,UAAU,CAAEC,KAAK,EAAE;EAC1B,YAGIA,KAAK,CAAC1B,MAAM,IAAI0B,KAAK,CAACzT,KAAK;IAF7BoR,MAAM,SAANA,MAAM;IACNsC,UAAU,SAAVA,UAAU,CACoB,CAAC;;EAEjC,IAAIjC,QAAQ;EAEZ,IAAIL,MAAM,EAAE;IACVK,QAAQ,GAAGP,aAAa,CAAC,IAAI,CAAClM,GAAG,EAAEoM,MAAM,CAAC;EAC5C;EAEA,IAAI,CAACK,QAAQ,EAAE;IACbA,QAAQ,GAAG,IAAI,CAACzM,GAAG;EACrB;EAEA0O,UAAU,CAACC,MAAM,GAAGlC,QAAQ;AAC9B;AAEA,SAASmC,eAAe,CAAEtB,SAAS,EAAE;EACnC;EACA,IAAMuB,KAAK,GAAG,mBAAmB;EACjCrZ,MAAM,CAACuJ,cAAc,CAACuO,SAAS,EAAEuB,KAAK,EAAE;IACtCC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,KAAK;IACjB/T,KAAK,EAAE;EACT,CAAC,CAAC;EACF,OAAOsS,SAAS;AAClB;AAEA,SAASG,MAAM,CAAE3X,GAAG,EAAE;EACpB,IAAMkZ,EAAE,GAAG,QAAQ;EACnB,IAAMC,IAAI,GAAG,UAAU;EACvB,IAAIpZ,QAAQ,CAACC,GAAG,CAAC,IAAIN,MAAM,CAAC0Z,YAAY,CAACpZ,GAAG,CAAC,EAAE;IAC7C;IACAN,MAAM,CAACuJ,cAAc,CAACjJ,GAAG,EAAEkZ,EAAE,EAAE;MAC7BF,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjB/T,KAAK,oCACFiU,IAAI,EAAG,IAAI;IAEhB,CAAC,CAAC;EACJ;EACA,OAAOnZ,GAAG;AACZ;AAEA,IAAMqZ,UAAU,GAAG,wBAAwB;AAC3C,SAASC,kBAAkB,CAAEC,SAAS,EAAEC,UAAU,EAAE;EAClD,IAAIA,UAAU,EAAE;IACd9Z,MAAM,CAACsB,IAAI,CAACwY,UAAU,CAAC,CAACtY,OAAO,CAAC,UAACqC,IAAI,EAAK;MACxC,IAAMkW,OAAO,GAAGlW,IAAI,CAACmW,KAAK,CAACL,UAAU,CAAC;MACtC,IAAII,OAAO,EAAE;QACX,IAAME,WAAW,GAAGF,OAAO,CAAC,CAAC,CAAC;QAC9BF,SAAS,CAAChW,IAAI,CAAC,GAAGiW,UAAU,CAACjW,IAAI,CAAC;QAClCgW,SAAS,CAACI,WAAW,CAAC,GAAGH,UAAU,CAACG,WAAW,CAAC;MAClD;IACF,CAAC,CAAC;EACJ;AACF;AAEA,IAAMC,MAAM,GAAGC,IAAI;AACnB,IAAMC,WAAW,GAAGC,SAAS;AAE7B,IAAMC,WAAW,GAAG,IAAI;AAExB,IAAMC,SAAS,GAAG5Z,MAAM,CAAC,UAAC3D,GAAG,EAAK;EAChC,OAAOiE,QAAQ,CAACjE,GAAG,CAACE,OAAO,CAACod,WAAW,EAAE,GAAG,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,SAASE,gBAAgB,CAAE9C,UAAU,EAAE;EACrC,IAAM+C,eAAe,GAAG/C,UAAU,CAACF,YAAY;EAC/C,IAAMkD,eAAe,GAAG,SAAlBA,eAAe,CAAazB,KAAK,EAAW;IAAA,mCAANjN,IAAI;MAAJA,IAAI;IAAA;IAC9C;IACA,IAAI,IAAI,CAACxB,GAAG,IAAK,IAAI,CAACwN,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC2C,OAAQ,EAAE;MACtD1B,KAAK,GAAGsB,SAAS,CAACtB,KAAK,CAAC;IAC1B,CAAC,MAAM;MACL;MACA,IAAM2B,QAAQ,GAAGL,SAAS,CAACtB,KAAK,CAAC;MACjC,IAAI2B,QAAQ,KAAK3B,KAAK,EAAE;QACtBwB,eAAe,CAAC/G,KAAK,CAAC,IAAI,GAAGkH,QAAQ,SAAK5O,IAAI,EAAE;MAClD;IACF;IACA,OAAOyO,eAAe,CAAC/G,KAAK,CAAC,IAAI,GAAGuF,KAAK,SAAKjN,IAAI,EAAE;EACtD,CAAC;EACD,IAAI;IACF;IACA0L,UAAU,CAACF,YAAY,GAAGkD,eAAe;EAC3C,CAAC,CAAC,OAAOzb,KAAK,EAAE;IACdyY,UAAU,CAACmD,aAAa,GAAGH,eAAe;EAC5C;AACF;AAEA,SAASI,QAAQ,CAAEjX,IAAI,EAAED,OAAO,EAAEmX,WAAW,EAAE;EAC7C,IAAMC,OAAO,GAAGpX,OAAO,CAACC,IAAI,CAAC;EAC7BD,OAAO,CAACC,IAAI,CAAC,GAAG,YAAmB;IACjCuV,eAAe,CAAC,IAAI,CAAC;IACrBoB,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAIQ,OAAO,EAAE;MAAA,mCAHchP,IAAI;QAAJA,IAAI;MAAA;MAI7B,OAAOgP,OAAO,CAACtH,KAAK,CAAC,IAAI,EAAE1H,IAAI,CAAC;IAClC;EACF,CAAC;AACH;AACA,IAAI,CAACkO,MAAM,CAACe,YAAY,EAAE;EACxBf,MAAM,CAACe,YAAY,GAAG,IAAI;EAC1Bd,IAAI,GAAG,gBAAwB;IAAA,IAAdvW,OAAO,uEAAG,CAAC,CAAC;IAC3BkX,QAAQ,CAAC,QAAQ,EAAElX,OAAO,CAAC;IAC3B,OAAOsW,MAAM,CAACtW,OAAO,CAAC;EACxB,CAAC;EACDuW,IAAI,CAACe,KAAK,GAAGhB,MAAM,CAACgB,KAAK;EAEzBb,SAAS,GAAG,qBAAwB;IAAA,IAAdzW,OAAO,uEAAG,CAAC,CAAC;IAChCkX,QAAQ,CAAC,SAAS,EAAElX,OAAO,CAAC;IAC5B,OAAOwW,WAAW,CAACxW,OAAO,CAAC;EAC7B,CAAC;AACH;AAEA,IAAMuX,gBAAgB,GAAG,CACvB,mBAAmB,EACnB,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,UAAU,EACV,cAAc,CACf;AAED,SAASC,SAAS,CAAEzE,EAAE,EAAEF,KAAK,EAAE;EAC7B,IAAMiB,UAAU,GAAGf,EAAE,CAAC0E,GAAG,CAAC1E,EAAE,CAAC2E,MAAM,CAAC;EACpC7E,KAAK,CAACjV,OAAO,CAAC,UAAA+Z,IAAI,EAAI;IACpB,IAAI9a,MAAM,CAACiX,UAAU,EAAE6D,IAAI,CAAC,EAAE;MAC5B5E,EAAE,CAAC4E,IAAI,CAAC,GAAG7D,UAAU,CAAC6D,IAAI,CAAC;IAC7B;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,OAAO,CAAEjZ,IAAI,EAAE2W,UAAU,EAAE;EAClC,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAI5Z,YAAG,CAACsE,OAAO,IAAI3B,KAAK,CAACC,OAAO,CAAC5C,YAAG,CAACsE,OAAO,CAACrB,IAAI,CAAC,CAAC,EAAE;IACnD,OAAO,IAAI;EACb;EAEA2W,UAAU,GAAGA,UAAU,CAACuC,OAAO,IAAIvC,UAAU;EAE7C,IAAIhZ,IAAI,CAACgZ,UAAU,CAAC,EAAE;IACpB,IAAIhZ,IAAI,CAACgZ,UAAU,CAACwC,aAAa,CAACnZ,IAAI,CAAC,CAAC,EAAE;MACxC,OAAO,IAAI;IACb;IACA,IAAI2W,UAAU,CAACyC,KAAK,IAClBzC,UAAU,CAACyC,KAAK,CAAC/X,OAAO,IACxB3B,KAAK,CAACC,OAAO,CAACgX,UAAU,CAACyC,KAAK,CAAC/X,OAAO,CAACrB,IAAI,CAAC,CAAC,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA,IAAIrC,IAAI,CAACgZ,UAAU,CAAC3W,IAAI,CAAC,CAAC,IAAIN,KAAK,CAACC,OAAO,CAACgX,UAAU,CAAC3W,IAAI,CAAC,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;EACA,IAAMqZ,MAAM,GAAG1C,UAAU,CAAC0C,MAAM;EAChC,IAAI3Z,KAAK,CAACC,OAAO,CAAC0Z,MAAM,CAAC,EAAE;IACzB,OAAO,CAAC,CAACA,MAAM,CAAC9R,IAAI,CAAC,UAAAxB,KAAK;MAAA,OAAIkT,OAAO,CAACjZ,IAAI,EAAE+F,KAAK,CAAC;IAAA,EAAC;EACrD;AACF;AAEA,SAASuT,SAAS,CAAEC,SAAS,EAAE1Z,KAAK,EAAE8W,UAAU,EAAE;EAChD9W,KAAK,CAACZ,OAAO,CAAC,UAAAe,IAAI,EAAI;IACpB,IAAIiZ,OAAO,CAACjZ,IAAI,EAAE2W,UAAU,CAAC,EAAE;MAC7B4C,SAAS,CAACvZ,IAAI,CAAC,GAAG,UAAUyJ,IAAI,EAAE;QAChC,OAAO,IAAI,CAACxB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACuR,WAAW,CAACxZ,IAAI,EAAEyJ,IAAI,CAAC;MACrD,CAAC;IACH;EACF,CAAC,CAAC;AACJ;AAEA,SAASgQ,gBAAgB,CAAEF,SAAS,EAAE5C,UAAU,EAAiB;EAAA,IAAf+C,QAAQ,uEAAG,EAAE;EAC7DC,SAAS,CAAChD,UAAU,CAAC,CAAC1X,OAAO,CAAC,UAACe,IAAI;IAAA,OAAK4Z,UAAU,CAACL,SAAS,EAAEvZ,IAAI,EAAE0Z,QAAQ,CAAC;EAAA,EAAC;AAChF;AAEA,SAASC,SAAS,CAAEhD,UAAU,EAAc;EAAA,IAAZ9W,KAAK,uEAAG,EAAE;EACxC,IAAI8W,UAAU,EAAE;IACdlZ,MAAM,CAACsB,IAAI,CAAC4X,UAAU,CAAC,CAAC1X,OAAO,CAAC,UAACqC,IAAI,EAAK;MACxC,IAAIA,IAAI,CAACpH,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAIyD,IAAI,CAACgZ,UAAU,CAACrV,IAAI,CAAC,CAAC,EAAE;QACtDzB,KAAK,CAACC,IAAI,CAACwB,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;EACA,OAAOzB,KAAK;AACd;AAEA,SAAS+Z,UAAU,CAAEL,SAAS,EAAEvZ,IAAI,EAAE0Z,QAAQ,EAAE;EAC9C,IAAIA,QAAQ,CAACxf,OAAO,CAAC8F,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC9B,MAAM,CAACqb,SAAS,EAAEvZ,IAAI,CAAC,EAAE;IAC7DuZ,SAAS,CAACvZ,IAAI,CAAC,GAAG,UAAUyJ,IAAI,EAAE;MAChC,OAAO,IAAI,CAACxB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACuR,WAAW,CAACxZ,IAAI,EAAEyJ,IAAI,CAAC;IACrD,CAAC;EACH;AACF;AAEA,SAASoQ,gBAAgB,CAAE9c,GAAG,EAAE4Z,UAAU,EAAE;EAC1CA,UAAU,GAAGA,UAAU,CAACuC,OAAO,IAAIvC,UAAU;EAC7C,IAAImD,YAAY;EAChB,IAAInc,IAAI,CAACgZ,UAAU,CAAC,EAAE;IACpBmD,YAAY,GAAGnD,UAAU;EAC3B,CAAC,MAAM;IACLmD,YAAY,GAAG/c,GAAG,CAACgd,MAAM,CAACpD,UAAU,CAAC;EACvC;EACAA,UAAU,GAAGmD,YAAY,CAACzY,OAAO;EACjC,OAAO,CAACyY,YAAY,EAAEnD,UAAU,CAAC;AACnC;AAEA,SAASqD,SAAS,CAAE5F,EAAE,EAAE6F,QAAQ,EAAE;EAChC,IAAIva,KAAK,CAACC,OAAO,CAACsa,QAAQ,CAAC,IAAIA,QAAQ,CAAClf,MAAM,EAAE;IAC9C,IAAMmf,MAAM,GAAGzc,MAAM,CAACa,MAAM,CAAC,IAAI,CAAC;IAClC2b,QAAQ,CAAChb,OAAO,CAAC,UAAAkb,QAAQ,EAAI;MAC3BD,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI;IACzB,CAAC,CAAC;IACF/F,EAAE,CAACgG,YAAY,GAAGhG,EAAE,CAAC8F,MAAM,GAAGA,MAAM;EACtC;AACF;AAEA,SAASG,UAAU,CAAEC,MAAM,EAAEnF,UAAU,EAAE;EACvCmF,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAE7e,KAAK,CAAC,GAAG,CAAC;EAClC,IAAMwN,GAAG,GAAGqR,MAAM,CAACvf,MAAM;EAEzB,IAAIkO,GAAG,KAAK,CAAC,EAAE;IACbkM,UAAU,CAACV,OAAO,GAAG6F,MAAM,CAAC,CAAC,CAAC;EAChC,CAAC,MAAM,IAAIrR,GAAG,KAAK,CAAC,EAAE;IACpBkM,UAAU,CAACV,OAAO,GAAG6F,MAAM,CAAC,CAAC,CAAC;IAC9BnF,UAAU,CAACoF,QAAQ,GAAGD,MAAM,CAAC,CAAC,CAAC;EACjC;AACF;AAEA,SAASE,QAAQ,CAAE7D,UAAU,EAAE8D,OAAO,EAAE;EACtC,IAAI7Z,IAAI,GAAG+V,UAAU,CAAC/V,IAAI,IAAI,CAAC,CAAC;EAChC,IAAMyF,OAAO,GAAGsQ,UAAU,CAACtQ,OAAO,IAAI,CAAC,CAAC;EAExC,IAAI,OAAOzF,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAI;MACFA,IAAI,GAAGA,IAAI,CAAC3C,IAAI,CAACwc,OAAO,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAO9H,CAAC,EAAE;MACV,IAAIhG,+IAAW,CAAC+N,aAAa,EAAE;QAC7B9J,OAAO,CAACC,IAAI,CAAC,wEAAwE,EAAEjQ,IAAI,CAAC;MAC9F;IACF;EACF,CAAC,MAAM;IACL,IAAI;MACF;MACAA,IAAI,GAAGpE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACme,SAAS,CAAC/Z,IAAI,CAAC,CAAC;IACzC,CAAC,CAAC,OAAO+R,CAAC,EAAE,CAAE;EAChB;EAEA,IAAI,CAAC3U,aAAa,CAAC4C,IAAI,CAAC,EAAE;IACxBA,IAAI,GAAG,CAAC,CAAC;EACX;EAEAnD,MAAM,CAACsB,IAAI,CAACsH,OAAO,CAAC,CAACpH,OAAO,CAAC,UAAAoR,UAAU,EAAI;IACzC,IAAIoK,OAAO,CAACG,mBAAmB,CAAC1gB,OAAO,CAACmW,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAACnS,MAAM,CAAC0C,IAAI,EAAEyP,UAAU,CAAC,EAAE;MACvFzP,IAAI,CAACyP,UAAU,CAAC,GAAGhK,OAAO,CAACgK,UAAU,CAAC;IACxC;EACF,CAAC,CAAC;EAEF,OAAOzP,IAAI;AACb;AAEA,IAAMia,UAAU,GAAG,CAACngB,MAAM,EAAE6J,MAAM,EAAEuW,OAAO,EAAErd,MAAM,EAAEiC,KAAK,EAAE,IAAI,CAAC;AAEjE,SAASqb,cAAc,CAAEzZ,IAAI,EAAE;EAC7B,OAAO,SAAS0Z,QAAQ,CAAEC,MAAM,EAAEC,MAAM,EAAE;IACxC,IAAI,IAAI,CAACjT,GAAG,EAAE;MACZ,IAAI,CAACA,GAAG,CAAC3G,IAAI,CAAC,GAAG2Z,MAAM,CAAC,CAAC;IAC3B;EACF,CAAC;AACH;;AAEA,SAASE,aAAa,CAAExE,UAAU,EAAEhC,YAAY,EAAE;EAChD,IAAMyG,YAAY,GAAGzE,UAAU,CAAC0E,SAAS;EACzC,IAAMC,UAAU,GAAG3E,UAAU,CAAC4E,OAAO;EACrC,IAAMC,SAAS,GAAG7E,UAAU,CAAC0C,MAAM;EAEnC,IAAIoC,QAAQ,GAAG9E,UAAU,CAAC+E,KAAK;EAE/B,IAAI,CAACD,QAAQ,EAAE;IACb9E,UAAU,CAAC+E,KAAK,GAAGD,QAAQ,GAAG,EAAE;EAClC;EAEA,IAAMJ,SAAS,GAAG,EAAE;EACpB,IAAI3b,KAAK,CAACC,OAAO,CAACyb,YAAY,CAAC,EAAE;IAC/BA,YAAY,CAACnc,OAAO,CAAC,UAAA0c,QAAQ,EAAI;MAC/BN,SAAS,CAACvb,IAAI,CAAC6b,QAAQ,CAAChhB,OAAO,CAAC,QAAQ,EAAK,IAAI,eAAM,CAAC;MACxD,IAAIghB,QAAQ,KAAK,kBAAkB,EAAE;QACnC,IAAIjc,KAAK,CAACC,OAAO,CAAC8b,QAAQ,CAAC,EAAE;UAC3BA,QAAQ,CAAC3b,IAAI,CAAC,MAAM,CAAC;UACrB2b,QAAQ,CAAC3b,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC,MAAM;UACL2b,QAAQ,CAACna,IAAI,GAAG;YACd8R,IAAI,EAAE1Y,MAAM;YACZwe,OAAO,EAAE;UACX,CAAC;UACDuC,QAAQ,CAACxY,KAAK,GAAG;YACfmQ,IAAI,EAAE,CAAC1Y,MAAM,EAAE6J,MAAM,EAAEuW,OAAO,EAAEpb,KAAK,EAAEjC,MAAM,EAAEH,IAAI,CAAC;YACpD4b,OAAO,EAAE;UACX,CAAC;QACH;MACF;IACF,CAAC,CAAC;EACJ;EACA,IAAIlb,aAAa,CAACsd,UAAU,CAAC,IAAIA,UAAU,CAACI,KAAK,EAAE;IACjDL,SAAS,CAACvb,IAAI,CACZ6U,YAAY,CAAC;MACXiH,UAAU,EAAEC,cAAc,CAACP,UAAU,CAACI,KAAK,EAAE,IAAI;IACnD,CAAC,CAAC,CACH;EACH;EACA,IAAIhc,KAAK,CAACC,OAAO,CAAC6b,SAAS,CAAC,EAAE;IAC5BA,SAAS,CAACvc,OAAO,CAAC,UAAA6c,QAAQ,EAAI;MAC5B,IAAI9d,aAAa,CAAC8d,QAAQ,CAAC,IAAIA,QAAQ,CAACJ,KAAK,EAAE;QAC7CL,SAAS,CAACvb,IAAI,CACZ6U,YAAY,CAAC;UACXiH,UAAU,EAAEC,cAAc,CAACC,QAAQ,CAACJ,KAAK,EAAE,IAAI;QACjD,CAAC,CAAC,CACH;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAOL,SAAS;AAClB;AAEA,SAASU,aAAa,CAAEriB,GAAG,EAAE0Z,IAAI,EAAE4I,YAAY,EAAEC,IAAI,EAAE;EACrD;EACA,IAAIvc,KAAK,CAACC,OAAO,CAACyT,IAAI,CAAC,IAAIA,IAAI,CAACrY,MAAM,KAAK,CAAC,EAAE;IAC5C,OAAOqY,IAAI,CAAC,CAAC,CAAC;EAChB;EACA,OAAOA,IAAI;AACb;AAEA,SAASyI,cAAc,CAAEH,KAAK,EAA0C;EAAA,IAAxCQ,UAAU,uEAAG,KAAK;EAAA,IAAED,IAAI,uEAAG,EAAE;EAAA,IAAE5a,OAAO;EACpE,IAAMua,UAAU,GAAG,CAAC,CAAC;EACrB,IAAI,CAACM,UAAU,EAAE;IACfN,UAAU,CAACO,KAAK,GAAG;MACjB/I,IAAI,EAAE1Y,MAAM;MACZuI,KAAK,EAAE;IACT,CAAC;IACD;MACE,IAAK5B,OAAO,CAAC+a,WAAW,EAAE;QACxBR,UAAU,CAACS,gBAAgB,GAAG;UAC5BjJ,IAAI,EAAE,IAAI;UACVnQ,KAAK,EAAE;QACT,CAAC;QACD2Y,UAAU,CAACU,gBAAgB,GAAG;UAC5BlJ,IAAI,EAAE,IAAI;UACVnQ,KAAK,EAAE;QACT,CAAC;MACH;IACF;IACA;IACA2Y,UAAU,CAACW,mBAAmB,GAAG;MAC/BnJ,IAAI,EAAE1Y,MAAM;MACZuI,KAAK,EAAE;IACT,CAAC;IACD2Y,UAAU,CAAC3B,QAAQ,GAAG;MAAE;MACtB7G,IAAI,EAAE,IAAI;MACVnQ,KAAK,EAAE,EAAE;MACT+X,QAAQ,EAAE,kBAAUC,MAAM,EAAEC,MAAM,EAAE;QAClC,IAAMhB,MAAM,GAAGzc,MAAM,CAACa,MAAM,CAAC,IAAI,CAAC;QAClC2c,MAAM,CAAChc,OAAO,CAAC,UAAAkb,QAAQ,EAAI;UACzBD,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI;QACzB,CAAC,CAAC;QACF,IAAI,CAACqC,OAAO,CAAC;UACXtC,MAAM,EAANA;QACF,CAAC,CAAC;MACJ;IACF,CAAC;EACH;EACA,IAAIxa,KAAK,CAACC,OAAO,CAAC+b,KAAK,CAAC,EAAE;IAAE;IAC1BA,KAAK,CAACzc,OAAO,CAAC,UAAAvF,GAAG,EAAI;MACnBkiB,UAAU,CAACliB,GAAG,CAAC,GAAG;QAChB0Z,IAAI,EAAE,IAAI;QACV4H,QAAQ,EAAED,cAAc,CAACrhB,GAAG;MAC9B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIsE,aAAa,CAAC0d,KAAK,CAAC,EAAE;IAAE;IACjCje,MAAM,CAACsB,IAAI,CAAC2c,KAAK,CAAC,CAACzc,OAAO,CAAC,UAAAvF,GAAG,EAAI;MAChC,IAAM+iB,IAAI,GAAGf,KAAK,CAAChiB,GAAG,CAAC;MACvB,IAAIsE,aAAa,CAACye,IAAI,CAAC,EAAE;QAAE;QACzB,IAAIxZ,KAAK,GAAGwZ,IAAI,CAACvD,OAAO;QACxB,IAAIvb,IAAI,CAACsF,KAAK,CAAC,EAAE;UACfA,KAAK,GAAGA,KAAK,EAAE;QACjB;QAEAwZ,IAAI,CAACrJ,IAAI,GAAG2I,aAAa,CAACriB,GAAG,EAAE+iB,IAAI,CAACrJ,IAAI,CAAC;QAEzCwI,UAAU,CAACliB,GAAG,CAAC,GAAG;UAChB0Z,IAAI,EAAEyH,UAAU,CAAC3gB,OAAO,CAACuiB,IAAI,CAACrJ,IAAI,CAAC,KAAK,CAAC,CAAC,GAAGqJ,IAAI,CAACrJ,IAAI,GAAG,IAAI;UAC7DnQ,KAAK,EAALA,KAAK;UACL+X,QAAQ,EAAED,cAAc,CAACrhB,GAAG;QAC9B,CAAC;MACH,CAAC,MAAM;QAAE;QACP,IAAM0Z,IAAI,GAAG2I,aAAa,CAACriB,GAAG,EAAE+iB,IAAI,CAAC;QACrCb,UAAU,CAACliB,GAAG,CAAC,GAAG;UAChB0Z,IAAI,EAAEyH,UAAU,CAAC3gB,OAAO,CAACkZ,IAAI,CAAC,KAAK,CAAC,CAAC,GAAGA,IAAI,GAAG,IAAI;UACnD4H,QAAQ,EAAED,cAAc,CAACrhB,GAAG;QAC9B,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAOkiB,UAAU;AACnB;AAEA,SAASc,SAAS,CAAEhG,KAAK,EAAE;EACzB;EACA,IAAI;IACFA,KAAK,CAACiG,EAAE,GAAGngB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACme,SAAS,CAACjE,KAAK,CAAC,CAAC;EAC9C,CAAC,CAAC,OAAO/D,CAAC,EAAE,CAAE;EAEd+D,KAAK,CAACkG,eAAe,GAAGze,IAAI;EAC5BuY,KAAK,CAACmG,cAAc,GAAG1e,IAAI;EAE3BuY,KAAK,CAACld,MAAM,GAAGkd,KAAK,CAACld,MAAM,IAAI,CAAC,CAAC;EAEjC,IAAI,CAAC0E,MAAM,CAACwY,KAAK,EAAE,QAAQ,CAAC,EAAE;IAC5BA,KAAK,CAAC1B,MAAM,GAAG,CAAC,CAAC;EACnB;EAEA,IAAI9W,MAAM,CAACwY,KAAK,EAAE,UAAU,CAAC,EAAE;IAC7BA,KAAK,CAAC1B,MAAM,GAAG,sBAAO0B,KAAK,CAAC1B,MAAM,MAAK,QAAQ,GAAG0B,KAAK,CAAC1B,MAAM,GAAG,CAAC,CAAC;IACnE0B,KAAK,CAAC1B,MAAM,CAAC8H,QAAQ,GAAGpG,KAAK,CAACoG,QAAQ;EACxC;EAEA,IAAI9e,aAAa,CAAC0Y,KAAK,CAAC1B,MAAM,CAAC,EAAE;IAC/B0B,KAAK,CAACld,MAAM,GAAGiE,MAAM,CAAC+F,MAAM,CAAC,CAAC,CAAC,EAAEkT,KAAK,CAACld,MAAM,EAAEkd,KAAK,CAAC1B,MAAM,CAAC;EAC9D;EAEA,OAAO0B,KAAK;AACd;AAEA,SAASqG,aAAa,CAAE3I,EAAE,EAAE4I,cAAc,EAAE;EAC1C,IAAIvC,OAAO,GAAGrG,EAAE;EAChB4I,cAAc,CAAC/d,OAAO,CAAC,UAAAge,aAAa,EAAI;IACtC,IAAMC,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;IACjC,IAAMha,KAAK,GAAGga,aAAa,CAAC,CAAC,CAAC;IAC9B,IAAIC,QAAQ,IAAI,OAAOja,KAAK,KAAK,WAAW,EAAE;MAAE;MAC9C,IAAMka,QAAQ,GAAGF,aAAa,CAAC,CAAC,CAAC;MACjC,IAAMG,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC;MAElC,IAAII,IAAI;MACR,IAAI9Y,MAAM,CAAC+Y,SAAS,CAACJ,QAAQ,CAAC,EAAE;QAC9BG,IAAI,GAAGH,QAAQ;MACjB,CAAC,MAAM,IAAI,CAACA,QAAQ,EAAE;QACpBG,IAAI,GAAG5C,OAAO;MAChB,CAAC,MAAM,IAAI,OAAOyC,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,EAAE;QACnD,IAAIA,QAAQ,CAAChjB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;UACjCmjB,IAAI,GAAGH,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC;QAC3B,CAAC,MAAM;UACLF,IAAI,GAAGjJ,EAAE,CAACoJ,WAAW,CAACN,QAAQ,EAAEzC,OAAO,CAAC;QAC1C;MACF;MAEA,IAAIlW,MAAM,CAAC+Y,SAAS,CAACD,IAAI,CAAC,EAAE;QAC1B5C,OAAO,GAAGxX,KAAK;MACjB,CAAC,MAAM,IAAI,CAACka,QAAQ,EAAE;QACpB1C,OAAO,GAAG4C,IAAI,CAACpa,KAAK,CAAC;MACvB,CAAC,MAAM;QACL,IAAIvD,KAAK,CAACC,OAAO,CAAC0d,IAAI,CAAC,EAAE;UACvB5C,OAAO,GAAG4C,IAAI,CAAC9V,IAAI,CAAC,UAAAkW,QAAQ,EAAI;YAC9B,OAAOrJ,EAAE,CAACoJ,WAAW,CAACL,QAAQ,EAAEM,QAAQ,CAAC,KAAKxa,KAAK;UACrD,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIjF,aAAa,CAACqf,IAAI,CAAC,EAAE;UAC9B5C,OAAO,GAAGhd,MAAM,CAACsB,IAAI,CAACse,IAAI,CAAC,CAAC9V,IAAI,CAAC,UAAAmW,OAAO,EAAI;YAC1C,OAAOtJ,EAAE,CAACoJ,WAAW,CAACL,QAAQ,EAAEE,IAAI,CAACK,OAAO,CAAC,CAAC,KAAKza,KAAK;UAC1D,CAAC,CAAC;QACJ,CAAC,MAAM;UACL2N,OAAO,CAAClU,KAAK,CAAC,iBAAiB,EAAE2gB,IAAI,CAAC;QACxC;MACF;MAEA,IAAID,SAAS,EAAE;QACb3C,OAAO,GAAGrG,EAAE,CAACoJ,WAAW,CAACJ,SAAS,EAAE3C,OAAO,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;EACF,OAAOA,OAAO;AAChB;AAEA,SAASkD,iBAAiB,CAAEvJ,EAAE,EAAEwJ,KAAK,EAAElH,KAAK,EAAEmH,QAAQ,EAAE;EACtD,IAAMC,QAAQ,GAAG,CAAC,CAAC;EAEnB,IAAIpe,KAAK,CAACC,OAAO,CAACie,KAAK,CAAC,IAAIA,KAAK,CAAC7iB,MAAM,EAAE;IACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI6iB,KAAK,CAAC3e,OAAO,CAAC,UAACie,QAAQ,EAAEjd,KAAK,EAAK;MACjC,IAAI,OAAOid,QAAQ,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACA,QAAQ,EAAE;UAAE;UACfY,QAAQ,CAAC,GAAG,GAAG7d,KAAK,CAAC,GAAGmU,EAAE;QAC5B,CAAC,MAAM;UACL,IAAI8I,QAAQ,KAAK,QAAQ,EAAE;YAAE;YAC3BY,QAAQ,CAAC,GAAG,GAAG7d,KAAK,CAAC,GAAGyW,KAAK;UAC/B,CAAC,MAAM,IAAIwG,QAAQ,KAAK,WAAW,EAAE;YACnCY,QAAQ,CAAC,GAAG,GAAG7d,KAAK,CAAC,GAAGyW,KAAK,CAAC1B,MAAM,GAAG0B,KAAK,CAAC1B,MAAM,CAAC6I,QAAQ,IAAIA,QAAQ,GAAGA,QAAQ;UACrF,CAAC,MAAM,IAAIX,QAAQ,CAAChjB,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAAE;YAC9C4jB,QAAQ,CAAC,GAAG,GAAG7d,KAAK,CAAC,GAAGmU,EAAE,CAACoJ,WAAW,CAACN,QAAQ,CAACviB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE+b,KAAK,CAAC;UAChF,CAAC,MAAM;YACLoH,QAAQ,CAAC,GAAG,GAAG7d,KAAK,CAAC,GAAGmU,EAAE,CAACoJ,WAAW,CAACN,QAAQ,CAAC;UAClD;QACF;MACF,CAAC,MAAM;QACLY,QAAQ,CAAC,GAAG,GAAG7d,KAAK,CAAC,GAAG8c,aAAa,CAAC3I,EAAE,EAAE8I,QAAQ,CAAC;MACrD;IACF,CAAC,CAAC;EACJ;EAEA,OAAOY,QAAQ;AACjB;AAEA,SAASC,aAAa,CAAEC,GAAG,EAAE;EAC3B,IAAMjgB,GAAG,GAAG,CAAC,CAAC;EACd,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4iB,GAAG,CAACjjB,MAAM,EAAEK,CAAC,EAAE,EAAE;IACnC,IAAM6iB,OAAO,GAAGD,GAAG,CAAC5iB,CAAC,CAAC;IACtB2C,GAAG,CAACkgB,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EAC9B;EACA,OAAOlgB,GAAG;AACZ;AAEA,SAASmgB,gBAAgB,CAAE9J,EAAE,EAAEsC,KAAK,EAA+C;EAAA,IAA7CjN,IAAI,uEAAG,EAAE;EAAA,IAAEmU,KAAK,uEAAG,EAAE;EAAA,IAAEO,QAAQ;EAAA,IAAE9N,UAAU;EAC/E,IAAI+N,eAAe,GAAG,KAAK,CAAC,CAAC;;EAE7B;EACA,IAAMP,QAAQ,GAAG7f,aAAa,CAAC0Y,KAAK,CAAC1B,MAAM,CAAC,GACxC0B,KAAK,CAAC1B,MAAM,CAAC6I,QAAQ,IAAI,CAACnH,KAAK,CAAC1B,MAAM,CAAC,GACvC,CAAC0B,KAAK,CAAC1B,MAAM,CAAC;EAElB,IAAImJ,QAAQ,EAAE;IAAE;IACdC,eAAe,GAAG1H,KAAK,CAAC2H,aAAa,IACnC3H,KAAK,CAAC2H,aAAa,CAAC5I,OAAO,IAC3BiB,KAAK,CAAC2H,aAAa,CAAC5I,OAAO,CAAC2C,OAAO,KAAK,IAAI;IAC9C,IAAI,CAAC3O,IAAI,CAAC1O,MAAM,EAAE;MAAE;MAClB,IAAIqjB,eAAe,EAAE;QACnB,OAAO,CAAC1H,KAAK,CAAC;MAChB;MACA,OAAOmH,QAAQ;IACjB;EACF;EAEA,IAAMC,QAAQ,GAAGH,iBAAiB,CAACvJ,EAAE,EAAEwJ,KAAK,EAAElH,KAAK,EAAEmH,QAAQ,CAAC;EAE9D,IAAMS,GAAG,GAAG,EAAE;EACd7U,IAAI,CAACxK,OAAO,CAAC,UAAAsf,GAAG,EAAI;IAClB,IAAIA,GAAG,KAAK,QAAQ,EAAE;MACpB,IAAIlO,UAAU,KAAK,aAAa,IAAI,CAAC8N,QAAQ,EAAE;QAAE;QAC/CG,GAAG,CAACxe,IAAI,CAAC4W,KAAK,CAACld,MAAM,CAACyJ,KAAK,CAAC;MAC9B,CAAC,MAAM;QACL,IAAIkb,QAAQ,IAAI,CAACC,eAAe,EAAE;UAChCE,GAAG,CAACxe,IAAI,CAAC+d,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,MAAM;UAAE;UACPS,GAAG,CAACxe,IAAI,CAAC4W,KAAK,CAAC;QACjB;MACF;IACF,CAAC,MAAM;MACL,IAAIhX,KAAK,CAACC,OAAO,CAAC4e,GAAG,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACxCD,GAAG,CAACxe,IAAI,CAACie,aAAa,CAACQ,GAAG,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIrgB,MAAM,CAAC4f,QAAQ,EAAES,GAAG,CAAC,EAAE;QAC3DD,GAAG,CAACxe,IAAI,CAACge,QAAQ,CAACS,GAAG,CAAC,CAAC;MACzB,CAAC,MAAM;QACLD,GAAG,CAACxe,IAAI,CAACye,GAAG,CAAC;MACf;IACF;EACF,CAAC,CAAC;EAEF,OAAOD,GAAG;AACZ;AAEA,IAAME,IAAI,GAAG,GAAG;AAChB,IAAMC,MAAM,GAAG,GAAG;AAElB,SAASC,gBAAgB,CAAEC,SAAS,EAAEC,OAAO,EAAE;EAC7C,OAAQD,SAAS,KAAKC,OAAO,IAEzBA,OAAO,KAAK,cAAc,KAExBD,SAAS,KAAK,OAAO,IACrBA,SAAS,KAAK,KAAK,CAEtB;AACL;AAEA,SAASE,YAAY,CAAEzK,EAAE,EAAE;EACzB,IAAI0K,OAAO,GAAG1K,EAAE,CAAC0K,OAAO;EACxB;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACA,OAAO,KAAKA,OAAO,CAACC,QAAQ,CAACC,OAAO,IAAIF,OAAO,CAACA,OAAO,CAACC,QAAQ,CAACC,OAAO,IAAIF,OAAO,CAACtK,MAAM,CAAC+F,QAAQ,CAAC,EAAE;IAC9HuE,OAAO,GAAGA,OAAO,CAACA,OAAO;EAC3B;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACA,OAAO;AACnC;AAEA,SAASG,WAAW,CAAEvI,KAAK,EAAE;EAAA;EAC3BA,KAAK,GAAGgG,SAAS,CAAChG,KAAK,CAAC;;EAExB;EACA,IAAMjB,OAAO,GAAG,CAACiB,KAAK,CAAC2H,aAAa,IAAI3H,KAAK,CAACld,MAAM,EAAEic,OAAO;EAC7D,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO7E,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;EAChC;EACA,IAAMqO,SAAS,GAAGzJ,OAAO,CAACyJ,SAAS,IAAIzJ,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;EAC9D,IAAI,CAACyJ,SAAS,EAAE;IACd,OAAOtO,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;EAChC;;EAEA;EACA,IAAM8N,SAAS,GAAGjI,KAAK,CAACtD,IAAI;EAE5B,IAAMkL,GAAG,GAAG,EAAE;EAEdY,SAAS,CAACjgB,OAAO,CAAC,UAAAkgB,QAAQ,EAAI;IAC5B,IAAI/L,IAAI,GAAG+L,QAAQ,CAAC,CAAC,CAAC;IACtB,IAAMC,WAAW,GAAGD,QAAQ,CAAC,CAAC,CAAC;IAE/B,IAAMhB,QAAQ,GAAG/K,IAAI,CAAC/X,MAAM,CAAC,CAAC,CAAC,KAAKojB,MAAM;IAC1CrL,IAAI,GAAG+K,QAAQ,GAAG/K,IAAI,CAACtY,KAAK,CAAC,CAAC,CAAC,GAAGsY,IAAI;IACtC,IAAMiM,MAAM,GAAGjM,IAAI,CAAC/X,MAAM,CAAC,CAAC,CAAC,KAAKmjB,IAAI;IACtCpL,IAAI,GAAGiM,MAAM,GAAGjM,IAAI,CAACtY,KAAK,CAAC,CAAC,CAAC,GAAGsY,IAAI;IAEpC,IAAIgM,WAAW,IAAIV,gBAAgB,CAACC,SAAS,EAAEvL,IAAI,CAAC,EAAE;MACpDgM,WAAW,CAACngB,OAAO,CAAC,UAAAqgB,UAAU,EAAI;QAChC,IAAMjP,UAAU,GAAGiP,UAAU,CAAC,CAAC,CAAC;QAChC,IAAIjP,UAAU,EAAE;UACd,IAAIkP,UAAU,GAAG,MAAI,CAACtX,GAAG;UACzB,IAAIsX,UAAU,CAACR,QAAQ,CAACC,OAAO,EAAE;YAAE;YACjCO,UAAU,GAAGV,YAAY,CAACU,UAAU,CAAC,IAAIA,UAAU;UACrD;UACA,IAAIlP,UAAU,KAAK,OAAO,EAAE;YAC1BkP,UAAU,CAAC/M,KAAK,CAACrB,KAAK,CAACoO,UAAU,EAC/BrB,gBAAgB,CACd,MAAI,CAACjW,GAAG,EACRyO,KAAK,EACL4I,UAAU,CAAC,CAAC,CAAC,EACbA,UAAU,CAAC,CAAC,CAAC,EACbnB,QAAQ,EACR9N,UAAU,CACX,CAAC;YACJ;UACF;UACA,IAAMmP,OAAO,GAAGD,UAAU,CAAClP,UAAU,CAAC;UACtC,IAAI,CAAC1S,IAAI,CAAC6hB,OAAO,CAAC,EAAE;YAClB,IAAMpM,KAAI,GAAG,MAAI,CAACnL,GAAG,CAAC8Q,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,WAAW;YAC9D,IAAM0G,IAAI,GAAG,MAAI,CAAC3K,KAAK,IAAI,MAAI,CAAC4K,EAAE;YAClC,MAAM,IAAI7kB,KAAK,WAAIuY,KAAI,gBAAKqM,IAAI,yCAA6BpP,UAAU,QAAI;UAC7E;UACA,IAAIgP,MAAM,EAAE;YACV,IAAIG,OAAO,CAACG,IAAI,EAAE;cAChB;YACF;YACAH,OAAO,CAACG,IAAI,GAAG,IAAI;UACrB;UACA,IAAIhf,MAAM,GAAGud,gBAAgB,CAC3B,MAAI,CAACjW,GAAG,EACRyO,KAAK,EACL4I,UAAU,CAAC,CAAC,CAAC,EACbA,UAAU,CAAC,CAAC,CAAC,EACbnB,QAAQ,EACR9N,UAAU,CACX;UACD1P,MAAM,GAAGjB,KAAK,CAACC,OAAO,CAACgB,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;UAC5C;UACA,IAAI,2DAA2D,CAAC/F,IAAI,CAAC4kB,OAAO,CAAC3jB,QAAQ,EAAE,CAAC,EAAE;YACxF;YACA8E,MAAM,GAAGA,MAAM,CAAClB,MAAM,CAAC,YAAqBiX,KAAK,CAAC,CAAC;UACrD;UACA4H,GAAG,CAACxe,IAAI,CAAC0f,OAAO,CAACrO,KAAK,CAACoO,UAAU,EAAE5e,MAAM,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,IACEge,SAAS,KAAK,OAAO,IACrBL,GAAG,CAACvjB,MAAM,KAAK,CAAC,IAChB,OAAOujB,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAC7B;IACA,OAAOA,GAAG,CAAC,CAAC,CAAC;EACf;AACF;AAEA,IAAMsB,aAAa,GAAG,CAAC,CAAC;AAExB,SAASC,eAAe,CAAEC,EAAE,EAAE;EAC5B,IAAMC,YAAY,GAAGH,aAAa,CAACE,EAAE,CAAC;EACtC,OAAOF,aAAa,CAACE,EAAE,CAAC;EACxB,OAAOC,YAAY;AACrB;AAEA,IAAMlgB,KAAK,GAAG,CACZ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,eAAe,EACf,sBAAsB,CACvB;AAED,SAASmgB,gBAAgB,GAAI;EAC3BjjB,YAAG,CAACC,SAAS,CAACijB,qBAAqB,GAAG,YAAY;IAChD;IACA;MACE,OAAO,IAAI,CAACzL,MAAM,CAACyL,qBAAqB,EAAE;IAC5C;EACF,CAAC;EACD,IAAMC,QAAQ,GAAGnjB,YAAG,CAACC,SAAS,CAACwc,WAAW;EAC1Czc,YAAG,CAACC,SAAS,CAACwc,WAAW,GAAG,UAAUxZ,IAAI,EAAEyJ,IAAI,EAAE;IAChD,IAAIzJ,IAAI,KAAK,QAAQ,IAAIyJ,IAAI,IAAIA,IAAI,CAAC0W,MAAM,EAAE;MAC5C,IAAI,CAACC,gBAAgB,GAAGP,eAAe,CAACpW,IAAI,CAAC0W,MAAM,CAAC;MACpD,OAAO1W,IAAI,CAAC0W,MAAM;IACpB;IACA,OAAOD,QAAQ,CAACjiB,IAAI,CAAC,IAAI,EAAE+B,IAAI,EAAEyJ,IAAI,CAAC;EACxC,CAAC;AACH;AAEA,SAAS4W,qBAAqB,GAAI;EAChC,IAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAMC,OAAO,GAAG,CAAC,CAAC;EAElB,SAASC,SAAS,CAAE5iB,EAAE,EAAE;IACtB,IAAM0c,MAAM,GAAG,IAAI,CAACyE,QAAQ,CAAC0B,SAAS,CAACtE,KAAK;IAC5C,IAAI7B,MAAM,EAAE;MACV,IAAM6B,KAAK,GAAG7B,MAAM,CAAC7e,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClCmC,EAAE,CAACue,KAAK,CAAC;IACX;EACF;EAEApf,YAAG,CAACC,SAAS,CAAC0jB,OAAO,GAAG,UAAUvE,KAAK,EAAE;IACvC,IAAMwE,IAAI,GAAGL,MAAM,CAACnE,KAAK,CAAC;IAC1B,IAAI,CAACwE,IAAI,EAAE;MACTJ,OAAO,CAACpE,KAAK,CAAC,GAAG,IAAI;MACrB,IAAI,CAAC9J,GAAG,CAAC,gBAAgB,EAAE,YAAM;QAC/B,OAAOkO,OAAO,CAACpE,KAAK,CAAC;MACvB,CAAC,CAAC;IACJ;IACA,OAAOwE,IAAI;EACb,CAAC;EAED5jB,YAAG,CAACC,SAAS,CAAC4jB,OAAO,GAAG,UAAUzE,KAAK,EAAE7a,IAAI,EAAEuf,OAAO,EAAE;IACtD,IAAMF,IAAI,GAAGL,MAAM,CAACnE,KAAK,CAAC;IAC1B,IAAIwE,IAAI,EAAE;MACR,IAAMhgB,MAAM,GAAGggB,IAAI,CAACrf,IAAI,CAAC,IAAI,EAAE;MAC/B,IAAIuf,OAAO,EAAE;QACX,OAAOlgB,MAAM;MACf;MACA,OAAOA,MAAM,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EAED5D,YAAG,CAACC,SAAS,CAAC8jB,OAAO,GAAG,UAAUxf,IAAI,EAAE2B,KAAK,EAAE;IAC7C,IAAIhD,KAAK,GAAG,CAAC;IACbugB,SAAS,CAACviB,IAAI,CAAC,IAAI,EAAE,UAAAke,KAAK,EAAI;MAC5B,IAAMwE,IAAI,GAAGL,MAAM,CAACnE,KAAK,CAAC;MAC1B,IAAMxb,MAAM,GAAGggB,IAAI,CAACrf,IAAI,CAAC,GAAGqf,IAAI,CAACrf,IAAI,CAAC,IAAI,EAAE;MAC5CX,MAAM,CAACb,IAAI,CAACmD,KAAK,CAAC;MAClBhD,KAAK,GAAGU,MAAM,CAAC5F,MAAM,GAAG,CAAC;IAC3B,CAAC,CAAC;IACF,OAAOkF,KAAK;EACd,CAAC;EAEDlD,YAAG,CAACC,SAAS,CAAC+jB,QAAQ,GAAG,YAAY;IACnCP,SAAS,CAACviB,IAAI,CAAC,IAAI,EAAE,UAAAke,KAAK,EAAI;MAC5BmE,MAAM,CAACnE,KAAK,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EAEDpf,YAAG,CAACC,SAAS,CAACgkB,QAAQ,GAAG,YAAY;IACnCR,SAAS,CAACviB,IAAI,CAAC,IAAI,EAAE,UAAAke,KAAK,EAAI;MAC5B,IAAIoE,OAAO,CAACpE,KAAK,CAAC,EAAE;QAClBoE,OAAO,CAACpE,KAAK,CAAC,CAAChW,YAAY,EAAE;MAC/B;IACF,CAAC,CAAC;EACJ,CAAC;EAEDpJ,YAAG,CAACgJ,KAAK,CAAC;IACRkb,SAAS,uBAAI;MACX,IAAMR,SAAS,GAAG,IAAI,CAAC1B,QAAQ,CAAC0B,SAAS;MACzC,IAAMtE,KAAK,GAAGsE,SAAS,IAAIA,SAAS,CAACtE,KAAK;MAC1C,IAAIA,KAAK,EAAE;QACT,OAAOmE,MAAM,CAACnE,KAAK,CAAC;QACpB,OAAOoE,OAAO,CAACpE,KAAK,CAAC;MACvB;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAAS+E,YAAY,CAAE9M,EAAE,SAGtB;EAAA,IAFDF,KAAK,SAALA,KAAK;IACLqC,QAAQ,SAARA,QAAQ;EAERyJ,gBAAgB,EAAE;EAClB;IACEK,qBAAqB,EAAE;EACzB;EACA,IAAIjM,EAAE,CAAC2K,QAAQ,CAACoC,KAAK,EAAE;IACrBpkB,YAAG,CAACC,SAAS,CAACokB,MAAM,GAAGhN,EAAE,CAAC2K,QAAQ,CAACoC,KAAK;EAC1C;EACArkB,UAAU,CAACC,YAAG,CAAC;EAEfA,YAAG,CAACC,SAAS,CAACqkB,MAAM,GAAG,WAAW;EAElCtkB,YAAG,CAACgJ,KAAK,CAAC;IACRC,YAAY,0BAAI;MACd,IAAI,CAAC,IAAI,CAAC+Y,QAAQ,CAAChG,MAAM,EAAE;QACzB;MACF;MAEA,IAAI,CAACA,MAAM,GAAG,IAAI,CAACgG,QAAQ,CAAChG,MAAM;MAElC,IAAI,CAACD,GAAG;QACNlY,IAAI,EAAE,CAAC;MAAC,GACP,IAAI,CAACmY,MAAM,EAAG,IAAI,CAACgG,QAAQ,CAAC5J,UAAU,CACxC;MAED,IAAI,CAACX,MAAM,GAAG,IAAI,CAACuK,QAAQ,CAAC5J,UAAU;MAEtC,OAAO,IAAI,CAAC4J,QAAQ,CAAChG,MAAM;MAC3B,OAAO,IAAI,CAACgG,QAAQ,CAAC5J,UAAU;MAC/B,IACI,IAAI,CAAC4D,MAAM,KAAK,MAAM,IACxB,OAAOjR,MAAM,KAAK,UAAU,EAC5B;QAAE;QACF,IAAMC,GAAG,GAAGD,MAAM,EAAE;QACpB,IAAIC,GAAG,CAACE,GAAG,IAAIF,GAAG,CAACE,GAAG,CAACqZ,KAAK,EAAE;UAC5B,IAAI,CAACC,KAAK,GAAGxZ,GAAG,CAACE,GAAG,CAACqZ,KAAK;QAC5B;MACF;MACA,IAAI,IAAI,CAACvI,MAAM,KAAK,KAAK,EAAE;QACzBxC,QAAQ,CAAC,IAAI,CAAC;QACdsC,SAAS,CAAC,IAAI,EAAE3E,KAAK,CAAC;MACxB;IACF;EACF,CAAC,CAAC;EAEF,IAAMsN,UAAU,GAAG;IACjBC,QAAQ,oBAAEhY,IAAI,EAAE;MACd,IAAI,IAAI,CAACxB,GAAG,EAAE;QAAE;QACd;MACF;MACA;QACE,IAAIjM,EAAE,CAAC0lB,OAAO,IAAI,CAAC1lB,EAAE,CAAC0lB,OAAO,CAAC,UAAU,CAAC,EAAE;UAAE;UAC3C9Q,OAAO,CAAClU,KAAK,CAAC,qDAAqD,CAAC;QACtE;MACF;MAEA,IAAI,CAACuL,GAAG,GAAGmM,EAAE;MAEb,IAAI,CAACnM,GAAG,CAAC6Q,GAAG,GAAG;QACb/Q,GAAG,EAAE;MACP,CAAC;MAED,IAAI,CAACE,GAAG,CAACuM,MAAM,GAAG,IAAI;MACtB;MACA,IAAI,CAACvM,GAAG,CAAC0Z,UAAU,GAAG,IAAI,CAACA,UAAU;MAErC,IAAI,CAAC1Z,GAAG,CAAC2Z,UAAU,GAAG,IAAI;MAC1B,IAAI,CAAC3Z,GAAG,CAACuR,WAAW,CAAC,SAAS,EAAE/P,IAAI,CAAC;MAErC,IAAI,CAACxB,GAAG,CAACuR,WAAW,CAAC,UAAU,EAAE/P,IAAI,CAAC;IACxC;EACF,CAAC;;EAED;EACA+X,UAAU,CAACG,UAAU,GAAGvN,EAAE,CAAC2K,QAAQ,CAAC4C,UAAU,IAAI,CAAC,CAAC;EACpD;EACA,IAAMtb,OAAO,GAAG+N,EAAE,CAAC2K,QAAQ,CAAC1Y,OAAO;EACnC,IAAIA,OAAO,EAAE;IACX5I,MAAM,CAACsB,IAAI,CAACsH,OAAO,CAAC,CAACpH,OAAO,CAAC,UAAAqC,IAAI,EAAI;MACnCkgB,UAAU,CAAClgB,IAAI,CAAC,GAAG+E,OAAO,CAAC/E,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ;EAEAoF,aAAa,CAAC3J,YAAG,EAAEqX,EAAE,EAAGnP,eAAe,CAACjJ,EAAE,CAACkJ,cAAc,EAAE,CAACC,QAAQ,CAAC,IAAIP,SAAS,CAC/E;EAEH0U,SAAS,CAACkI,UAAU,EAAE3hB,KAAK,CAAC;EAC5B4Z,gBAAgB,CAAC+H,UAAU,EAAEpN,EAAE,CAAC2K,QAAQ,CAAC;EAEzC,OAAOyC,UAAU;AACnB;AAEA,SAASK,QAAQ,CAAEzN,EAAE,EAAE;EACrB,OAAO8M,YAAY,CAAC9M,EAAE,EAAE;IACtBF,KAAK,EAALA,KAAK;IACLqC,QAAQ,EAARA;EACF,CAAC,CAAC;AACJ;AAEA,SAASuL,SAAS,CAAE1N,EAAE,EAAE;EACtB2N,GAAG,CAACF,QAAQ,CAACzN,EAAE,CAAC,CAAC;EACjB,OAAOA,EAAE;AACX;AAEA,IAAM4N,eAAe,GAAG,UAAU;AAClC,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqB,CAAGtmB,CAAC;EAAA,OAAI,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;AAAA;AACrE,IAAMqmB,OAAO,GAAG,MAAM;;AAEtB;AACA;AACA;AACA,IAAMC,MAAM,GAAG,SAATA,MAAM,CAAG1nB,GAAG;EAAA,OAAI2nB,kBAAkB,CAAC3nB,GAAG,CAAC,CAC1CE,OAAO,CAACqnB,eAAe,EAAEC,qBAAqB,CAAC,CAC/CtnB,OAAO,CAACunB,OAAO,EAAE,GAAG,CAAC;AAAA;AAExB,SAASG,cAAc,CAAEtkB,GAAG,EAAsB;EAAA,IAApBukB,SAAS,uEAAGH,MAAM;EAC9C,IAAM3iB,GAAG,GAAGzB,GAAG,GAAGN,MAAM,CAACsB,IAAI,CAAChB,GAAG,CAAC,CAACrC,GAAG,CAAC,UAAAhC,GAAG,EAAI;IAC5C,IAAM6oB,GAAG,GAAGxkB,GAAG,CAACrE,GAAG,CAAC;IAEpB,IAAI6oB,GAAG,KAAKxU,SAAS,EAAE;MACrB,OAAO,EAAE;IACX;IAEA,IAAIwU,GAAG,KAAK,IAAI,EAAE;MAChB,OAAOD,SAAS,CAAC5oB,GAAG,CAAC;IACvB;IAEA,IAAIgG,KAAK,CAACC,OAAO,CAAC4iB,GAAG,CAAC,EAAE;MACtB,IAAMtnB,MAAM,GAAG,EAAE;MACjBsnB,GAAG,CAACtjB,OAAO,CAAC,UAAAujB,IAAI,EAAI;QAClB,IAAIA,IAAI,KAAKzU,SAAS,EAAE;UACtB;QACF;QACA,IAAIyU,IAAI,KAAK,IAAI,EAAE;UACjBvnB,MAAM,CAAC6E,IAAI,CAACwiB,SAAS,CAAC5oB,GAAG,CAAC,CAAC;QAC7B,CAAC,MAAM;UACLuB,MAAM,CAAC6E,IAAI,CAACwiB,SAAS,CAAC5oB,GAAG,CAAC,GAAG,GAAG,GAAG4oB,SAAS,CAACE,IAAI,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MACF,OAAOvnB,MAAM,CAACtB,IAAI,CAAC,GAAG,CAAC;IACzB;IAEA,OAAO2oB,SAAS,CAAC5oB,GAAG,CAAC,GAAG,GAAG,GAAG4oB,SAAS,CAACC,GAAG,CAAC;EAC9C,CAAC,CAAC,CAACtY,MAAM,CAAC,UAAAwY,CAAC;IAAA,OAAIA,CAAC,CAAC1nB,MAAM,GAAG,CAAC;EAAA,EAAC,CAACpB,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;EAC7C,OAAO6F,GAAG,cAAOA,GAAG,IAAK,EAAE;AAC7B;AAEA,SAASkjB,kBAAkB,CAAEC,mBAAmB,EAGxB;EAAA,gFAApB,CAAC,CAAC;IAFJ9N,MAAM,SAANA,MAAM;IACNE,YAAY,SAAZA,YAAY;EAAA,IACN6N,cAAc;EACpB,wBAAmC/I,gBAAgB,CAAC9c,YAAG,EAAE4lB,mBAAmB,CAAC;IAAA;IAAtE7I,YAAY;IAAEnD,UAAU;EAE/B,IAAMtV,OAAO;IACXwhB,aAAa,EAAE,IAAI;IACnB;IACAC,cAAc,EAAE;EAAI,GAChBnM,UAAU,CAACtV,OAAO,IAAI,CAAC,CAAC,CAC7B;EAED;IACE;IACA,IAAIsV,UAAU,CAAC,WAAW,CAAC,IAAIA,UAAU,CAAC,WAAW,CAAC,CAACtV,OAAO,EAAE;MAC9D5D,MAAM,CAAC+F,MAAM,CAACnC,OAAO,EAAEsV,UAAU,CAAC,WAAW,CAAC,CAACtV,OAAO,CAAC;IACzD;EACF;EAEA,IAAM0hB,gBAAgB,GAAG;IACvB1hB,OAAO,EAAPA,OAAO;IACPT,IAAI,EAAE4Z,QAAQ,CAAC7D,UAAU,EAAE5Z,YAAG,CAACC,SAAS,CAAC;IACzCqe,SAAS,EAAEF,aAAa,CAACxE,UAAU,EAAEhC,YAAY,CAAC;IAClDiH,UAAU,EAAEC,cAAc,CAAClF,UAAU,CAAC+E,KAAK,EAAE,KAAK,EAAE/E,UAAU,CAACqM,MAAM,EAAE3hB,OAAO,CAAC;IAC/E4hB,SAAS,EAAE;MACTC,QAAQ,sBAAI;QACV,IAAMtH,UAAU,GAAG,IAAI,CAACA,UAAU;QAElC,IAAMva,OAAO,GAAG;UACd0X,MAAM,EAAElE,MAAM,CAAC5W,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,WAAW;UAChDkX,UAAU,EAAE,IAAI;UAChBsL,SAAS,EAAE7E;QACb,CAAC;QAEDvB,UAAU,CAACuB,UAAU,CAACO,KAAK,EAAE,IAAI,CAAC;;QAElC;QACApH,YAAY,CAAC9W,IAAI,CAAC,IAAI,EAAE;UACtBoW,MAAM,EAAE,IAAI,CAACkG,QAAQ;UACrB5D,UAAU,EAAEtV;QACd,CAAC,CAAC;;QAEF;QACA,IAAI,CAAC4G,GAAG,GAAG,IAAI6R,YAAY,CAACzY,OAAO,CAAC;;QAEpC;QACA2Y,SAAS,CAAC,IAAI,CAAC/R,GAAG,EAAE2T,UAAU,CAAC3B,QAAQ,CAAC;;QAExC;QACA,IAAI,CAAChS,GAAG,CAACkb,MAAM,EAAE;MACnB,CAAC;MACDC,KAAK,mBAAI;QACP;QACA;QACA,IAAI,IAAI,CAACnb,GAAG,EAAE;UACZ,IAAI,CAACA,GAAG,CAAC2Z,UAAU,GAAG,IAAI;UAC1B,IAAI,CAAC3Z,GAAG,CAACuR,WAAW,CAAC,SAAS,CAAC;UAC/B,IAAI,CAACvR,GAAG,CAACuR,WAAW,CAAC,SAAS,CAAC;QACjC;MACF,CAAC;MACD6J,QAAQ,sBAAI;QACV,IAAI,CAACpb,GAAG,IAAI,IAAI,CAACA,GAAG,CAACqb,QAAQ,EAAE;MACjC;IACF,CAAC;IACDC,aAAa,EAAE;MACbC,IAAI,gBAAE/Z,IAAI,EAAE;QACV,IAAI,CAACxB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACuR,WAAW,CAAC,YAAY,EAAE/P,IAAI,CAAC;MACtD,CAAC;MACDga,IAAI,kBAAI;QACN,IAAI,CAACxb,GAAG,IAAI,IAAI,CAACA,GAAG,CAACuR,WAAW,CAAC,YAAY,CAAC;MAChD,CAAC;MACDkK,MAAM,kBAAEC,IAAI,EAAE;QACZ,IAAI,CAAC1b,GAAG,IAAI,IAAI,CAACA,GAAG,CAACuR,WAAW,CAAC,cAAc,EAAEmK,IAAI,CAAC;MACxD;IACF,CAAC;IACDtd,OAAO,EAAE;MACPud,GAAG,EAAEnN,UAAU;MACfoN,GAAG,EAAE5E;IACP;EACF,CAAC;EACD;EACA,IAAItI,UAAU,CAACmN,eAAe,EAAE;IAC9Bf,gBAAgB,CAACe,eAAe,GAAGnN,UAAU,CAACmN,eAAe;EAC/D;EAEA,IAAIpkB,KAAK,CAACC,OAAO,CAACgX,UAAU,CAACoN,cAAc,CAAC,EAAE;IAC5CpN,UAAU,CAACoN,cAAc,CAAC9kB,OAAO,CAAC,UAAA+kB,UAAU,EAAI;MAC9CjB,gBAAgB,CAAC1c,OAAO,CAAC2d,UAAU,CAAC,GAAG,UAAUva,IAAI,EAAE;QACrD,OAAO,IAAI,CAACxB,GAAG,CAAC+b,UAAU,CAAC,CAACva,IAAI,CAAC;MACnC,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,IAAImZ,cAAc,EAAE;IAClB,OAAO,CAACG,gBAAgB,EAAEpM,UAAU,EAAEmD,YAAY,CAAC;EACrD;EACA,IAAIjF,MAAM,EAAE;IACV,OAAOkO,gBAAgB;EACzB;EACA,OAAO,CAACA,gBAAgB,EAAEjJ,YAAY,CAAC;AACzC;AAEA,SAASmK,cAAc,CAAEtB,mBAAmB,EAAEC,cAAc,EAAE;EAC5D,OAAOF,kBAAkB,CAACC,mBAAmB,EAAE;IAC7C9N,MAAM,EAANA,MAAM;IACNE,YAAY,EAAZA;EACF,CAAC,EAAE6N,cAAc,CAAC;AACpB;AAEA,IAAMsB,OAAO,GAAG,CACd,QAAQ,EACR,QAAQ,EACR,UAAU,CACX;AAEDA,OAAO,CAACpkB,IAAI,OAAZokB,OAAO,EAAStL,gBAAgB,CAAC;AAEjC,SAASuL,aAAa,CAAEC,cAAc,EAAE;EACtC,sBAAkCH,cAAc,CAACG,cAAc,EAAE,IAAI,CAAC;IAAA;IAA/DC,WAAW;IAAE1N,UAAU;EAE9B2C,SAAS,CAAC+K,WAAW,CAAChe,OAAO,EAAE6d,OAAO,EAAEvN,UAAU,CAAC;EAEnD0N,WAAW,CAAChe,OAAO,CAACie,MAAM,GAAG,UAAUC,KAAK,EAAE;IAC5C,IAAI,CAACljB,OAAO,GAAGkjB,KAAK;IACpB,IAAMC,SAAS,GAAG/mB,MAAM,CAAC+F,MAAM,CAAC,CAAC,CAAC,EAAE+gB,KAAK,CAAC;IAC1C,OAAOC,SAAS,CAACrE,MAAM;IACvB,IAAI,CAAChX,KAAK,GAAG;MACXC,QAAQ,EAAE,GAAG,IAAI,IAAI,CAAC0L,KAAK,IAAI,IAAI,CAAC4K,EAAE,CAAC,GAAG2C,cAAc,CAACmC,SAAS;IACpE,CAAC;IACD,IAAI,CAACvc,GAAG,CAAC6Q,GAAG,CAACyL,KAAK,GAAGA,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACtc,GAAG,CAACuR,WAAW,CAAC,QAAQ,EAAE+K,KAAK,CAAC;EACvC,CAAC;EACD;IACE9K,gBAAgB,CAAC4K,WAAW,CAAChe,OAAO,EAAE+d,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC;EACpE;EACA;IACE/M,kBAAkB,CAACgN,WAAW,CAAChe,OAAO,EAAEsQ,UAAU,CAACtQ,OAAO,CAAC;EAC7D;EAEA,OAAOge,WAAW;AACpB;AAEA,SAASI,SAAS,CAAEL,cAAc,EAAE;EAClC,OAAOD,aAAa,CAACC,cAAc,CAAC;AACtC;AAEA,SAASM,UAAU,CAAEN,cAAc,EAAE;EACnC;IACE,OAAOtM,SAAS,CAAC2M,SAAS,CAACL,cAAc,CAAC,CAAC;EAC7C;AACF;AAEA,SAASO,eAAe,CAAEhO,UAAU,EAAE;EACpC;IACE,OAAOmB,SAAS,CAACmM,cAAc,CAACtN,UAAU,CAAC,CAAC;EAC9C;AACF;AAEA,SAASiO,mBAAmB,CAAExQ,EAAE,EAAE;EAChC,IAAMoN,UAAU,GAAGK,QAAQ,CAACzN,EAAE,CAAC;EAC/B,IAAMrM,GAAG,GAAGD,MAAM,CAAC;IACjBE,YAAY,EAAE;EAChB,CAAC,CAAC;EACFoM,EAAE,CAACI,MAAM,GAAGzM,GAAG;EACf,IAAM4Z,UAAU,GAAG5Z,GAAG,CAAC4Z,UAAU;EACjC,IAAIA,UAAU,EAAE;IACdlkB,MAAM,CAACsB,IAAI,CAACyiB,UAAU,CAACG,UAAU,CAAC,CAAC1iB,OAAO,CAAC,UAAAqC,IAAI,EAAI;MACjD,IAAI,CAACpD,MAAM,CAACyjB,UAAU,EAAErgB,IAAI,CAAC,EAAE;QAC7BqgB,UAAU,CAACrgB,IAAI,CAAC,GAAGkgB,UAAU,CAACG,UAAU,CAACrgB,IAAI,CAAC;MAChD;IACF,CAAC,CAAC;EACJ;EACA7D,MAAM,CAACsB,IAAI,CAACyiB,UAAU,CAAC,CAACviB,OAAO,CAAC,UAAAqC,IAAI,EAAI;IACtC,IAAI,CAACpD,MAAM,CAAC6J,GAAG,EAAEzG,IAAI,CAAC,EAAE;MACtByG,GAAG,CAACzG,IAAI,CAAC,GAAGkgB,UAAU,CAAClgB,IAAI,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,IAAI3D,IAAI,CAAC6jB,UAAU,CAACqD,MAAM,CAAC,IAAI7oB,EAAE,CAAC8oB,SAAS,EAAE;IAC3C9oB,EAAE,CAAC8oB,SAAS,CAAC,YAAa;MAAA,mCAATrb,IAAI;QAAJA,IAAI;MAAA;MACnB2K,EAAE,CAACoF,WAAW,CAAC,QAAQ,EAAE/P,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9L,IAAI,CAAC6jB,UAAU,CAACuD,MAAM,CAAC,IAAI/oB,EAAE,CAACgpB,SAAS,EAAE;IAC3ChpB,EAAE,CAACgpB,SAAS,CAAC,YAAa;MAAA,mCAATvb,IAAI;QAAJA,IAAI;MAAA;MACnB2K,EAAE,CAACoF,WAAW,CAAC,QAAQ,EAAE/P,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9L,IAAI,CAAC6jB,UAAU,CAACC,QAAQ,CAAC,EAAE;IAC7B,IAAMhY,IAAI,GAAGzN,EAAE,CAAClC,oBAAoB,IAAIkC,EAAE,CAAClC,oBAAoB,EAAE;IACjEsa,EAAE,CAACoF,WAAW,CAAC,UAAU,EAAE/P,IAAI,CAAC;EAClC;EACA,OAAO2K,EAAE;AACX;AAEA,SAAS6Q,YAAY,CAAE7Q,EAAE,EAAE;EACzB,IAAMoN,UAAU,GAAGK,QAAQ,CAACzN,EAAE,CAAC;EAC/B,IAAIzW,IAAI,CAAC6jB,UAAU,CAACqD,MAAM,CAAC,IAAI7oB,EAAE,CAAC8oB,SAAS,EAAE;IAC3C9oB,EAAE,CAAC8oB,SAAS,CAAC,YAAa;MAAA,mCAATrb,IAAI;QAAJA,IAAI;MAAA;MACnB2K,EAAE,CAACoF,WAAW,CAAC,QAAQ,EAAE/P,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9L,IAAI,CAAC6jB,UAAU,CAACuD,MAAM,CAAC,IAAI/oB,EAAE,CAACgpB,SAAS,EAAE;IAC3ChpB,EAAE,CAACgpB,SAAS,CAAC,YAAa;MAAA,mCAATvb,IAAI;QAAJA,IAAI;MAAA;MACnB2K,EAAE,CAACoF,WAAW,CAAC,QAAQ,EAAE/P,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9L,IAAI,CAAC6jB,UAAU,CAACC,QAAQ,CAAC,EAAE;IAC7B,IAAMhY,IAAI,GAAGzN,EAAE,CAAClC,oBAAoB,IAAIkC,EAAE,CAAClC,oBAAoB,EAAE;IACjEsa,EAAE,CAACoF,WAAW,CAAC,UAAU,EAAE/P,IAAI,CAAC;EAClC;EACA,OAAO2K,EAAE;AACX;AAEAnE,KAAK,CAAChR,OAAO,CAAC,UAAAsS,OAAO,EAAI;EACvBxB,SAAS,CAACwB,OAAO,CAAC,GAAG,KAAK;AAC5B,CAAC,CAAC;AAEFrB,QAAQ,CAACjR,OAAO,CAAC,UAAAimB,UAAU,EAAI;EAC7B,IAAMC,OAAO,GAAGpV,SAAS,CAACmV,UAAU,CAAC,IAAInV,SAAS,CAACmV,UAAU,CAAC,CAAC5jB,IAAI,GAAGyO,SAAS,CAACmV,UAAU,CAAC,CAAC5jB,IAAI,GAC5F4jB,UAAU;EACd,IAAI,CAAClpB,EAAE,CAAC0lB,OAAO,CAACyD,OAAO,CAAC,EAAE;IACxBpV,SAAS,CAACmV,UAAU,CAAC,GAAG,KAAK;EAC/B;AACF,CAAC,CAAC;AAEF,IAAIE,GAAG,GAAG,CAAC,CAAC;AAEZ,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAI,WAAW,KAAK,UAAU,EAAE;EAC9DD,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,EAAE;IAClBpe,GAAG,eAAEzN,MAAM,EAAE8H,IAAI,EAAE;MACjB,IAAIpD,MAAM,CAAC1E,MAAM,EAAE8H,IAAI,CAAC,EAAE;QACxB,OAAO9H,MAAM,CAAC8H,IAAI,CAAC;MACrB;MACA,IAAImH,OAAO,CAACnH,IAAI,CAAC,EAAE;QACjB,OAAOmH,OAAO,CAACnH,IAAI,CAAC;MACtB;MACA,IAAIS,GAAG,CAACT,IAAI,CAAC,EAAE;QACb,OAAO6B,SAAS,CAAC7B,IAAI,EAAES,GAAG,CAACT,IAAI,CAAC,CAAC;MACnC;MACA;QACE,IAAI0Q,QAAQ,CAAC1Q,IAAI,CAAC,EAAE;UAClB,OAAO6B,SAAS,CAAC7B,IAAI,EAAE0Q,QAAQ,CAAC1Q,IAAI,CAAC,CAAC;QACxC;QACA,IAAI8P,QAAQ,CAAC9P,IAAI,CAAC,EAAE;UAClB,OAAO6B,SAAS,CAAC7B,IAAI,EAAE8P,QAAQ,CAAC9P,IAAI,CAAC,CAAC;QACxC;MACF;MACA,IAAImR,QAAQ,CAACnR,IAAI,CAAC,EAAE;QAClB,OAAOmR,QAAQ,CAACnR,IAAI,CAAC;MACvB;MACA,OAAO6B,SAAS,CAAC7B,IAAI,EAAEyP,OAAO,CAACzP,IAAI,EAAEtF,EAAE,CAACsF,IAAI,CAAC,CAAC,CAAC;IACjD,CAAC;IACD4F,GAAG,eAAE1N,MAAM,EAAE8H,IAAI,EAAE2B,KAAK,EAAE;MACxBzJ,MAAM,CAAC8H,IAAI,CAAC,GAAG2B,KAAK;MACpB,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ,CAAC,MAAM;EACLxF,MAAM,CAACsB,IAAI,CAAC0J,OAAO,CAAC,CAACxJ,OAAO,CAAC,UAAAqC,IAAI,EAAI;IACnC8jB,GAAG,CAAC9jB,IAAI,CAAC,GAAGmH,OAAO,CAACnH,IAAI,CAAC;EAC3B,CAAC,CAAC;EAEF;IACE7D,MAAM,CAACsB,IAAI,CAACqS,QAAQ,CAAC,CAACnS,OAAO,CAAC,UAAAqC,IAAI,EAAI;MACpC8jB,GAAG,CAAC9jB,IAAI,CAAC,GAAG6B,SAAS,CAAC7B,IAAI,EAAE8P,QAAQ,CAAC9P,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;IACF7D,MAAM,CAACsB,IAAI,CAACiT,QAAQ,CAAC,CAAC/S,OAAO,CAAC,UAAAqC,IAAI,EAAI;MACpC8jB,GAAG,CAAC9jB,IAAI,CAAC,GAAG6B,SAAS,CAAC7B,IAAI,EAAE0Q,QAAQ,CAAC1Q,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;EAEA7D,MAAM,CAACsB,IAAI,CAAC0T,QAAQ,CAAC,CAACxT,OAAO,CAAC,UAAAqC,IAAI,EAAI;IACpC8jB,GAAG,CAAC9jB,IAAI,CAAC,GAAGmR,QAAQ,CAACnR,IAAI,CAAC;EAC5B,CAAC,CAAC;EAEF7D,MAAM,CAACsB,IAAI,CAACgD,GAAG,CAAC,CAAC9C,OAAO,CAAC,UAAAqC,IAAI,EAAI;IAC/B8jB,GAAG,CAAC9jB,IAAI,CAAC,GAAG6B,SAAS,CAAC7B,IAAI,EAAES,GAAG,CAACT,IAAI,CAAC,CAAC;EACxC,CAAC,CAAC;EAEF7D,MAAM,CAACsB,IAAI,CAAC/C,EAAE,CAAC,CAACiD,OAAO,CAAC,UAAAqC,IAAI,EAAI;IAC9B,IAAIpD,MAAM,CAAClC,EAAE,EAAEsF,IAAI,CAAC,IAAIpD,MAAM,CAAC6R,SAAS,EAAEzO,IAAI,CAAC,EAAE;MAC/C8jB,GAAG,CAAC9jB,IAAI,CAAC,GAAG6B,SAAS,CAAC7B,IAAI,EAAEyP,OAAO,CAACzP,IAAI,EAAEtF,EAAE,CAACsF,IAAI,CAAC,CAAC,CAAC;IACtD;EACF,CAAC,CAAC;AACJ;AAEAtF,EAAE,CAAC8lB,SAAS,GAAGA,SAAS;AACxB9lB,EAAE,CAAC0oB,UAAU,GAAGA,UAAU;AAC1B1oB,EAAE,CAAC2oB,eAAe,GAAGA,eAAe;AACpC3oB,EAAE,CAAC4oB,mBAAmB,GAAGA,mBAAmB;AAC5C5oB,EAAE,CAACipB,YAAY,GAAGA,YAAY;AAE9B,IAAIK,KAAK,GAAGF,GAAG;AAAC,eAEDE,KAAK;AAAA,2B;;;;;;;;;;;ACrlFpB;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;;;;ACnBA,SAASC,sBAAsB,CAACxnB,GAAG,EAAE;EACnC,OAAOA,GAAG,IAAIA,GAAG,CAACynB,UAAU,GAAGznB,GAAG,GAAG;IACnC,SAAS,EAAEA;EACb,CAAC;AACH;AACA0nB,MAAM,CAACC,OAAO,GAAGH,sBAAsB,EAAEE,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACLrH,IAAIC,cAAc,GAAGC,mBAAO,CAAC,4BAAqB,CAAC;AACnD,IAAIC,oBAAoB,GAAGD,mBAAO,CAAC,kCAA2B,CAAC;AAC/D,IAAIE,0BAA0B,GAAGF,mBAAO,CAAC,wCAAiC,CAAC;AAC3E,IAAIG,eAAe,GAAGH,mBAAO,CAAC,8BAAsB,CAAC;AACrD,SAASI,cAAc,CAAChI,GAAG,EAAE5iB,CAAC,EAAE;EAC9B,OAAOuqB,cAAc,CAAC3H,GAAG,CAAC,IAAI6H,oBAAoB,CAAC7H,GAAG,EAAE5iB,CAAC,CAAC,IAAI0qB,0BAA0B,CAAC9H,GAAG,EAAE5iB,CAAC,CAAC,IAAI2qB,eAAe,EAAE;AACvH;AACAN,MAAM,CAACC,OAAO,GAAGM,cAAc,EAAEP,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACP7G,SAASO,eAAe,CAACjI,GAAG,EAAE;EAC5B,IAAIte,KAAK,CAACC,OAAO,CAACqe,GAAG,CAAC,EAAE,OAAOA,GAAG;AACpC;AACAyH,MAAM,CAACC,OAAO,GAAGO,eAAe,EAAER,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACH9G,SAASQ,qBAAqB,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAIvgB,CAAC,GAAG,IAAI,IAAIsgB,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOE,MAAM,IAAIF,CAAC,CAACE,MAAM,CAACC,QAAQ,CAAC,IAAIH,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAItgB,CAAC,EAAE;IACb,IAAI8M,CAAC;MACH4T,CAAC;MACDnrB,CAAC;MACDorB,CAAC;MACDC,CAAC,GAAG,EAAE;MACNC,CAAC,GAAG,CAAC,CAAC;MACNC,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIvrB,CAAC,GAAG,CAACyK,CAAC,GAAGA,CAAC,CAAC5H,IAAI,CAACkoB,CAAC,CAAC,EAAES,IAAI,EAAE,CAAC,KAAKR,CAAC,EAAE;QACrC,IAAI3oB,MAAM,CAACoI,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrB6gB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAAC/T,CAAC,GAAGvX,CAAC,CAAC6C,IAAI,CAAC4H,CAAC,CAAC,EAAEghB,IAAI,CAAC,KAAKJ,CAAC,CAAC3mB,IAAI,CAAC6S,CAAC,CAAC1P,KAAK,CAAC,EAAEwjB,CAAC,CAAC1rB,MAAM,KAAKqrB,CAAC,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC;QAAC;MAAC;IAC1F,CAAC,CAAC,OAAOP,CAAC,EAAE;MACVQ,CAAC,GAAG,CAAC,CAAC,EAAEJ,CAAC,GAAGJ,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAACO,CAAC,IAAI,IAAI,IAAI7gB,CAAC,CAAC,QAAQ,CAAC,KAAK2gB,CAAC,GAAG3gB,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAEpI,MAAM,CAAC+oB,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAIG,CAAC,EAAE,MAAMJ,CAAC;MAChB;IACF;IACA,OAAOE,CAAC;EACV;AACF;AACAhB,MAAM,CAACC,OAAO,GAAGQ,qBAAqB,EAAET,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;AC3BpH,IAAIoB,gBAAgB,GAAGlB,mBAAO,CAAC,8BAAuB,CAAC;AACvD,SAASmB,2BAA2B,CAACJ,CAAC,EAAEK,MAAM,EAAE;EAC9C,IAAI,CAACL,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOG,gBAAgB,CAACH,CAAC,EAAEK,MAAM,CAAC;EAC7D,IAAIT,CAAC,GAAG9oB,MAAM,CAACT,SAAS,CAACnB,QAAQ,CAACoC,IAAI,CAAC0oB,CAAC,CAAC,CAAC7rB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIyrB,CAAC,KAAK,QAAQ,IAAII,CAAC,CAAC3jB,WAAW,EAAEujB,CAAC,GAAGI,CAAC,CAAC3jB,WAAW,CAAC1B,IAAI;EAC3D,IAAIilB,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAO7mB,KAAK,CAACunB,IAAI,CAACN,CAAC,CAAC;EACpD,IAAIJ,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAAC3rB,IAAI,CAAC2rB,CAAC,CAAC,EAAE,OAAOO,gBAAgB,CAACH,CAAC,EAAEK,MAAM,CAAC;AACjH;AACAvB,MAAM,CAACC,OAAO,GAAGqB,2BAA2B,EAAEtB,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACT1H,SAASwB,iBAAiB,CAAClJ,GAAG,EAAE/U,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAG+U,GAAG,CAACjjB,MAAM,EAAEkO,GAAG,GAAG+U,GAAG,CAACjjB,MAAM;EACrD,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAE+rB,IAAI,GAAG,IAAIznB,KAAK,CAACuJ,GAAG,CAAC,EAAE7N,CAAC,GAAG6N,GAAG,EAAE7N,CAAC,EAAE;IAAE+rB,IAAI,CAAC/rB,CAAC,CAAC,GAAG4iB,GAAG,CAAC5iB,CAAC,CAAC;EAAC;EACtE,OAAO+rB,IAAI;AACb;AACA1B,MAAM,CAACC,OAAO,GAAGwB,iBAAiB,EAAEzB,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACLhH,SAAS0B,gBAAgB,GAAG;EAC1B,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAClK;AACA5B,MAAM,CAACC,OAAO,GAAG0B,gBAAgB,EAAE3B,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACH/G,IAAI4B,aAAa,GAAG1B,mBAAO,CAAC,4BAAoB,CAAC;AACjD,SAAS2B,eAAe,CAACxpB,GAAG,EAAErE,GAAG,EAAEuJ,KAAK,EAAE;EACxCvJ,GAAG,GAAG4tB,aAAa,CAAC5tB,GAAG,CAAC;EACxB,IAAIA,GAAG,IAAIqE,GAAG,EAAE;IACdN,MAAM,CAACuJ,cAAc,CAACjJ,GAAG,EAAErE,GAAG,EAAE;MAC9BuJ,KAAK,EAAEA,KAAK;MACZ+T,UAAU,EAAE,IAAI;MAChBD,YAAY,EAAE,IAAI;MAClByQ,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLzpB,GAAG,CAACrE,GAAG,CAAC,GAAGuJ,KAAK;EAClB;EACA,OAAOlF,GAAG;AACZ;AACA0nB,MAAM,CAACC,OAAO,GAAG6B,eAAe,EAAE9B,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACf9G,IAAI+B,OAAO,GAAG7B,mBAAO,CAAC,qBAAa,CAAC,CAAC,SAAS,CAAC;AAC/C,IAAI8B,WAAW,GAAG9B,mBAAO,CAAC,0BAAkB,CAAC;AAC7C,SAAS0B,aAAa,CAACzhB,CAAC,EAAE;EACxB,IAAIzK,CAAC,GAAGssB,WAAW,CAAC7hB,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAI4hB,OAAO,CAACrsB,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AACAqqB,MAAM,CAACC,OAAO,GAAG4B,aAAa,EAAE7B,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACN5G,SAAS+B,OAAO,CAACd,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAO,CAAClB,MAAM,CAACC,OAAO,GAAG+B,OAAO,GAAG,UAAU,IAAI,OAAOpB,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUK,CAAC,EAAE;IAClH,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAON,MAAM,IAAIM,CAAC,CAAC3jB,WAAW,KAAKqjB,MAAM,IAAIM,CAAC,KAAKN,MAAM,CAACrpB,SAAS,GAAG,QAAQ,GAAG,OAAO2pB,CAAC;EACrH,CAAC,EAAElB,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,GAAG+B,OAAO,CAACd,CAAC,CAAC;AAC9F;AACAlB,MAAM,CAACC,OAAO,GAAG+B,OAAO,EAAEhC,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACTtG,IAAI+B,OAAO,GAAG7B,mBAAO,CAAC,qBAAa,CAAC,CAAC,SAAS,CAAC;AAC/C,SAAS8B,WAAW,CAAC7hB,CAAC,EAAEsgB,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIsB,OAAO,CAAC5hB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAI8M,CAAC,GAAG9M,CAAC,CAACwgB,MAAM,CAACqB,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAK/U,CAAC,EAAE;IAChB,IAAIvX,CAAC,GAAGuX,CAAC,CAAC1U,IAAI,CAAC4H,CAAC,EAAEsgB,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIsB,OAAO,CAACrsB,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIisB,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKlB,CAAC,GAAGzrB,MAAM,GAAG6J,MAAM,EAAEsB,CAAC,CAAC;AAC9C;AACA4f,MAAM,CAACC,OAAO,GAAGgC,WAAW,EAAEjC,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACX1G,IAAIiC,cAAc,GAAG/B,mBAAO,CAAC,6BAAqB,CAAC;AACnD,IAAIgC,wBAAwB,GAAGhC,mBAAO,CAAC,uCAA+B,CAAC;AACvE,SAASiC,UAAU,CAAChiB,CAAC,EAAE8M,CAAC,EAAEwT,CAAC,EAAE;EAC3B,IAAIyB,wBAAwB,EAAE,EAAE,OAAOE,OAAO,CAACC,SAAS,CAAC5W,KAAK,CAAC,IAAI,EAAEmB,SAAS,CAAC;EAC/E,IAAIqU,CAAC,GAAG,CAAC,IAAI,CAAC;EACdA,CAAC,CAAC7mB,IAAI,CAACqR,KAAK,CAACwV,CAAC,EAAEhU,CAAC,CAAC;EAClB,IAAIqV,CAAC,GAAG,KAAKniB,CAAC,CAACoiB,IAAI,CAAC9W,KAAK,CAACtL,CAAC,EAAE8gB,CAAC,CAAC,GAAG;EAClC,OAAOR,CAAC,IAAIwB,cAAc,CAACK,CAAC,EAAE7B,CAAC,CAACnpB,SAAS,CAAC,EAAEgrB,CAAC;AAC/C;AACAvC,MAAM,CAACC,OAAO,GAAGmC,UAAU,EAAEpC,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACTzG,SAASwC,eAAe,CAACvB,CAAC,EAAEqB,CAAC,EAAE;EAC7BvC,MAAM,CAACC,OAAO,GAAGwC,eAAe,GAAGzqB,MAAM,CAACkqB,cAAc,GAAGlqB,MAAM,CAACkqB,cAAc,CAACM,IAAI,EAAE,GAAG,SAASC,eAAe,CAACvB,CAAC,EAAEqB,CAAC,EAAE;IACvHrB,CAAC,CAAChe,SAAS,GAAGqf,CAAC;IACf,OAAOrB,CAAC;EACV,CAAC,EAAElB,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO;EAC/E,OAAOwC,eAAe,CAACvB,CAAC,EAAEqB,CAAC,CAAC;AAC9B;AACAvC,MAAM,CAACC,OAAO,GAAGwC,eAAe,EAAEzC,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACP9G,SAASyC,yBAAyB,GAAG;EACnC,IAAI;IACF,IAAItiB,CAAC,GAAG,CAACiV,OAAO,CAAC9d,SAAS,CAACorB,OAAO,CAACnqB,IAAI,CAAC6pB,OAAO,CAACC,SAAS,CAACjN,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,OAAOjV,CAAC,EAAE,CAAC;EACb,OAAO,CAAC4f,MAAM,CAACC,OAAO,GAAGyC,yBAAyB,GAAG,SAASA,yBAAyB,GAAG;IACxF,OAAO,CAAC,CAACtiB,CAAC;EACZ,CAAC,EAAE4f,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,GAAG;AACpF;AACAD,MAAM,CAACC,OAAO,GAAGyC,yBAAyB,EAAE1C,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACRxH,IAAI2C,iBAAiB,GAAGzC,mBAAO,CAAC,gCAAwB,CAAC;AACzD,IAAI0C,eAAe,GAAG1C,mBAAO,CAAC,8BAAsB,CAAC;AACrD,IAAIE,0BAA0B,GAAGF,mBAAO,CAAC,wCAAiC,CAAC;AAC3E,IAAI2C,iBAAiB,GAAG3C,mBAAO,CAAC,gCAAwB,CAAC;AACzD,SAAS4C,kBAAkB,CAACxK,GAAG,EAAE;EAC/B,OAAOqK,iBAAiB,CAACrK,GAAG,CAAC,IAAIsK,eAAe,CAACtK,GAAG,CAAC,IAAI8H,0BAA0B,CAAC9H,GAAG,CAAC,IAAIuK,iBAAiB,EAAE;AACjH;AACA9C,MAAM,CAACC,OAAO,GAAG8C,kBAAkB,EAAE/C,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACPjH,IAAIoB,gBAAgB,GAAGlB,mBAAO,CAAC,8BAAuB,CAAC;AACvD,SAAS6C,kBAAkB,CAACzK,GAAG,EAAE;EAC/B,IAAIte,KAAK,CAACC,OAAO,CAACqe,GAAG,CAAC,EAAE,OAAO8I,gBAAgB,CAAC9I,GAAG,CAAC;AACtD;AACAyH,MAAM,CAACC,OAAO,GAAG+C,kBAAkB,EAAEhD,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACJjH,SAASgD,gBAAgB,CAACC,IAAI,EAAE;EAC9B,IAAI,OAAOtC,MAAM,KAAK,WAAW,IAAIsC,IAAI,CAACtC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIqC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOjpB,KAAK,CAACunB,IAAI,CAAC0B,IAAI,CAAC;AAC3H;AACAlD,MAAM,CAACC,OAAO,GAAGgD,gBAAgB,EAAEjD,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACH/G,SAASkD,kBAAkB,GAAG;EAC5B,MAAM,IAAIvB,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AACA5B,MAAM,CAACC,OAAO,GAAGkD,kBAAkB,EAAEnD,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHjH,IAAM5nB,QAAQ,GAAG,SAAXA,QAAQ,CAAIykB,GAAG;EAAA,OAAKA,GAAG,KAAK,IAAI,IAAI,sBAAOA,GAAG,MAAK,QAAQ;AAAA;AACjE,IAAMsG,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAAC,IAC/BC,aAAa;EACf,yBAAc;IAAA;IACV,IAAI,CAACC,OAAO,GAAGtrB,MAAM,CAACa,MAAM,CAAC,IAAI,CAAC;EACtC;EAAC;IAAA;IAAA,OACD,qBAAY3B,OAAO,EAAE4J,MAAM,EAAkC;MAAA,IAAhCyiB,UAAU,uEAAGH,iBAAiB;MACvD,IAAI,CAACtiB,MAAM,EAAE;QACT,OAAO,CAAC5J,OAAO,CAAC;MACpB;MACA,IAAIssB,MAAM,GAAG,IAAI,CAACF,OAAO,CAACpsB,OAAO,CAAC;MAClC,IAAI,CAACssB,MAAM,EAAE;QACTA,MAAM,GAAGxsB,KAAK,CAACE,OAAO,EAAEqsB,UAAU,CAAC;QACnC,IAAI,CAACD,OAAO,CAACpsB,OAAO,CAAC,GAAGssB,MAAM;MAClC;MACA,OAAOC,OAAO,CAACD,MAAM,EAAE1iB,MAAM,CAAC;IAClC;EAAC;EAAA;AAAA;AAAA;AAEL,IAAM4iB,mBAAmB,GAAG,UAAU;AACtC,IAAMC,oBAAoB,GAAG,UAAU;AACvC,SAAS3sB,KAAK,CAAC4sB,MAAM,QAAkC;EAAA;IAA/BC,cAAc;IAAEC,YAAY;EAChD,IAAMN,MAAM,GAAG,EAAE;EACjB,IAAIO,QAAQ,GAAG,CAAC;EAChB,IAAIC,IAAI,GAAG,EAAE;EACb,OAAOD,QAAQ,GAAGH,MAAM,CAACtuB,MAAM,EAAE;IAC7B,IAAI2uB,IAAI,GAAGL,MAAM,CAACG,QAAQ,EAAE,CAAC;IAC7B,IAAIE,IAAI,KAAKJ,cAAc,EAAE;MACzB,IAAIG,IAAI,EAAE;QACNR,MAAM,CAACnpB,IAAI,CAAC;UAAEsT,IAAI,EAAE,MAAM;UAAEnQ,KAAK,EAAEwmB;QAAK,CAAC,CAAC;MAC9C;MACAA,IAAI,GAAG,EAAE;MACT,IAAIE,GAAG,GAAG,EAAE;MACZD,IAAI,GAAGL,MAAM,CAACG,QAAQ,EAAE,CAAC;MACzB,OAAOE,IAAI,KAAK3b,SAAS,IAAI2b,IAAI,KAAKH,YAAY,EAAE;QAChDI,GAAG,IAAID,IAAI;QACXA,IAAI,GAAGL,MAAM,CAACG,QAAQ,EAAE,CAAC;MAC7B;MACA,IAAMI,QAAQ,GAAGF,IAAI,KAAKH,YAAY;MACtC,IAAMnW,IAAI,GAAG+V,mBAAmB,CAACvuB,IAAI,CAAC+uB,GAAG,CAAC,GACpC,MAAM,GACNC,QAAQ,IAAIR,oBAAoB,CAACxuB,IAAI,CAAC+uB,GAAG,CAAC,GACtC,OAAO,GACP,SAAS;MACnBV,MAAM,CAACnpB,IAAI,CAAC;QAAEmD,KAAK,EAAE0mB,GAAG;QAAEvW,IAAI,EAAJA;MAAK,CAAC,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,KACK;MACDqW,IAAI,IAAIC,IAAI;IAChB;EACJ;EACAD,IAAI,IAAIR,MAAM,CAACnpB,IAAI,CAAC;IAAEsT,IAAI,EAAE,MAAM;IAAEnQ,KAAK,EAAEwmB;EAAK,CAAC,CAAC;EAClD,OAAOR,MAAM;AACjB;AACA,SAASC,OAAO,CAACD,MAAM,EAAE1iB,MAAM,EAAE;EAC7B,IAAMsjB,QAAQ,GAAG,EAAE;EACnB,IAAI5pB,KAAK,GAAG,CAAC;EACb,IAAM6pB,IAAI,GAAGpqB,KAAK,CAACC,OAAO,CAAC4G,MAAM,CAAC,GAC5B,MAAM,GACNzI,QAAQ,CAACyI,MAAM,CAAC,GACZ,OAAO,GACP,SAAS;EACnB,IAAIujB,IAAI,KAAK,SAAS,EAAE;IACpB,OAAOD,QAAQ;EACnB;EACA,OAAO5pB,KAAK,GAAGgpB,MAAM,CAACluB,MAAM,EAAE;IAC1B,IAAMgB,KAAK,GAAGktB,MAAM,CAAChpB,KAAK,CAAC;IAC3B,QAAQlE,KAAK,CAACqX,IAAI;MACd,KAAK,MAAM;QACPyW,QAAQ,CAAC/pB,IAAI,CAAC/D,KAAK,CAACkH,KAAK,CAAC;QAC1B;MACJ,KAAK,MAAM;QACP4mB,QAAQ,CAAC/pB,IAAI,CAACyG,MAAM,CAACsD,QAAQ,CAAC9N,KAAK,CAACkH,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;QAChD;MACJ,KAAK,OAAO;QACR,IAAI6mB,IAAI,KAAK,OAAO,EAAE;UAClBD,QAAQ,CAAC/pB,IAAI,CAACyG,MAAM,CAACxK,KAAK,CAACkH,KAAK,CAAC,CAAC;QACtC,CAAC,MACI;UACD,IAAI0J,IAAqC,EAAE;YACvCiE,OAAO,CAACC,IAAI,0BAAmB9U,KAAK,CAACqX,IAAI,oCAA0B0W,IAAI,oBAAiB;UAC5F;QACJ;QACA;MACJ,KAAK,SAAS;QACV,IAAInd,IAAqC,EAAE;UACvCiE,OAAO,CAACC,IAAI,mCAAmC;QACnD;QACA;IAAM;IAEd5Q,KAAK,EAAE;EACX;EACA,OAAO4pB,QAAQ;AACnB;AAEA,IAAMnlB,cAAc,GAAG,SAAS;AAAC;AACjC,IAAMC,cAAc,GAAG,SAAS;AAAC;AACjC,IAAMC,SAAS,GAAG,IAAI;AAAC;AACvB,IAAMC,SAAS,GAAG,IAAI;AAAC;AACvB,IAAMC,SAAS,GAAG,IAAI;AAAC;AACvB,IAAMpH,cAAc,GAAGD,MAAM,CAACT,SAAS,CAACU,cAAc;AACtD,IAAMQ,MAAM,GAAG,SAATA,MAAM,CAAIqkB,GAAG,EAAE7oB,GAAG;EAAA,OAAKgE,cAAc,CAACO,IAAI,CAACskB,GAAG,EAAE7oB,GAAG,CAAC;AAAA;AAC1D,IAAMqwB,gBAAgB,GAAG,IAAIjB,aAAa,EAAE;AAC5C,SAASzhB,OAAO,CAAC5M,GAAG,EAAE6M,KAAK,EAAE;EACzB,OAAO,CAAC,CAACA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAK/M,GAAG,CAACP,OAAO,CAACsN,IAAI,CAAC,KAAK,CAAC,CAAC;EAAA,EAAC;AAC3D;AACA,SAASC,UAAU,CAAChN,GAAG,EAAE6M,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAK/M,GAAG,CAACP,OAAO,CAACsN,IAAI,CAAC,KAAK,CAAC;EAAA,EAAC;AACxD;AACA,SAASvC,eAAe,CAACD,MAAM,EAAED,QAAQ,EAAE;EACvC,IAAI,CAACC,MAAM,EAAE;IACT;EACJ;EACAA,MAAM,GAAGA,MAAM,CAAC0C,IAAI,EAAE,CAAC/M,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACzC,IAAIoK,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,EAAE;IAC9B,OAAOA,MAAM;EACjB;EACAA,MAAM,GAAGA,MAAM,CAAC2C,WAAW,EAAE;EAC7B,IAAI3C,MAAM,KAAK,SAAS,EAAE;IACtB;IACA,OAAON,cAAc;EACzB;EACA,IAAIM,MAAM,CAAC9K,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAC5B,IAAI8K,MAAM,CAAC9K,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9B,OAAOwK,cAAc;IACzB;IACA,IAAIM,MAAM,CAAC9K,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9B,OAAOyK,cAAc;IACzB;IACA,IAAI0C,OAAO,CAACrC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;MAChD,OAAOL,cAAc;IACzB;IACA,OAAOD,cAAc;EACzB;EACA,IAAIc,OAAO,GAAG,CAACZ,SAAS,EAAEC,SAAS,EAAEC,SAAS,CAAC;EAC/C,IAAIC,QAAQ,IAAItH,MAAM,CAACsB,IAAI,CAACgG,QAAQ,CAAC,CAAChK,MAAM,GAAG,CAAC,EAAE;IAC9CyK,OAAO,GAAG/H,MAAM,CAACsB,IAAI,CAACgG,QAAQ,CAAC;EACnC;EACA,IAAM6C,IAAI,GAAGH,UAAU,CAACzC,MAAM,EAAEQ,OAAO,CAAC;EACxC,IAAIoC,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;AACJ;AAAC,IACKoiB,IAAI;EACN,qBAAsE;IAAA,IAAxDhlB,MAAM,SAANA,MAAM;MAAEilB,cAAc,SAAdA,cAAc;MAAEllB,QAAQ,SAARA,QAAQ;MAAEmlB,OAAO,SAAPA,OAAO;MAAEC,QAAQ,SAARA,QAAQ;IAAA;IAC7D,IAAI,CAACnlB,MAAM,GAAGJ,SAAS;IACvB,IAAI,CAACqlB,cAAc,GAAGrlB,SAAS;IAC/B,IAAI,CAACjI,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACoI,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACqlB,QAAQ,GAAG,EAAE;IAClB,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACxC;IACA,IAAI,CAACE,QAAQ,GAAGA,QAAQ,IAAIJ,gBAAgB;IAC5C,IAAI,CAAChlB,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;IAC9B,IAAI,CAACyB,SAAS,CAACxB,MAAM,IAAIJ,SAAS,CAAC;IACnC,IAAIslB,OAAO,EAAE;MACT,IAAI,CAAChkB,WAAW,CAACgkB,OAAO,CAAC;IAC7B;EACJ;EAAC;IAAA;IAAA,OACD,mBAAUllB,MAAM,EAAE;MAAA;MACd,IAAMoD,SAAS,GAAG,IAAI,CAACpD,MAAM;MAC7B,IAAI,CAACA,MAAM,GAAGC,eAAe,CAACD,MAAM,EAAE,IAAI,CAACD,QAAQ,CAAC,IAAI,IAAI,CAACklB,cAAc;MAC3E,IAAI,CAAC,IAAI,CAACllB,QAAQ,CAAC,IAAI,CAACC,MAAM,CAAC,EAAE;QAC7B;QACA,IAAI,CAACD,QAAQ,CAAC,IAAI,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC;MACnC;MACA,IAAI,CAACrI,OAAO,GAAG,IAAI,CAACoI,QAAQ,CAAC,IAAI,CAACC,MAAM,CAAC;MACzC;MACA,IAAIoD,SAAS,KAAK,IAAI,CAACpD,MAAM,EAAE;QAC3B,IAAI,CAAColB,QAAQ,CAACnrB,OAAO,CAAC,UAACirB,OAAO,EAAK;UAC/BA,OAAO,CAAC,KAAI,CAACllB,MAAM,EAAEoD,SAAS,CAAC;QACnC,CAAC,CAAC;MACN;IACJ;EAAC;IAAA;IAAA,OACD,qBAAY;MACR,OAAO,IAAI,CAACpD,MAAM;IACtB;EAAC;IAAA;IAAA,OACD,qBAAYpH,EAAE,EAAE;MAAA;MACZ,IAAMqC,KAAK,GAAG,IAAI,CAACmqB,QAAQ,CAACtqB,IAAI,CAAClC,EAAE,CAAC,GAAG,CAAC;MACxC,OAAO,YAAM;QACT,MAAI,CAACwsB,QAAQ,CAAClqB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAClC,CAAC;IACL;EAAC;IAAA;IAAA,OACD,aAAI+E,MAAM,EAAErI,OAAO,EAAmB;MAAA,IAAjB0tB,QAAQ,uEAAG,IAAI;MAChC,IAAM5kB,WAAW,GAAG,IAAI,CAACV,QAAQ,CAACC,MAAM,CAAC;MACzC,IAAIS,WAAW,EAAE;QACb,IAAI4kB,QAAQ,EAAE;UACV5sB,MAAM,CAAC+F,MAAM,CAACiC,WAAW,EAAE9I,OAAO,CAAC;QACvC,CAAC,MACI;UACDc,MAAM,CAACsB,IAAI,CAACpC,OAAO,CAAC,CAACsC,OAAO,CAAC,UAACvF,GAAG,EAAK;YAClC,IAAI,CAACwE,MAAM,CAACuH,WAAW,EAAE/L,GAAG,CAAC,EAAE;cAC3B+L,WAAW,CAAC/L,GAAG,CAAC,GAAGiD,OAAO,CAACjD,GAAG,CAAC;YACnC;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACD,IAAI,CAACqL,QAAQ,CAACC,MAAM,CAAC,GAAGrI,OAAO;MACnC;IACJ;EAAC;IAAA;IAAA,OACD,WAAEA,OAAO,EAAE4J,MAAM,EAAEyiB,UAAU,EAAE;MAC3B,OAAO,IAAI,CAACmB,QAAQ,CAACG,WAAW,CAAC3tB,OAAO,EAAE4J,MAAM,EAAEyiB,UAAU,CAAC,CAACrvB,IAAI,CAAC,EAAE,CAAC;IAC1E;EAAC;IAAA;IAAA,OACD,WAAED,GAAG,EAAEsL,MAAM,EAAEuB,MAAM,EAAE;MACnB,IAAI5J,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAI,OAAOqI,MAAM,KAAK,QAAQ,EAAE;QAC5BA,MAAM,GAAGC,eAAe,CAACD,MAAM,EAAE,IAAI,CAACD,QAAQ,CAAC;QAC/CC,MAAM,KAAKrI,OAAO,GAAG,IAAI,CAACoI,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC/C,CAAC,MACI;QACDuB,MAAM,GAAGvB,MAAM;MACnB;MACA,IAAI,CAAC9G,MAAM,CAACvB,OAAO,EAAEjD,GAAG,CAAC,EAAE;QACvBkX,OAAO,CAACC,IAAI,iDAA0CnX,GAAG,4CAAyC;QAClG,OAAOA,GAAG;MACd;MACA,OAAO,IAAI,CAACywB,QAAQ,CAACG,WAAW,CAAC3tB,OAAO,CAACjD,GAAG,CAAC,EAAE6M,MAAM,CAAC,CAAC5M,IAAI,CAAC,EAAE,CAAC;IACnE;EAAC;EAAA;AAAA;AAAA;AAGL,SAAS4wB,cAAc,CAAC5jB,KAAK,EAAEhB,IAAI,EAAE;EACjC;EACA,IAAIgB,KAAK,CAACI,YAAY,EAAE;IACpB;IACAJ,KAAK,CAACI,YAAY,CAAC,UAACyjB,SAAS,EAAK;MAC9B7kB,IAAI,CAACa,SAAS,CAACgkB,SAAS,CAAC;IAC7B,CAAC,CAAC;EACN,CAAC,MACI;IACD7jB,KAAK,CAAC8jB,MAAM,CAAC;MAAA,OAAM9jB,KAAK,CAACuB,OAAO;IAAA,GAAE,UAACsiB,SAAS,EAAK;MAC7C7kB,IAAI,CAACa,SAAS,CAACgkB,SAAS,CAAC;IAC7B,CAAC,CAAC;EACN;AACJ;AACA,SAASE,gBAAgB,GAAG;EACxB,IAAI,OAAOtF,GAAG,KAAK,WAAW,IAAIA,GAAG,CAAC3e,SAAS,EAAE;IAC7C,OAAO2e,GAAG,CAAC3e,SAAS,EAAE;EAC1B;EACA;EACA,IAAI,OAAO8B,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC9B,SAAS,EAAE;IACnD,OAAO8B,MAAM,CAAC9B,SAAS,EAAE;EAC7B;EACA,OAAO7B,SAAS;AACpB;AACA,SAASgB,WAAW,CAACZ,MAAM,EAA0C;EAAA,IAAxCD,QAAQ,uEAAG,CAAC,CAAC;EAAA,IAAEklB,cAAc;EAAA,IAAEC,OAAO;EAC/D;EACA,IAAI,OAAOllB,MAAM,KAAK,QAAQ,EAAE;IAAA,YACP,CACjBD,QAAQ,EACRC,MAAM,CACT;IAHAA,MAAM;IAAED,QAAQ;EAIrB;EACA,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;IAC5B;IACAA,MAAM,GAAG0lB,gBAAgB,EAAE;EAC/B;EACA,IAAI,OAAOT,cAAc,KAAK,QAAQ,EAAE;IACpCA,cAAc,GACT,OAAO1kB,WAAW,KAAK,WAAW,IAAIA,WAAW,CAAC0kB,cAAc,IAC7DrlB,SAAS;EACrB;EACA,IAAMe,IAAI,GAAG,IAAIqkB,IAAI,CAAC;IAClBhlB,MAAM,EAANA,MAAM;IACNilB,cAAc,EAAdA,cAAc;IACdllB,QAAQ,EAARA,QAAQ;IACRmlB,OAAO,EAAPA;EACJ,CAAC,CAAC;EACF,IAAIrkB,EAAC,GAAG,WAACnM,GAAG,EAAE6M,MAAM,EAAK;IACrB,IAAI,OAAOuB,MAAM,KAAK,UAAU,EAAE;MAC9B;MACA;MACAjC,EAAC,GAAG,WAAUnM,GAAG,EAAE6M,MAAM,EAAE;QACvB,OAAOZ,IAAI,CAACE,CAAC,CAACnM,GAAG,EAAE6M,MAAM,CAAC;MAC9B,CAAC;IACL,CAAC,MACI;MACD,IAAIokB,kBAAkB,GAAG,KAAK;MAC9B9kB,EAAC,GAAG,WAAUnM,GAAG,EAAE6M,MAAM,EAAE;QACvB,IAAMI,KAAK,GAAGmB,MAAM,EAAE,CAACG,GAAG;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAItB,KAAK,EAAE;UACP;UACAA,KAAK,CAACuB,OAAO;UACb,IAAI,CAACyiB,kBAAkB,EAAE;YACrBA,kBAAkB,GAAG,IAAI;YACzBJ,cAAc,CAAC5jB,KAAK,EAAEhB,IAAI,CAAC;UAC/B;QACJ;QACA,OAAOA,IAAI,CAACE,CAAC,CAACnM,GAAG,EAAE6M,MAAM,CAAC;MAC9B,CAAC;IACL;IACA,OAAOV,EAAC,CAACnM,GAAG,EAAE6M,MAAM,CAAC;EACzB,CAAC;EACD,OAAO;IACHZ,IAAI,EAAJA,IAAI;IACJ+gB,CAAC,aAAC/pB,OAAO,EAAE4J,MAAM,EAAEyiB,UAAU,EAAE;MAC3B,OAAOrjB,IAAI,CAAC+gB,CAAC,CAAC/pB,OAAO,EAAE4J,MAAM,EAAEyiB,UAAU,CAAC;IAC9C,CAAC;IACDnjB,CAAC,aAACnM,GAAG,EAAE6M,MAAM,EAAE;MACX,OAAOV,EAAC,CAACnM,GAAG,EAAE6M,MAAM,CAAC;IACzB,CAAC;IACDqkB,GAAG,eAAC5lB,MAAM,EAAErI,OAAO,EAAmB;MAAA,IAAjB0tB,QAAQ,uEAAG,IAAI;MAChC,OAAO1kB,IAAI,CAACilB,GAAG,CAAC5lB,MAAM,EAAErI,OAAO,EAAE0tB,QAAQ,CAAC;IAC9C,CAAC;IACDjjB,KAAK,iBAACxJ,EAAE,EAAE;MACN,OAAO+H,IAAI,CAACO,WAAW,CAACtI,EAAE,CAAC;IAC/B,CAAC;IACD6I,SAAS,uBAAG;MACR,OAAOd,IAAI,CAACc,SAAS,EAAE;IAC3B,CAAC;IACDD,SAAS,qBAACgkB,SAAS,EAAE;MACjB,OAAO7kB,IAAI,CAACa,SAAS,CAACgkB,SAAS,CAAC;IACpC;EACJ,CAAC;AACL;AAEA,IAAMK,QAAQ,GAAG,SAAXA,QAAQ,CAAItI,GAAG;EAAA,OAAK,OAAOA,GAAG,KAAK,QAAQ;AAAA;AAAC;AAClD,IAAI4H,QAAQ;AACZ,SAASW,WAAW,CAACC,OAAO,EAAE/B,UAAU,EAAE;EACtC,IAAI,CAACmB,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAIrB,aAAa,EAAE;EAClC;EACA,OAAOkC,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAErxB,GAAG,EAAK;IAC1C,IAAMuJ,KAAK,GAAG8nB,OAAO,CAACrxB,GAAG,CAAC;IAC1B,IAAImxB,QAAQ,CAAC5nB,KAAK,CAAC,EAAE;MACjB,IAAIgoB,SAAS,CAAChoB,KAAK,EAAE+lB,UAAU,CAAC,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,MACI;MACD,OAAO8B,WAAW,CAAC7nB,KAAK,EAAE+lB,UAAU,CAAC;IACzC;EACJ,CAAC,CAAC;AACN;AACA,SAASkC,aAAa,CAACH,OAAO,EAAExkB,MAAM,EAAEyiB,UAAU,EAAE;EAChD,IAAI,CAACmB,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAIrB,aAAa,EAAE;EAClC;EACAkC,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAErxB,GAAG,EAAK;IACnC,IAAMuJ,KAAK,GAAG8nB,OAAO,CAACrxB,GAAG,CAAC;IAC1B,IAAImxB,QAAQ,CAAC5nB,KAAK,CAAC,EAAE;MACjB,IAAIgoB,SAAS,CAAChoB,KAAK,EAAE+lB,UAAU,CAAC,EAAE;QAC9B+B,OAAO,CAACrxB,GAAG,CAAC,GAAGyxB,UAAU,CAACloB,KAAK,EAAEsD,MAAM,EAAEyiB,UAAU,CAAC;MACxD;IACJ,CAAC,MACI;MACDkC,aAAa,CAACjoB,KAAK,EAAEsD,MAAM,EAAEyiB,UAAU,CAAC;IAC5C;EACJ,CAAC,CAAC;EACF,OAAO+B,OAAO;AAClB;AACA,SAASK,kBAAkB,CAACC,OAAO,SAAoC;EAAA,IAAhCrmB,MAAM,SAANA,MAAM;IAAEQ,OAAO,SAAPA,OAAO;IAAEwjB,UAAU,SAAVA,UAAU;EAC9D,IAAI,CAACiC,SAAS,CAACI,OAAO,EAAErC,UAAU,CAAC,EAAE;IACjC,OAAOqC,OAAO;EAClB;EACA,IAAI,CAAClB,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAIrB,aAAa,EAAE;EAClC;EACA,IAAMwC,YAAY,GAAG,EAAE;EACvB7tB,MAAM,CAACsB,IAAI,CAACyG,OAAO,CAAC,CAACvG,OAAO,CAAC,UAACqC,IAAI,EAAK;IACnC,IAAIA,IAAI,KAAK0D,MAAM,EAAE;MACjBsmB,YAAY,CAACxrB,IAAI,CAAC;QACdkF,MAAM,EAAE1D,IAAI;QACZiF,MAAM,EAAEf,OAAO,CAAClE,IAAI;MACxB,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACFgqB,YAAY,CAACC,OAAO,CAAC;IAAEvmB,MAAM,EAANA,MAAM;IAAEuB,MAAM,EAAEf,OAAO,CAACR,MAAM;EAAE,CAAC,CAAC;EACzD,IAAI;IACA,OAAOxI,IAAI,CAACme,SAAS,CAAC6Q,cAAc,CAAChvB,IAAI,CAACC,KAAK,CAAC4uB,OAAO,CAAC,EAAEC,YAAY,EAAEtC,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACjG,CAAC,CACD,OAAOrW,CAAC,EAAE,CAAE;EACZ,OAAO0Y,OAAO;AAClB;AACA,SAASJ,SAAS,CAAChoB,KAAK,EAAE+lB,UAAU,EAAE;EAClC,OAAO/lB,KAAK,CAAC/I,OAAO,CAAC8uB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5C;AACA,SAASmC,UAAU,CAACloB,KAAK,EAAEsD,MAAM,EAAEyiB,UAAU,EAAE;EAC3C,OAAOmB,QAAQ,CAACG,WAAW,CAACrnB,KAAK,EAAEsD,MAAM,EAAEyiB,UAAU,CAAC,CAACrvB,IAAI,CAAC,EAAE,CAAC;AACnE;AACA,SAAS8xB,YAAY,CAACV,OAAO,EAAErxB,GAAG,EAAE4xB,YAAY,EAAEtC,UAAU,EAAE;EAC1D,IAAM/lB,KAAK,GAAG8nB,OAAO,CAACrxB,GAAG,CAAC;EAC1B,IAAImxB,QAAQ,CAAC5nB,KAAK,CAAC,EAAE;IACjB;IACA,IAAIgoB,SAAS,CAAChoB,KAAK,EAAE+lB,UAAU,CAAC,EAAE;MAC9B+B,OAAO,CAACrxB,GAAG,CAAC,GAAGyxB,UAAU,CAACloB,KAAK,EAAEqoB,YAAY,CAAC,CAAC,CAAC,CAAC/kB,MAAM,EAAEyiB,UAAU,CAAC;MACpE,IAAIsC,YAAY,CAACvwB,MAAM,GAAG,CAAC,EAAE;QACzB;QACA,IAAM2wB,YAAY,GAAIX,OAAO,CAACrxB,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAE;QACpD4xB,YAAY,CAACrsB,OAAO,CAAC,UAAC0sB,UAAU,EAAK;UACjCD,YAAY,CAACC,UAAU,CAAC3mB,MAAM,CAAC,GAAGmmB,UAAU,CAACloB,KAAK,EAAE0oB,UAAU,CAACplB,MAAM,EAAEyiB,UAAU,CAAC;QACtF,CAAC,CAAC;MACN;IACJ;EACJ,CAAC,MACI;IACDwC,cAAc,CAACvoB,KAAK,EAAEqoB,YAAY,EAAEtC,UAAU,CAAC;EACnD;AACJ;AACA,SAASwC,cAAc,CAACT,OAAO,EAAEO,YAAY,EAAEtC,UAAU,EAAE;EACvDgC,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAErxB,GAAG,EAAK;IACnC+xB,YAAY,CAACV,OAAO,EAAErxB,GAAG,EAAE4xB,YAAY,EAAEtC,UAAU,CAAC;EACxD,CAAC,CAAC;EACF,OAAO+B,OAAO;AAClB;AACA,SAASC,WAAW,CAACD,OAAO,EAAEa,IAAI,EAAE;EAChC,IAAIlsB,KAAK,CAACC,OAAO,CAACorB,OAAO,CAAC,EAAE;IACxB,KAAK,IAAI3vB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2vB,OAAO,CAAChwB,MAAM,EAAEK,CAAC,EAAE,EAAE;MACrC,IAAIwwB,IAAI,CAACb,OAAO,EAAE3vB,CAAC,CAAC,EAAE;QAClB,OAAO,IAAI;MACf;IACJ;EACJ,CAAC,MACI,IAAI0C,QAAQ,CAACitB,OAAO,CAAC,EAAE;IACxB,KAAK,IAAMrxB,GAAG,IAAIqxB,OAAO,EAAE;MACvB,IAAIa,IAAI,CAACb,OAAO,EAAErxB,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI;MACf;IACJ;EACJ;EACA,OAAO,KAAK;AAChB;AAEA,SAASmyB,aAAa,CAACrmB,OAAO,EAAE;EAC5B,OAAO,UAACR,MAAM,EAAK;IACf,IAAI,CAACA,MAAM,EAAE;MACT,OAAOA,MAAM;IACjB;IACAA,MAAM,GAAGC,eAAe,CAACD,MAAM,CAAC,IAAIA,MAAM;IAC1C,OAAO8mB,kBAAkB,CAAC9mB,MAAM,CAAC,CAACuC,IAAI,CAAC,UAACvC,MAAM;MAAA,OAAKQ,OAAO,CAACtL,OAAO,CAAC8K,MAAM,CAAC,GAAG,CAAC,CAAC;IAAA,EAAC;EACpF,CAAC;AACL;AACA,SAAS8mB,kBAAkB,CAAC9mB,MAAM,EAAE;EAChC,IAAM+mB,KAAK,GAAG,EAAE;EAChB,IAAM9C,MAAM,GAAGjkB,MAAM,CAACvJ,KAAK,CAAC,GAAG,CAAC;EAChC,OAAOwtB,MAAM,CAACluB,MAAM,EAAE;IAClBgxB,KAAK,CAACjsB,IAAI,CAACmpB,MAAM,CAACtvB,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5BsvB,MAAM,CAAC+C,GAAG,EAAE;EAChB;EACA,OAAOD,KAAK;AAChB,C;;;;;;;;;;;AC1cA,SAASE,eAAe,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAI9E,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AACA5B,MAAM,CAACC,OAAO,GAAGuG,eAAe,EAAExG,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;ACL9G,IAAI4B,aAAa,GAAG1B,mBAAO,CAAC,4BAAoB,CAAC;AACjD,SAASwG,iBAAiB,CAAC5yB,MAAM,EAAEkiB,KAAK,EAAE;EACxC,KAAK,IAAItgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsgB,KAAK,CAAC3gB,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrC,IAAIixB,UAAU,GAAG3Q,KAAK,CAACtgB,CAAC,CAAC;IACzBixB,UAAU,CAACrV,UAAU,GAAGqV,UAAU,CAACrV,UAAU,IAAI,KAAK;IACtDqV,UAAU,CAACtV,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIsV,UAAU,EAAEA,UAAU,CAAC7E,QAAQ,GAAG,IAAI;IACrD/pB,MAAM,CAACuJ,cAAc,CAACxN,MAAM,EAAE8tB,aAAa,CAAC+E,UAAU,CAAC3yB,GAAG,CAAC,EAAE2yB,UAAU,CAAC;EAC1E;AACF;AACA,SAASC,YAAY,CAACH,WAAW,EAAEI,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEH,iBAAiB,CAACD,WAAW,CAACnvB,SAAS,EAAEuvB,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEJ,iBAAiB,CAACD,WAAW,EAAEK,WAAW,CAAC;EAC5D/uB,MAAM,CAACuJ,cAAc,CAACmlB,WAAW,EAAE,WAAW,EAAE;IAC9C3E,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,OAAO2E,WAAW;AACpB;AACA1G,MAAM,CAACC,OAAO,GAAG4G,YAAY,EAAE7G,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;;AClB3G;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC;;AAElC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;AACA,sBAAsB,+BAA+B;AACrD,sBAAsB,iBAAiB;AACvC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,kDAAkD,iCAAiC,EAAE;AACrF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,6BAA6B,cAAc;;AAE3C;;AAEA;AACA;AACA;AACA,6BAA6B,UAAU;;AAEvC;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC,kCAAkC;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,aAAoB;;AAErC;AACA;AACA;AACA,YAAY,aAAoB;;AAEhC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qBAAqB;AACxC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA,oCAAoC;AACpC;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA,iCAAiC;AACjC,uCAAuC,wBAAwB,EAAE;AACjE,0BAA0B;;AAE1B;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC,kBAAkB,YAAY;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA,wCAAwC,EAAE;AAC1C;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,+BAA+B,oBAAoB,EAAE;AACrD;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0BAA0B,SAAS,qBAAqB;;AAExD;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;;AAEA,iBAAiB,iBAAiB;AAClC;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,uBAAuB;AACzD,iCAAiC,sBAAsB;AACvD;AACA,kBAAkB;AAClB,MAAM,IAAqC;AAC3C;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAoB;AACtC;AACA;AACA,mBAAmB;AACnB;AACA;AACA,iBAAiB,uBAAuB;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,OAAO,UAAU,IAAqC;AACtD;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,GAAG,UAAU,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,mBAAmB,mBAAmB;AACtC,+BAA+B;AAC/B;AACA,GAAG;AACH;AACA;AACA;AACA,kBAAkB,YAAY;AAC9B,WAAW;AACX;AACA,GAAG,UAAU,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA,oCAAoC;AACpC;AACA,qCAAqC;AACrC;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAEQ;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,6CAA6C,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,6CAA6C,qCAAqC,EAAE;AACpF;;AAEA;AACA;AACA;;AAEA,oCAAoC,yCAAyC,EAAE;AAC/E;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,kBAAkB;AAC3C;AACA;AACA,4BAA4B;AAC5B,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,sDAAsD,EAAE;AACtF;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;;AAEA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,iCAAiC;AACnE,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kCAAkC,iCAAiC;AACnE,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,yBAAyB;AAC1C,GAAG;AACH;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA;;AAEA;AACA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,uBAAuB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,mBAAmB;AACxC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,qBAAqB;AACtC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,qBAAqB;AAClC;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO,MAAM,EAEN;AACP,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,iBAAiB;AACpC;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,IAAqC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,sBAAsB,mBAAmB;AACzC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO;AACtC,uCAAuC;AACvC;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB,sCAAsC;AACtC;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA,KAAK;AACL;AACA;AACA,kCAAkC,OAAO;AACzC;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA,4CAA4C,eAAe;AAC3D,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,kDAAkD;AAClD,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;AACA,KAAK,UAAU,KAAqC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,qCAAqC,gEAAgE;AACrG;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,4BAA4B,+BAA+B;AAC3D,4BAA4B,+BAA+B;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,mBAAmB;AACtC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C,kDAAkD;AAClD;AACA;AACA,mCAAmC;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,sEAAsE;;AAEtE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,uFAAuF;AAC5F;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0CAA0C;AAC1C,iBAAiB,yBAAyB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG,+BAA+B;AAClC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,oBAAoB,oBAAoB;AACxC,sBAAsB,4BAA4B;AAClD;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,mBAAmB;AACnB,yBAAyB;AACzB;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,6CAA6C;AAC9E;AACA;AACA,6CAA6C,4CAA4C;;AAEzF;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,GAAG,MAAM,EAGN;AACH;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,KAAK,2CAA2C,8BAA8B,EAAE;;AAEhF;AACA,wCAAwC,OAAO;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;;AAEL;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB,KAAqC;AACrD;AACA,oBAAoB,SAAI;AACxB;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,mBAAmB,qBAAqB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,OAAO;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;;AAE1B,kBAAkB;AAClB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qBAAqB;AACxC;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAM,IAAqC;AAC3C;AACA;AACA;;AAEA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,OAAO;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,0BAA0B;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,oBAAoB,EAAE;;AAEpD;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,oBAAoB;AACpB;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,oBAAoB,KAAqC;AACzD;AACA,MAAM,SAAE;AACR;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C,qBAAqB,+BAA+B;AACpD;AACA;AACA,GAAG;AACH,yBAAyB;AACzB;AACA,sBAAsB,iCAAiC;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kGAAkG;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK,MAAM,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,UAAU,IAAqC;AACpD;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB,oBAAoB;AACzC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,8BAA8B;AAC9B,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA,KAAK,MAAM,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,sCAAsC;AACtC,8C;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,eAAe;AACrC;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE;AACtE;AACA;AACA;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC;;AAEjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,YAAY,KAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,0CAA0C,2BAA2B,EAAE;AACvE,KAAK;AACL;AACA,0CAA0C,4BAA4B,EAAE;AACxE,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,+BAA+B,eAAe;AAC9C,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,yBAAyB;AACzB;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA,SAAS;AACT;AACA;AACA,aAAa;AACb;AACA;AACA,iBAAiB;AACjB;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,YAAY,+IAAW;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAmB;AAC1C;AACA;AACA;AACA;;AAEA;AACA,0CAA0C,gCAAgC,EAAE;AAC5E;;AAEA;AACA;AACA;AACA;AACA,WAAW,+IAAW;AACtB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,WAAW,+IAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,0CAA0C;;AAE1C;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA,sCAAsC;AACtC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA,KAAK;AACL;AACA;AACA,UAAU,+IAAW;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,OAAO;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,qDAAqD,EAAE,SAAS;AACtH;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC,OAAO;AACxC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,0BAA0B,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEe,kEAAG,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC97LnB;;AAEA;AACA;AACA;AACO,SAAS+G,QAAQ,GAAG;EAC1B,OAAOrH,GAAG,CAACnpB,cAAc,CAAC,YAAY,CAAC;AACxC;;AAEA;AACA;AACA;AACO,SAASywB,QAAQ,CAAC3wB,KAAK,EAAE;EAC/BqpB,GAAG,CAACuH,cAAc,CAAC,YAAY,EAAE5wB,KAAK,CAAC;EACvC6U,OAAO,CAACgc,GAAG,CAAC,UAAU,CAAC;AACxB;;AAEA;AACA;AACA;AACO,SAASC,WAAW,GAAG;EAC7BzH,GAAG,CAAC0H,iBAAiB,CAAC,YAAY,CAAC;EACnC1H,GAAG,CAAC0H,iBAAiB,CAAC,WAAW,CAAC;EAClClc,OAAO,CAACgc,GAAG,CAAC,eAAe,CAAC;AAC7B;;AAEA;AACA;AACA;AACO,SAASG,WAAW,GAAG;EAC7B,OAAO3H,GAAG,CAACnpB,cAAc,CAAC,WAAW,CAAC;AACvC;;AAEA;AACA;AACA;AACO,SAAS+wB,WAAW,CAACzwB,QAAQ,EAAE;EACrC,OAAO6oB,GAAG,CAACuH,cAAc,CAAC,WAAW,EAAEpwB,QAAQ,CAAC;AACjD;;AAEA;AACA;AACA;AACO,SAAS0wB,UAAU,GAAG;EAC5B,IAAMlxB,KAAK,GAAG0wB,QAAQ,EAAE;EACxB,IAAMlwB,QAAQ,GAAGwwB,WAAW,EAAE;EAE9B,OAAO,CAAC,EAAEhxB,KAAK,IAAIQ,QAAQ,CAAC;AAC7B;;AAIA;AACA;AACA;AACA;AACA;AACO,SAAS2wB,UAAU,GAAmB;EAAA,IAAlBC,SAAS,uEAAG,IAAI;EAC1C,IAAI,CAACF,UAAU,EAAE,EAAE;IAClB,IAAMtwB,OAAO,GAAG,MAAM;IAEtB,IAAIwwB,SAAS,EAAE;MACd/H,GAAG,CAAC+H,SAAS,CAAC;QACb9d,KAAK,EAAE1S,OAAO;QACdywB,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE;MACX,CAAC,CAAC;IACH;;IAEA;IACAC,UAAU,CAAC,YAAM;MAChBlI,GAAG,CAACmI,UAAU,CAAC;QACdzkB,GAAG,EAAE;MACN,CAAC,CAAC;IACH,CAAC,EAAEqkB,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC;IAExB,OAAO,KAAK;EACb;EACA,OAAO,IAAI;AACZ;;AAEA;AACA;AACA;AACO,SAASK,MAAM,GAAG;EACxB,OAAO,IAAIvsB,OAAO,CAAC,UAACC,OAAO,EAAK;IAC/BkkB,GAAG,CAACqI,SAAS,CAAC;MACbpe,KAAK,EAAE,MAAM;MACbqe,OAAO,EAAE,WAAW;MACpBrqB,OAAO,EAAE,iBAAC7D,GAAG,EAAK;QACjB,IAAIA,GAAG,CAACmuB,OAAO,EAAE;UAChB;UACAd,WAAW,EAAE;UAEbzH,GAAG,CAAC+H,SAAS,CAAC;YACb9d,KAAK,EAAE,OAAO;YACd+d,IAAI,EAAE,SAAS;YACfC,QAAQ,EAAE;UACX,CAAC,CAAC;;UAEF;UACAC,UAAU,CAAC,YAAM;YAChBlI,GAAG,CAACwI,QAAQ,CAAC;cACZ9kB,GAAG,EAAE;YACN,CAAC,CAAC;UACH,CAAC,EAAE,IAAI,CAAC;UAER5H,OAAO,CAAC,IAAI,CAAC;QACd,CAAC,MAAM;UACNA,OAAO,CAAC,KAAK,CAAC;QACf;MACD;IACD,CAAC,CAAC;EACH,CAAC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACO,IAAM2sB,eAAe,GAAG;EAC9BxnB,OAAO,EAAE;IACR;IACAynB,gBAAgB,8BAAG;MAClB,OAAOZ,UAAU,EAAE;IACpB,CAAC;IAED;IACAa,YAAY,0BAAG;MACd,OAAOP,MAAM,EAAE;IAChB;EACD;AACD,CAAC;;AAED;AACA;AACA;AAFA;AAGO,IAAMQ,cAAc,GAAG,CAC7B,oBAAoB,EACpB,8BAA8B,EAC9B,sBAAsB,CACtB;;AAED;AACA;AACA;AAFA;AAGO,SAASC,eAAe,CAACnlB,GAAG,EAAE;EACpC;EACA,IAAMolB,QAAQ,GAAGplB,GAAG,CAACrN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAClC,OAAOuyB,cAAc,CAACG,IAAI,CAAC,UAAAjlB,IAAI;IAAA,OAAIglB,QAAQ,CAACj0B,QAAQ,CAACiP,IAAI,CAAC;EAAA,EAAC;AAC5D;;AAEA;AACA,IAAIklB,aAAa,GAAG,KAAK;;AAEzB;AACA;AACA;AACO,SAASC,YAAY,GAAG;EAC9BD,aAAa,GAAG,IAAI;EACpB;EACAd,UAAU,CAAC,YAAM;IAChBc,aAAa,GAAG,KAAK;EACtB,CAAC,EAAE,IAAI,CAAC;AACT;;AAEA;AACA;AACA;AACO,SAASE,oBAAoB,GAAG;EACtC;EACA,IAAMC,kBAAkB,GAAGnJ,GAAG,CAACmI,UAAU;EACzCnI,GAAG,CAACmI,UAAU,GAAG,UAASlsB,OAAO,EAAE;IAClC,IAAI,CAAC+sB,aAAa,IAAIH,eAAe,CAAC5sB,OAAO,CAACyH,GAAG,CAAC,IAAI,CAACmkB,UAAU,EAAE,EAAE;MACpErc,OAAO,CAACgc,GAAG,CAAC,gBAAgB,EAAEvrB,OAAO,CAACyH,GAAG,EAAE,OAAO,EAAEmkB,UAAU,EAAE,CAAC;MACjEC,UAAU,EAAE;MACZ;IACD;IACA,OAAOqB,kBAAkB,CAACtwB,IAAI,CAAC,IAAI,EAAEoD,OAAO,CAAC;EAC9C,CAAC;;EAED;EACA,IAAMmtB,iBAAiB,GAAGpJ,GAAG,CAACqJ,SAAS;EACvCrJ,GAAG,CAACqJ,SAAS,GAAG,UAASptB,OAAO,EAAE;IACjC,IAAI,CAAC+sB,aAAa,IAAIH,eAAe,CAAC5sB,OAAO,CAACyH,GAAG,CAAC,IAAI,CAACmkB,UAAU,EAAE,EAAE;MACpErc,OAAO,CAACgc,GAAG,CAAC,eAAe,EAAEvrB,OAAO,CAACyH,GAAG,EAAE,OAAO,EAAEmkB,UAAU,EAAE,CAAC;MAChEC,UAAU,EAAE;MACZ;IACD;IACA,OAAOsB,iBAAiB,CAACvwB,IAAI,CAAC,IAAI,EAAEoD,OAAO,CAAC;EAC7C,CAAC;;EAED;EACA,IAAMqtB,gBAAgB,GAAGtJ,GAAG,CAACwI,QAAQ;EACrCxI,GAAG,CAACwI,QAAQ,GAAG,UAASvsB,OAAO,EAAE;IAChC,IAAI,CAAC+sB,aAAa,IAAIH,eAAe,CAAC5sB,OAAO,CAACyH,GAAG,CAAC,IAAI,CAACmkB,UAAU,EAAE,EAAE;MACpErc,OAAO,CAACgc,GAAG,CAAC,cAAc,EAAEvrB,OAAO,CAACyH,GAAG,EAAE,OAAO,EAAEmkB,UAAU,EAAE,CAAC;MAC/DC,UAAU,EAAE;MACZ;IACD;IACA,OAAOwB,gBAAgB,CAACzwB,IAAI,CAAC,IAAI,EAAEoD,OAAO,CAAC;EAC5C,CAAC;;EAED;EACA,IAAMstB,kBAAkB,GAAGvJ,GAAG,CAAC/b,UAAU;EACzC+b,GAAG,CAAC/b,UAAU,GAAG,UAAShI,OAAO,EAAE;IAClC,IAAI4sB,eAAe,CAAC5sB,OAAO,CAACyH,GAAG,CAAC,IAAI,CAACmkB,UAAU,EAAE,EAAE;MAClDC,UAAU,EAAE;MACZ;IACD;IACA,OAAOyB,kBAAkB,CAAC1wB,IAAI,CAAC,IAAI,EAAEoD,OAAO,CAAC;EAC9C,CAAC;AACF;;AAEA;AACA;AACA;AAFA,SAGsButB,cAAc;EAAA;AAAA;AAAA;EAAA,0FAA7B;IAAA;IAAA;MAAA;QAAA;UAAA;YACA7yB,KAAK,GAAG0wB,QAAQ,EAAE;YAClBlwB,QAAQ,GAAGwwB,WAAW,EAAE;YAAA,MAE1B,CAAChxB,KAAK,IAAI,CAACQ,QAAQ;cAAA;cAAA;YAAA;YACtB;YACA6oB,GAAG,CAACwI,QAAQ,CAAC;cACZ9kB,GAAG,EAAE;YACN,CAAC,CAAC;YAAA,iCACK,KAAK;UAAA;YAAA;YAAA,iCAgBL,IAAI;UAAA;YAAA;YAAA;YAEX8H,OAAO,CAAClU,KAAK,CAAC,YAAY,cAAQ;YAClC;YACAmwB,WAAW,EAAE;YACbzH,GAAG,CAACwI,QAAQ,CAAC;cACZ9kB,GAAG,EAAE;YACN,CAAC,CAAC;YAAA,iCACK,KAAK;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA,CAEb;EAAA;AAAA,C;;;;;;;;;;;AC5PD;;AAEA,IAAI+lB,OAAO,GAAGjJ,mBAAO,CAAC,mDAA2C,CAAC,EAAE;AACpEH,MAAM,CAACC,OAAO,GAAGmJ,OAAO,C;;;;;;;;;;ACHxB,IAAIpH,OAAO,GAAG7B,mBAAO,CAAC,qBAAa,CAAC,CAAC,SAAS,CAAC;AAC/C,SAASkJ,mBAAmB,GAAG;EAC7B,YAAY;;EAAE;EACdrJ,MAAM,CAACC,OAAO,GAAGoJ,mBAAmB,GAAG,SAASA,mBAAmB,GAAG;IACpE,OAAOnc,CAAC;EACV,CAAC,EAAE8S,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO;EAC/E,IAAI7f,CAAC;IACH8M,CAAC,GAAG,CAAC,CAAC;IACNwT,CAAC,GAAG1oB,MAAM,CAACT,SAAS;IACpBupB,CAAC,GAAGJ,CAAC,CAACzoB,cAAc;IACpBipB,CAAC,GAAGlpB,MAAM,CAACuJ,cAAc,IAAI,UAAUnB,CAAC,EAAE8M,CAAC,EAAEwT,CAAC,EAAE;MAC9CtgB,CAAC,CAAC8M,CAAC,CAAC,GAAGwT,CAAC,CAACljB,KAAK;IAChB,CAAC;IACD7H,CAAC,GAAG,UAAU,IAAI,OAAOirB,MAAM,GAAGA,MAAM,GAAG,CAAC,CAAC;IAC7CI,CAAC,GAAGrrB,CAAC,CAACkrB,QAAQ,IAAI,YAAY;IAC9B3qB,CAAC,GAAGP,CAAC,CAAC2zB,aAAa,IAAI,iBAAiB;IACxCvI,CAAC,GAAGprB,CAAC,CAAC4zB,WAAW,IAAI,eAAe;EACtC,SAASC,MAAM,CAACppB,CAAC,EAAE8M,CAAC,EAAEwT,CAAC,EAAE;IACvB,OAAO1oB,MAAM,CAACuJ,cAAc,CAACnB,CAAC,EAAE8M,CAAC,EAAE;MACjC1P,KAAK,EAAEkjB,CAAC;MACRnP,UAAU,EAAE,CAAC,CAAC;MACdD,YAAY,EAAE,CAAC,CAAC;MAChByQ,QAAQ,EAAE,CAAC;IACb,CAAC,CAAC,EAAE3hB,CAAC,CAAC8M,CAAC,CAAC;EACV;EACA,IAAI;IACFsc,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAChB,CAAC,CAAC,OAAOppB,CAAC,EAAE;IACVopB,MAAM,GAAG,SAASA,MAAM,CAACppB,CAAC,EAAE8M,CAAC,EAAEwT,CAAC,EAAE;MAChC,OAAOtgB,CAAC,CAAC8M,CAAC,CAAC,GAAGwT,CAAC;IACjB,CAAC;EACH;EACA,SAAS+I,IAAI,CAACrpB,CAAC,EAAE8M,CAAC,EAAEwT,CAAC,EAAEI,CAAC,EAAE;IACxB,IAAInrB,CAAC,GAAGuX,CAAC,IAAIA,CAAC,CAAC3V,SAAS,YAAYmyB,SAAS,GAAGxc,CAAC,GAAGwc,SAAS;MAC3D1I,CAAC,GAAGhpB,MAAM,CAACa,MAAM,CAAClD,CAAC,CAAC4B,SAAS,CAAC;MAC9BrB,CAAC,GAAG,IAAIyzB,OAAO,CAAC7I,CAAC,IAAI,EAAE,CAAC;IAC1B,OAAOI,CAAC,CAACF,CAAC,EAAE,SAAS,EAAE;MACrBxjB,KAAK,EAAEosB,gBAAgB,CAACxpB,CAAC,EAAEsgB,CAAC,EAAExqB,CAAC;IACjC,CAAC,CAAC,EAAE8qB,CAAC;EACP;EACA,SAAS/T,QAAQ,CAAC7M,CAAC,EAAE8M,CAAC,EAAEwT,CAAC,EAAE;IACzB,IAAI;MACF,OAAO;QACL/S,IAAI,EAAE,QAAQ;QACdmL,GAAG,EAAE1Y,CAAC,CAAC5H,IAAI,CAAC0U,CAAC,EAAEwT,CAAC;MAClB,CAAC;IACH,CAAC,CAAC,OAAOtgB,CAAC,EAAE;MACV,OAAO;QACLuN,IAAI,EAAE,OAAO;QACbmL,GAAG,EAAE1Y;MACP,CAAC;IACH;EACF;EACA8M,CAAC,CAACuc,IAAI,GAAGA,IAAI;EACb,IAAII,CAAC,GAAG,gBAAgB;IACtBlJ,CAAC,GAAG,gBAAgB;IACpBM,CAAC,GAAG,WAAW;IACf6I,CAAC,GAAG,WAAW;IACfC,CAAC,GAAG,CAAC,CAAC;EACR,SAASL,SAAS,GAAG,CAAC;EACtB,SAASM,iBAAiB,GAAG,CAAC;EAC9B,SAASC,0BAA0B,GAAG,CAAC;EACvC,IAAI1H,CAAC,GAAG,CAAC,CAAC;EACViH,MAAM,CAACjH,CAAC,EAAEvB,CAAC,EAAE,YAAY;IACvB,OAAO,IAAI;EACb,CAAC,CAAC;EACF,IAAIkJ,CAAC,GAAGlyB,MAAM,CAACmyB,cAAc;IAC3BzoB,CAAC,GAAGwoB,CAAC,IAAIA,CAAC,CAACA,CAAC,CAACppB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3BY,CAAC,IAAIA,CAAC,KAAKgf,CAAC,IAAII,CAAC,CAACtoB,IAAI,CAACkJ,CAAC,EAAEsf,CAAC,CAAC,KAAKuB,CAAC,GAAG7gB,CAAC,CAAC;EACvC,IAAI0oB,CAAC,GAAGH,0BAA0B,CAAC1yB,SAAS,GAAGmyB,SAAS,CAACnyB,SAAS,GAAGS,MAAM,CAACa,MAAM,CAAC0pB,CAAC,CAAC;EACrF,SAAS8H,qBAAqB,CAACjqB,CAAC,EAAE;IAChC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC5G,OAAO,CAAC,UAAU0T,CAAC,EAAE;MAC/Csc,MAAM,CAACppB,CAAC,EAAE8M,CAAC,EAAE,UAAU9M,CAAC,EAAE;QACxB,OAAO,IAAI,CAACkqB,OAAO,CAACpd,CAAC,EAAE9M,CAAC,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,SAASmqB,aAAa,CAACnqB,CAAC,EAAE8M,CAAC,EAAE;IAC3B,SAAS3Q,MAAM,CAACmkB,CAAC,EAAEQ,CAAC,EAAEvrB,CAAC,EAAEqrB,CAAC,EAAE;MAC1B,IAAI9qB,CAAC,GAAG+W,QAAQ,CAAC7M,CAAC,CAACsgB,CAAC,CAAC,EAAEtgB,CAAC,EAAE8gB,CAAC,CAAC;MAC5B,IAAI,OAAO,KAAKhrB,CAAC,CAACyX,IAAI,EAAE;QACtB,IAAIoT,CAAC,GAAG7qB,CAAC,CAAC4iB,GAAG;UACX+Q,CAAC,GAAG9I,CAAC,CAACvjB,KAAK;QACb,OAAOqsB,CAAC,IAAI,QAAQ,IAAI7H,OAAO,CAAC6H,CAAC,CAAC,IAAI/I,CAAC,CAACtoB,IAAI,CAACqxB,CAAC,EAAE,SAAS,CAAC,GAAG3c,CAAC,CAACzR,OAAO,CAACouB,CAAC,CAACW,OAAO,CAAC,CAACnvB,IAAI,CAAC,UAAU+E,CAAC,EAAE;UAClG7D,MAAM,CAAC,MAAM,EAAE6D,CAAC,EAAEzK,CAAC,EAAEqrB,CAAC,CAAC;QACzB,CAAC,EAAE,UAAU5gB,CAAC,EAAE;UACd7D,MAAM,CAAC,OAAO,EAAE6D,CAAC,EAAEzK,CAAC,EAAEqrB,CAAC,CAAC;QAC1B,CAAC,CAAC,GAAG9T,CAAC,CAACzR,OAAO,CAACouB,CAAC,CAAC,CAACxuB,IAAI,CAAC,UAAU+E,CAAC,EAAE;UAClC2gB,CAAC,CAACvjB,KAAK,GAAG4C,CAAC,EAAEzK,CAAC,CAACorB,CAAC,CAAC;QACnB,CAAC,EAAE,UAAU3gB,CAAC,EAAE;UACd,OAAO7D,MAAM,CAAC,OAAO,EAAE6D,CAAC,EAAEzK,CAAC,EAAEqrB,CAAC,CAAC;QACjC,CAAC,CAAC;MACJ;MACAA,CAAC,CAAC9qB,CAAC,CAAC4iB,GAAG,CAAC;IACV;IACA,IAAI4H,CAAC;IACLQ,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE;MACjB1jB,KAAK,EAAE,SAASA,KAAK,CAAC4C,CAAC,EAAE0gB,CAAC,EAAE;QAC1B,SAAS2J,0BAA0B,GAAG;UACpC,OAAO,IAAIvd,CAAC,CAAC,UAAUA,CAAC,EAAEwT,CAAC,EAAE;YAC3BnkB,MAAM,CAAC6D,CAAC,EAAE0gB,CAAC,EAAE5T,CAAC,EAAEwT,CAAC,CAAC;UACpB,CAAC,CAAC;QACJ;QACA,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACrlB,IAAI,CAACovB,0BAA0B,EAAEA,0BAA0B,CAAC,GAAGA,0BAA0B,EAAE;MAC9G;IACF,CAAC,CAAC;EACJ;EACA,SAASb,gBAAgB,CAAC1c,CAAC,EAAEwT,CAAC,EAAEI,CAAC,EAAE;IACjC,IAAII,CAAC,GAAG2I,CAAC;IACT,OAAO,UAAUl0B,CAAC,EAAEqrB,CAAC,EAAE;MACrB,IAAIE,CAAC,KAAKD,CAAC,EAAE,MAAM7rB,KAAK,CAAC,8BAA8B,CAAC;MACxD,IAAI8rB,CAAC,KAAK4I,CAAC,EAAE;QACX,IAAI,OAAO,KAAKn0B,CAAC,EAAE,MAAMqrB,CAAC;QAC1B,OAAO;UACLxjB,KAAK,EAAE4C,CAAC;UACRghB,IAAI,EAAE,CAAC;QACT,CAAC;MACH;MACA,KAAKN,CAAC,CAAC/lB,MAAM,GAAGpF,CAAC,EAAEmrB,CAAC,CAAChI,GAAG,GAAGkI,CAAC,IAAI;QAC9B,IAAI9qB,CAAC,GAAG4qB,CAAC,CAAC4J,QAAQ;QAClB,IAAIx0B,CAAC,EAAE;UACL,IAAI6qB,CAAC,GAAG4J,mBAAmB,CAACz0B,CAAC,EAAE4qB,CAAC,CAAC;UACjC,IAAIC,CAAC,EAAE;YACL,IAAIA,CAAC,KAAKgJ,CAAC,EAAE;YACb,OAAOhJ,CAAC;UACV;QACF;QACA,IAAI,MAAM,KAAKD,CAAC,CAAC/lB,MAAM,EAAE+lB,CAAC,CAAC8J,IAAI,GAAG9J,CAAC,CAAC+J,KAAK,GAAG/J,CAAC,CAAChI,GAAG,CAAC,KAAK,IAAI,OAAO,KAAKgI,CAAC,CAAC/lB,MAAM,EAAE;UAC/E,IAAImmB,CAAC,KAAK2I,CAAC,EAAE,MAAM3I,CAAC,GAAG4I,CAAC,EAAEhJ,CAAC,CAAChI,GAAG;UAC/BgI,CAAC,CAACgK,iBAAiB,CAAChK,CAAC,CAAChI,GAAG,CAAC;QAC5B,CAAC,MAAM,QAAQ,KAAKgI,CAAC,CAAC/lB,MAAM,IAAI+lB,CAAC,CAACiK,MAAM,CAAC,QAAQ,EAAEjK,CAAC,CAAChI,GAAG,CAAC;QACzDoI,CAAC,GAAGD,CAAC;QACL,IAAIsB,CAAC,GAAGtV,QAAQ,CAACC,CAAC,EAAEwT,CAAC,EAAEI,CAAC,CAAC;QACzB,IAAI,QAAQ,KAAKyB,CAAC,CAAC5U,IAAI,EAAE;UACvB,IAAIuT,CAAC,GAAGJ,CAAC,CAACM,IAAI,GAAG0I,CAAC,GAAGnJ,CAAC,EAAE4B,CAAC,CAACzJ,GAAG,KAAKiR,CAAC,EAAE;UACrC,OAAO;YACLvsB,KAAK,EAAE+kB,CAAC,CAACzJ,GAAG;YACZsI,IAAI,EAAEN,CAAC,CAACM;UACV,CAAC;QACH;QACA,OAAO,KAAKmB,CAAC,CAAC5U,IAAI,KAAKuT,CAAC,GAAG4I,CAAC,EAAEhJ,CAAC,CAAC/lB,MAAM,GAAG,OAAO,EAAE+lB,CAAC,CAAChI,GAAG,GAAGyJ,CAAC,CAACzJ,GAAG,CAAC;MAClE;IACF,CAAC;EACH;EACA,SAAS6R,mBAAmB,CAACzd,CAAC,EAAEwT,CAAC,EAAE;IACjC,IAAII,CAAC,GAAGJ,CAAC,CAAC3lB,MAAM;MACdmmB,CAAC,GAAGhU,CAAC,CAAC2T,QAAQ,CAACC,CAAC,CAAC;IACnB,IAAII,CAAC,KAAK9gB,CAAC,EAAE,OAAOsgB,CAAC,CAACgK,QAAQ,GAAG,IAAI,EAAE,OAAO,KAAK5J,CAAC,IAAI5T,CAAC,CAAC2T,QAAQ,CAAC,QAAQ,CAAC,KAAKH,CAAC,CAAC3lB,MAAM,GAAG,QAAQ,EAAE2lB,CAAC,CAAC5H,GAAG,GAAG1Y,CAAC,EAAEuqB,mBAAmB,CAACzd,CAAC,EAAEwT,CAAC,CAAC,EAAE,OAAO,KAAKA,CAAC,CAAC3lB,MAAM,CAAC,IAAI,QAAQ,KAAK+lB,CAAC,KAAKJ,CAAC,CAAC3lB,MAAM,GAAG,OAAO,EAAE2lB,CAAC,CAAC5H,GAAG,GAAG,IAAI8I,SAAS,CAAC,mCAAmC,GAAGd,CAAC,GAAG,UAAU,CAAC,CAAC,EAAEiJ,CAAC;IAC3R,IAAIp0B,CAAC,GAAGsX,QAAQ,CAACiU,CAAC,EAAEhU,CAAC,CAAC2T,QAAQ,EAAEH,CAAC,CAAC5H,GAAG,CAAC;IACtC,IAAI,OAAO,KAAKnjB,CAAC,CAACgY,IAAI,EAAE,OAAO+S,CAAC,CAAC3lB,MAAM,GAAG,OAAO,EAAE2lB,CAAC,CAAC5H,GAAG,GAAGnjB,CAAC,CAACmjB,GAAG,EAAE4H,CAAC,CAACgK,QAAQ,GAAG,IAAI,EAAEX,CAAC;IACtF,IAAI/I,CAAC,GAAGrrB,CAAC,CAACmjB,GAAG;IACb,OAAOkI,CAAC,GAAGA,CAAC,CAACI,IAAI,IAAIV,CAAC,CAACxT,CAAC,CAAC8d,UAAU,CAAC,GAAGhK,CAAC,CAACxjB,KAAK,EAAEkjB,CAAC,CAACS,IAAI,GAAGjU,CAAC,CAAC+d,OAAO,EAAE,QAAQ,KAAKvK,CAAC,CAAC3lB,MAAM,KAAK2lB,CAAC,CAAC3lB,MAAM,GAAG,MAAM,EAAE2lB,CAAC,CAAC5H,GAAG,GAAG1Y,CAAC,CAAC,EAAEsgB,CAAC,CAACgK,QAAQ,GAAG,IAAI,EAAEX,CAAC,IAAI/I,CAAC,IAAIN,CAAC,CAAC3lB,MAAM,GAAG,OAAO,EAAE2lB,CAAC,CAAC5H,GAAG,GAAG,IAAI8I,SAAS,CAAC,kCAAkC,CAAC,EAAElB,CAAC,CAACgK,QAAQ,GAAG,IAAI,EAAEX,CAAC,CAAC;EAChQ;EACA,SAASmB,YAAY,CAAC9qB,CAAC,EAAE;IACvB,IAAI8M,CAAC,GAAG;MACNie,MAAM,EAAE/qB,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,IAAIA,CAAC,KAAK8M,CAAC,CAACke,QAAQ,GAAGhrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAIA,CAAC,KAAK8M,CAAC,CAACme,UAAU,GAAGjrB,CAAC,CAAC,CAAC,CAAC,EAAE8M,CAAC,CAACoe,QAAQ,GAAGlrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACmrB,UAAU,CAAClxB,IAAI,CAAC6S,CAAC,CAAC;EAC5G;EACA,SAASse,aAAa,CAACprB,CAAC,EAAE;IACxB,IAAI8M,CAAC,GAAG9M,CAAC,CAACqrB,UAAU,IAAI,CAAC,CAAC;IAC1Bve,CAAC,CAACS,IAAI,GAAG,QAAQ,EAAE,OAAOT,CAAC,CAAC4L,GAAG,EAAE1Y,CAAC,CAACqrB,UAAU,GAAGve,CAAC;EACnD;EACA,SAASyc,OAAO,CAACvpB,CAAC,EAAE;IAClB,IAAI,CAACmrB,UAAU,GAAG,CAAC;MACjBJ,MAAM,EAAE;IACV,CAAC,CAAC,EAAE/qB,CAAC,CAAC5G,OAAO,CAAC0xB,YAAY,EAAE,IAAI,CAAC,EAAE,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;EACnD;EACA,SAAS5qB,MAAM,CAACoM,CAAC,EAAE;IACjB,IAAIA,CAAC,IAAI,EAAE,KAAKA,CAAC,EAAE;MACjB,IAAIwT,CAAC,GAAGxT,CAAC,CAAC8T,CAAC,CAAC;MACZ,IAAIN,CAAC,EAAE,OAAOA,CAAC,CAACloB,IAAI,CAAC0U,CAAC,CAAC;MACvB,IAAI,UAAU,IAAI,OAAOA,CAAC,CAACiU,IAAI,EAAE,OAAOjU,CAAC;MACzC,IAAI,CAAC5I,KAAK,CAAC4I,CAAC,CAAC5X,MAAM,CAAC,EAAE;QACpB,IAAI4rB,CAAC,GAAG,CAAC,CAAC;UACRvrB,CAAC,GAAG,SAASwrB,IAAI,GAAG;YAClB,OAAO,EAAED,CAAC,GAAGhU,CAAC,CAAC5X,MAAM;cAAG,IAAIwrB,CAAC,CAACtoB,IAAI,CAAC0U,CAAC,EAAEgU,CAAC,CAAC,EAAE,OAAOC,IAAI,CAAC3jB,KAAK,GAAG0P,CAAC,CAACgU,CAAC,CAAC,EAAEC,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,EAAED,IAAI;YAAC;YACzF,OAAOA,IAAI,CAAC3jB,KAAK,GAAG4C,CAAC,EAAE+gB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,EAAED,IAAI;UAC7C,CAAC;QACH,OAAOxrB,CAAC,CAACwrB,IAAI,GAAGxrB,CAAC;MACnB;IACF;IACA,MAAM,IAAIisB,SAAS,CAACI,OAAO,CAAC9U,CAAC,CAAC,GAAG,kBAAkB,CAAC;EACtD;EACA,OAAO8c,iBAAiB,CAACzyB,SAAS,GAAG0yB,0BAA0B,EAAE/I,CAAC,CAACkJ,CAAC,EAAE,aAAa,EAAE;IACnF5sB,KAAK,EAAEysB,0BAA0B;IACjC3Y,YAAY,EAAE,CAAC;EACjB,CAAC,CAAC,EAAE4P,CAAC,CAAC+I,0BAA0B,EAAE,aAAa,EAAE;IAC/CzsB,KAAK,EAAEwsB,iBAAiB;IACxB1Y,YAAY,EAAE,CAAC;EACjB,CAAC,CAAC,EAAE0Y,iBAAiB,CAAC2B,WAAW,GAAGnC,MAAM,CAACS,0BAA0B,EAAElJ,CAAC,EAAE,mBAAmB,CAAC,EAAE7T,CAAC,CAAC0e,mBAAmB,GAAG,UAAUxrB,CAAC,EAAE;IACnI,IAAI8M,CAAC,GAAG,UAAU,IAAI,OAAO9M,CAAC,IAAIA,CAAC,CAAC7C,WAAW;IAC/C,OAAO,CAAC,CAAC2P,CAAC,KAAKA,CAAC,KAAK8c,iBAAiB,IAAI,mBAAmB,MAAM9c,CAAC,CAACye,WAAW,IAAIze,CAAC,CAACrR,IAAI,CAAC,CAAC;EAC9F,CAAC,EAAEqR,CAAC,CAAC2e,IAAI,GAAG,UAAUzrB,CAAC,EAAE;IACvB,OAAOpI,MAAM,CAACkqB,cAAc,GAAGlqB,MAAM,CAACkqB,cAAc,CAAC9hB,CAAC,EAAE6pB,0BAA0B,CAAC,IAAI7pB,CAAC,CAAC8C,SAAS,GAAG+mB,0BAA0B,EAAET,MAAM,CAACppB,CAAC,EAAE2gB,CAAC,EAAE,mBAAmB,CAAC,CAAC,EAAE3gB,CAAC,CAAC7I,SAAS,GAAGS,MAAM,CAACa,MAAM,CAACuxB,CAAC,CAAC,EAAEhqB,CAAC;EACxM,CAAC,EAAE8M,CAAC,CAAC4e,KAAK,GAAG,UAAU1rB,CAAC,EAAE;IACxB,OAAO;MACLoqB,OAAO,EAAEpqB;IACX,CAAC;EACH,CAAC,EAAEiqB,qBAAqB,CAACE,aAAa,CAAChzB,SAAS,CAAC,EAAEiyB,MAAM,CAACe,aAAa,CAAChzB,SAAS,EAAErB,CAAC,EAAE,YAAY;IAChG,OAAO,IAAI;EACb,CAAC,CAAC,EAAEgX,CAAC,CAACqd,aAAa,GAAGA,aAAa,EAAErd,CAAC,CAAC6e,KAAK,GAAG,UAAU3rB,CAAC,EAAEsgB,CAAC,EAAEI,CAAC,EAAEI,CAAC,EAAEvrB,CAAC,EAAE;IACtE,KAAK,CAAC,KAAKA,CAAC,KAAKA,CAAC,GAAG6F,OAAO,CAAC;IAC7B,IAAIwlB,CAAC,GAAG,IAAIuJ,aAAa,CAACd,IAAI,CAACrpB,CAAC,EAAEsgB,CAAC,EAAEI,CAAC,EAAEI,CAAC,CAAC,EAAEvrB,CAAC,CAAC;IAC9C,OAAOuX,CAAC,CAAC0e,mBAAmB,CAAClL,CAAC,CAAC,GAAGM,CAAC,GAAGA,CAAC,CAACG,IAAI,EAAE,CAAC9lB,IAAI,CAAC,UAAU+E,CAAC,EAAE;MAC/D,OAAOA,CAAC,CAACghB,IAAI,GAAGhhB,CAAC,CAAC5C,KAAK,GAAGwjB,CAAC,CAACG,IAAI,EAAE;IACpC,CAAC,CAAC;EACJ,CAAC,EAAEkJ,qBAAqB,CAACD,CAAC,CAAC,EAAEZ,MAAM,CAACY,CAAC,EAAErJ,CAAC,EAAE,WAAW,CAAC,EAAEyI,MAAM,CAACY,CAAC,EAAEpJ,CAAC,EAAE,YAAY;IAC/E,OAAO,IAAI;EACb,CAAC,CAAC,EAAEwI,MAAM,CAACY,CAAC,EAAE,UAAU,EAAE,YAAY;IACpC,OAAO,oBAAoB;EAC7B,CAAC,CAAC,EAAEld,CAAC,CAAC5T,IAAI,GAAG,UAAU8G,CAAC,EAAE;IACxB,IAAI8M,CAAC,GAAGlV,MAAM,CAACoI,CAAC,CAAC;MACfsgB,CAAC,GAAG,EAAE;IACR,KAAK,IAAII,CAAC,IAAI5T,CAAC;MAAEwT,CAAC,CAACrmB,IAAI,CAACymB,CAAC,CAAC;IAAC;IAC3B,OAAOJ,CAAC,CAACsL,OAAO,EAAE,EAAE,SAAS7K,IAAI,GAAG;MAClC,OAAOT,CAAC,CAACprB,MAAM,GAAG;QAChB,IAAI8K,CAAC,GAAGsgB,CAAC,CAAC6F,GAAG,EAAE;QACf,IAAInmB,CAAC,IAAI8M,CAAC,EAAE,OAAOiU,IAAI,CAAC3jB,KAAK,GAAG4C,CAAC,EAAE+gB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,EAAED,IAAI;MACzD;MACA,OAAOA,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,EAAED,IAAI;IAC7B,CAAC;EACH,CAAC,EAAEjU,CAAC,CAACpM,MAAM,GAAGA,MAAM,EAAE6oB,OAAO,CAACpyB,SAAS,GAAG;IACxCgG,WAAW,EAAEosB,OAAO;IACpB+B,KAAK,EAAE,SAASA,KAAK,CAACxe,CAAC,EAAE;MACvB,IAAI,IAAI,CAAC+e,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC9K,IAAI,GAAG,CAAC,EAAE,IAAI,CAACyJ,IAAI,GAAG,IAAI,CAACC,KAAK,GAAGzqB,CAAC,EAAE,IAAI,CAACghB,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAACsJ,QAAQ,GAAG,IAAI,EAAE,IAAI,CAAC3vB,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC+d,GAAG,GAAG1Y,CAAC,EAAE,IAAI,CAACmrB,UAAU,CAAC/xB,OAAO,CAACgyB,aAAa,CAAC,EAAE,CAACte,CAAC,EAAE,KAAK,IAAIwT,CAAC,IAAI,IAAI;QAAE,GAAG,KAAKA,CAAC,CAAC9qB,MAAM,CAAC,CAAC,CAAC,IAAIkrB,CAAC,CAACtoB,IAAI,CAAC,IAAI,EAAEkoB,CAAC,CAAC,IAAI,CAACpc,KAAK,CAAC,CAACoc,CAAC,CAACrrB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAACqrB,CAAC,CAAC,GAAGtgB,CAAC,CAAC;MAAC;IACzR,CAAC;IACD8rB,IAAI,EAAE,SAASA,IAAI,GAAG;MACpB,IAAI,CAAC9K,IAAI,GAAG,CAAC,CAAC;MACd,IAAIhhB,CAAC,GAAG,IAAI,CAACmrB,UAAU,CAAC,CAAC,CAAC,CAACE,UAAU;MACrC,IAAI,OAAO,KAAKrrB,CAAC,CAACuN,IAAI,EAAE,MAAMvN,CAAC,CAAC0Y,GAAG;MACnC,OAAO,IAAI,CAACqT,IAAI;IAClB,CAAC;IACDrB,iBAAiB,EAAE,SAASA,iBAAiB,CAAC5d,CAAC,EAAE;MAC/C,IAAI,IAAI,CAACkU,IAAI,EAAE,MAAMlU,CAAC;MACtB,IAAIwT,CAAC,GAAG,IAAI;MACZ,SAAS0L,MAAM,CAACtL,CAAC,EAAEI,CAAC,EAAE;QACpB,OAAOF,CAAC,CAACrT,IAAI,GAAG,OAAO,EAAEqT,CAAC,CAAClI,GAAG,GAAG5L,CAAC,EAAEwT,CAAC,CAACS,IAAI,GAAGL,CAAC,EAAEI,CAAC,KAAKR,CAAC,CAAC3lB,MAAM,GAAG,MAAM,EAAE2lB,CAAC,CAAC5H,GAAG,GAAG1Y,CAAC,CAAC,EAAE,CAAC,CAAC8gB,CAAC;MAC1F;MACA,KAAK,IAAIA,CAAC,GAAG,IAAI,CAACqK,UAAU,CAACj2B,MAAM,GAAG,CAAC,EAAE4rB,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACpD,IAAIvrB,CAAC,GAAG,IAAI,CAAC41B,UAAU,CAACrK,CAAC,CAAC;UACxBF,CAAC,GAAGrrB,CAAC,CAAC81B,UAAU;QAClB,IAAI,MAAM,KAAK91B,CAAC,CAACw1B,MAAM,EAAE,OAAOiB,MAAM,CAAC,KAAK,CAAC;QAC7C,IAAIz2B,CAAC,CAACw1B,MAAM,IAAI,IAAI,CAACc,IAAI,EAAE;UACzB,IAAI/1B,CAAC,GAAG4qB,CAAC,CAACtoB,IAAI,CAAC7C,CAAC,EAAE,UAAU,CAAC;YAC3BorB,CAAC,GAAGD,CAAC,CAACtoB,IAAI,CAAC7C,CAAC,EAAE,YAAY,CAAC;UAC7B,IAAIO,CAAC,IAAI6qB,CAAC,EAAE;YACV,IAAI,IAAI,CAACkL,IAAI,GAAGt2B,CAAC,CAACy1B,QAAQ,EAAE,OAAOgB,MAAM,CAACz2B,CAAC,CAACy1B,QAAQ,EAAE,CAAC,CAAC,CAAC;YACzD,IAAI,IAAI,CAACa,IAAI,GAAGt2B,CAAC,CAAC01B,UAAU,EAAE,OAAOe,MAAM,CAACz2B,CAAC,CAAC01B,UAAU,CAAC;UAC3D,CAAC,MAAM,IAAIn1B,CAAC,EAAE;YACZ,IAAI,IAAI,CAAC+1B,IAAI,GAAGt2B,CAAC,CAACy1B,QAAQ,EAAE,OAAOgB,MAAM,CAACz2B,CAAC,CAACy1B,QAAQ,EAAE,CAAC,CAAC,CAAC;UAC3D,CAAC,MAAM;YACL,IAAI,CAACrK,CAAC,EAAE,MAAM3rB,KAAK,CAAC,wCAAwC,CAAC;YAC7D,IAAI,IAAI,CAAC62B,IAAI,GAAGt2B,CAAC,CAAC01B,UAAU,EAAE,OAAOe,MAAM,CAACz2B,CAAC,CAAC01B,UAAU,CAAC;UAC3D;QACF;MACF;IACF,CAAC;IACDN,MAAM,EAAE,SAASA,MAAM,CAAC3qB,CAAC,EAAE8M,CAAC,EAAE;MAC5B,KAAK,IAAIwT,CAAC,GAAG,IAAI,CAAC6K,UAAU,CAACj2B,MAAM,GAAG,CAAC,EAAEorB,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACpD,IAAIQ,CAAC,GAAG,IAAI,CAACqK,UAAU,CAAC7K,CAAC,CAAC;QAC1B,IAAIQ,CAAC,CAACiK,MAAM,IAAI,IAAI,CAACc,IAAI,IAAInL,CAAC,CAACtoB,IAAI,CAAC0oB,CAAC,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC+K,IAAI,GAAG/K,CAAC,CAACmK,UAAU,EAAE;UAChF,IAAI11B,CAAC,GAAGurB,CAAC;UACT;QACF;MACF;MACAvrB,CAAC,KAAK,OAAO,KAAKyK,CAAC,IAAI,UAAU,KAAKA,CAAC,CAAC,IAAIzK,CAAC,CAACw1B,MAAM,IAAIje,CAAC,IAAIA,CAAC,IAAIvX,CAAC,CAAC01B,UAAU,KAAK11B,CAAC,GAAG,IAAI,CAAC;MAC5F,IAAIqrB,CAAC,GAAGrrB,CAAC,GAAGA,CAAC,CAAC81B,UAAU,GAAG,CAAC,CAAC;MAC7B,OAAOzK,CAAC,CAACrT,IAAI,GAAGvN,CAAC,EAAE4gB,CAAC,CAAClI,GAAG,GAAG5L,CAAC,EAAEvX,CAAC,IAAI,IAAI,CAACoF,MAAM,GAAG,MAAM,EAAE,IAAI,CAAComB,IAAI,GAAGxrB,CAAC,CAAC01B,UAAU,EAAEtB,CAAC,IAAI,IAAI,CAACjsB,QAAQ,CAACkjB,CAAC,CAAC;IAC1G,CAAC;IACDljB,QAAQ,EAAE,SAASA,QAAQ,CAACsC,CAAC,EAAE8M,CAAC,EAAE;MAChC,IAAI,OAAO,KAAK9M,CAAC,CAACuN,IAAI,EAAE,MAAMvN,CAAC,CAAC0Y,GAAG;MACnC,OAAO,OAAO,KAAK1Y,CAAC,CAACuN,IAAI,IAAI,UAAU,KAAKvN,CAAC,CAACuN,IAAI,GAAG,IAAI,CAACwT,IAAI,GAAG/gB,CAAC,CAAC0Y,GAAG,GAAG,QAAQ,KAAK1Y,CAAC,CAACuN,IAAI,IAAI,IAAI,CAACwe,IAAI,GAAG,IAAI,CAACrT,GAAG,GAAG1Y,CAAC,CAAC0Y,GAAG,EAAE,IAAI,CAAC/d,MAAM,GAAG,QAAQ,EAAE,IAAI,CAAComB,IAAI,GAAG,KAAK,IAAI,QAAQ,KAAK/gB,CAAC,CAACuN,IAAI,IAAIT,CAAC,KAAK,IAAI,CAACiU,IAAI,GAAGjU,CAAC,CAAC,EAAE6c,CAAC;IAC3N,CAAC;IACDsC,MAAM,EAAE,SAASA,MAAM,CAACjsB,CAAC,EAAE;MACzB,KAAK,IAAI8M,CAAC,GAAG,IAAI,CAACqe,UAAU,CAACj2B,MAAM,GAAG,CAAC,EAAE4X,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACpD,IAAIwT,CAAC,GAAG,IAAI,CAAC6K,UAAU,CAACre,CAAC,CAAC;QAC1B,IAAIwT,CAAC,CAAC2K,UAAU,KAAKjrB,CAAC,EAAE,OAAO,IAAI,CAACtC,QAAQ,CAAC4iB,CAAC,CAAC+K,UAAU,EAAE/K,CAAC,CAAC4K,QAAQ,CAAC,EAAEE,aAAa,CAAC9K,CAAC,CAAC,EAAEqJ,CAAC;MAC7F;IACF,CAAC;IACD,OAAO,EAAE,SAASuC,MAAM,CAAClsB,CAAC,EAAE;MAC1B,KAAK,IAAI8M,CAAC,GAAG,IAAI,CAACqe,UAAU,CAACj2B,MAAM,GAAG,CAAC,EAAE4X,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACpD,IAAIwT,CAAC,GAAG,IAAI,CAAC6K,UAAU,CAACre,CAAC,CAAC;QAC1B,IAAIwT,CAAC,CAACyK,MAAM,KAAK/qB,CAAC,EAAE;UAClB,IAAI0gB,CAAC,GAAGJ,CAAC,CAAC+K,UAAU;UACpB,IAAI,OAAO,KAAK3K,CAAC,CAACnT,IAAI,EAAE;YACtB,IAAIuT,CAAC,GAAGJ,CAAC,CAAChI,GAAG;YACb0S,aAAa,CAAC9K,CAAC,CAAC;UAClB;UACA,OAAOQ,CAAC;QACV;MACF;MACA,MAAM9rB,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC;IACDm3B,aAAa,EAAE,SAASA,aAAa,CAACrf,CAAC,EAAEwT,CAAC,EAAEI,CAAC,EAAE;MAC7C,OAAO,IAAI,CAAC4J,QAAQ,GAAG;QACrB7J,QAAQ,EAAE/f,MAAM,CAACoM,CAAC,CAAC;QACnB8d,UAAU,EAAEtK,CAAC;QACbuK,OAAO,EAAEnK;MACX,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC/lB,MAAM,KAAK,IAAI,CAAC+d,GAAG,GAAG1Y,CAAC,CAAC,EAAE2pB,CAAC;IAChD;EACF,CAAC,EAAE7c,CAAC;AACN;AACA8S,MAAM,CAACC,OAAO,GAAGoJ,mBAAmB,EAAErJ,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;AC/SlH,SAASuM,kBAAkB,CAACC,GAAG,EAAEhxB,OAAO,EAAEgB,MAAM,EAAEiwB,KAAK,EAAEC,MAAM,EAAE14B,GAAG,EAAE6kB,GAAG,EAAE;EACzE,IAAI;IACF,IAAI8T,IAAI,GAAGH,GAAG,CAACx4B,GAAG,CAAC,CAAC6kB,GAAG,CAAC;IACxB,IAAItb,KAAK,GAAGovB,IAAI,CAACpvB,KAAK;EACxB,CAAC,CAAC,OAAOvG,KAAK,EAAE;IACdwF,MAAM,CAACxF,KAAK,CAAC;IACb;EACF;EACA,IAAI21B,IAAI,CAACxL,IAAI,EAAE;IACb3lB,OAAO,CAAC+B,KAAK,CAAC;EAChB,CAAC,MAAM;IACLhC,OAAO,CAACC,OAAO,CAAC+B,KAAK,CAAC,CAACnC,IAAI,CAACqxB,KAAK,EAAEC,MAAM,CAAC;EAC5C;AACF;AACA,SAASE,iBAAiB,CAAC10B,EAAE,EAAE;EAC7B,OAAO,YAAY;IACjB,IAAI20B,IAAI,GAAG,IAAI;MACb9oB,IAAI,GAAG6I,SAAS;IAClB,OAAO,IAAIrR,OAAO,CAAC,UAAUC,OAAO,EAAEgB,MAAM,EAAE;MAC5C,IAAIgwB,GAAG,GAAGt0B,EAAE,CAACuT,KAAK,CAACohB,IAAI,EAAE9oB,IAAI,CAAC;MAC9B,SAAS0oB,KAAK,CAAClvB,KAAK,EAAE;QACpBgvB,kBAAkB,CAACC,GAAG,EAAEhxB,OAAO,EAAEgB,MAAM,EAAEiwB,KAAK,EAAEC,MAAM,EAAE,MAAM,EAAEnvB,KAAK,CAAC;MACxE;MACA,SAASmvB,MAAM,CAACvvB,GAAG,EAAE;QACnBovB,kBAAkB,CAACC,GAAG,EAAEhxB,OAAO,EAAEgB,MAAM,EAAEiwB,KAAK,EAAEC,MAAM,EAAE,OAAO,EAAEvvB,GAAG,CAAC;MACvE;MACAsvB,KAAK,CAACpkB,SAAS,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC;AACH;AACA0X,MAAM,CAACC,OAAO,GAAG4M,iBAAiB,EAAE7M,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,C;;;;;;;;;;;;;AC9BhH;AAAA;AAAA;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACrHAN,GAAG,CAAC7kB,cAAc,CAAC;EACjBmB,WAAW,uBAAElC,GAAG,EAAE;IAChB,IAAI,EAAE,CAAC,CAACA,GAAG,KAAK,QAAOA,GAAG,MAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOA,GAAG,CAACsB,IAAI,KAAK,UAAU,CAAC,EAAE;MACxG,OAAOtB,GAAG;IACZ;IACA,OAAO,IAAIyB,OAAO,CAAC,UAACC,OAAO,EAAEgB,MAAM,EAAK;MACtC1C,GAAG,CAACsB,IAAI,CAAC,UAACtB,GAAG,EAAK;QAChB,IAAI,CAACA,GAAG,EAAE,OAAO0B,OAAO,CAAC1B,GAAG,CAAC;QAC7B,OAAOA,GAAG,CAAC,CAAC,CAAC,GAAG0C,MAAM,CAAC1C,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG0B,OAAO,CAAC1B,GAAG,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;ACZF;AAuEA;AArEA;AACO,IAAMgzB,SAAS,GAAG;EACxB;EACAC,UAAU,wBAAG;IACZ,OAAOC,gBAAI,CAACzrB,GAAG,CAAC,kBAAkB,CAAC;EACpC,CAAC;EAED;EACA0rB,gBAAgB,4BAACC,SAAS,EAAE;IAC3B,OAAOF,gBAAI,CAACzrB,GAAG,CAAC,wBAAwB,EAAE;MAAE2rB,SAAS,EAATA;IAAU,CAAC,CAAC;EACzD,CAAC;EAED;EACAC,UAAU,sBAACD,SAAS,EAA8B;IAAA,IAA5BE,OAAO,uEAAG,CAAC;IAAA,IAAEC,QAAQ,uEAAG,EAAE;IAC/C,OAAOL,gBAAI,CAACzrB,GAAG,CAAC,kBAAkB,EAAE;MACnC2rB,SAAS,EAATA,SAAS;MACTE,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAARA;IACD,CAAC,CAAC;EACH,CAAC;EAED;EACAC,QAAQ,oBAACJ,SAAS,EAAE;IACnB,OAAOF,gBAAI,CAACO,IAAI,CAAC,qBAAqB,EAAE;MACvCL,SAAS,EAAEA;IACZ,CAAC,EAAE;MACFM,MAAM,EAAE;QACP,cAAc,EAAE;MACjB;IACD,CAAC,CAAC;EACH;AACD,CAAC;;AAED;AAAA;AACO,IAAMC,QAAQ,GAAG;EACvB;EACAC,QAAQ,oBAACR,SAAS,EAAE;IACnB,OAAOF,gBAAI,CAACzrB,GAAG,CAAC,qBAAqB,EAAE;MAAE2rB,SAAS,EAATA;IAAU,CAAC,CAAC;EACtD,CAAC;EAED;EACAS,YAAY,wBAACT,SAAS,EAAE;IACvB,OAAOF,gBAAI,CAACzrB,GAAG,CAAC,wBAAwB,EAAE;MACzC2rB,SAAS,EAATA,SAAS;MACTE,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC;EAED;EACAO,YAAY,wBAACV,SAAS,EAAE;IACvB,OAAOF,gBAAI,CAACzrB,GAAG,CAAC,qBAAqB,EAAE;MAAE2rB,SAAS,EAATA;IAAU,CAAC,CAAC;EACtD,CAAC;EAED;EACAW,QAAQ,oBAACX,SAAS,EAAEY,SAAS,EAAE;IAC9B,OAAOd,gBAAI,CAACe,GAAG,CAAC,kBAAkB,EAAE;MACnCb,SAAS,EAATA,SAAS;MACTY,SAAS,EAATA;IACD,CAAC,CAAC;EACH,CAAC;EAED;EACAE,YAAY,0BAAG;IACd,OAAOhB,gBAAI,CAACzrB,GAAG,CAAC,0BAA0B,CAAC;EAC5C;AACD,CAAC;;AAED;AAAA;AAGA;AACO,IAAM0sB,KAAK,GAAG;EACpB;EACAC,YAAY,wBAACn5B,GAAG,EAAE;IACjB,IAAI;MACH;MACA,IAAI,OAAO2qB,GAAG,CAACwO,YAAY,KAAK,UAAU,EAAE;QAC3C,OAAOxO,GAAG,CAACwO,YAAY,CAACn5B,GAAG,CAAC;MAC7B;;MAEA;MACA,OAAO,IAAAo5B,kBAAkB,EAACn5B,MAAM,CAACD,GAAG,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACfkU,OAAO,CAAClU,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,OAAOhC,MAAM,CAACD,GAAG,CAAC;IACnB;EACD,CAAC;EAED;EACAq5B,YAAY,wBAACr5B,GAAG,EAAE;IACjB,IAAI;MACH;MACA,IAAI,OAAO2qB,GAAG,CAAC0O,YAAY,KAAK,UAAU,EAAE;QAC3C,OAAO1O,GAAG,CAAC0O,YAAY,CAACr5B,GAAG,CAAC;MAC7B;;MAEA;MACA,OAAO,IAAAs5B,kBAAkB,EAACr5B,MAAM,CAACD,GAAG,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACfkU,OAAO,CAAClU,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,OAAOhC,MAAM,CAACD,GAAG,CAAC;IACnB;EACD,CAAC;EAED;EACAu5B,YAAY,wBAACv5B,GAAG,EAAE;IACjB,IAAI;MACHA,GAAG,GAAGC,MAAM,CAACD,GAAG,CAAC;MACjB,IAAIQ,MAAM,GAAG,EAAE;MACf,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,GAAG,CAACM,MAAM,EAAEK,CAAC,EAAE,EAAE;QACpC,IAAM64B,GAAG,GAAGx5B,GAAG,CAACmB,UAAU,CAACR,CAAC,CAAC,CAACS,QAAQ,CAAC,EAAE,CAAC;QAC1CZ,MAAM,IAAIg5B,GAAG,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC/B;MACA,OAAOj5B,MAAM;IACd,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACfkU,OAAO,CAAClU,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,OAAOhC,MAAM,CAACD,GAAG,CAAC;IACnB;EACD,CAAC;EAED;EACA05B,YAAY,wBAACC,MAAM,EAAE;IACpB,IAAI;MACHA,MAAM,GAAG15B,MAAM,CAAC05B,MAAM,CAAC;MACvB,IAAIn5B,MAAM,GAAG,EAAE;MACf,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGg5B,MAAM,CAACr5B,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;QAC1C,IAAM64B,GAAG,GAAGG,MAAM,CAAC7W,MAAM,CAACniB,CAAC,EAAE,CAAC,CAAC;QAC/B,IAAMi5B,QAAQ,GAAGxqB,QAAQ,CAACoqB,GAAG,EAAE,EAAE,CAAC;QAClC,IAAI,CAAClqB,KAAK,CAACsqB,QAAQ,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;UACrCp5B,MAAM,IAAIP,MAAM,CAACY,YAAY,CAAC+4B,QAAQ,CAAC;QACxC;MACD;MACA,OAAOp5B,MAAM;IACd,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACfkU,OAAO,CAAClU,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,OAAOhC,MAAM,CAAC05B,MAAM,CAAC;IACtB;EACD,CAAC;EAGD;EACAE,OAAO,mBAAC1zB,IAAI,EAA0C;IAAA,IAAxClH,GAAG,uEAAG,aAAa;IAAA,IAAE66B,EAAE,uEAAG,YAAY;IACnD,IAAI;MACH;MACA,IAAI,sBAAO3zB,IAAI,MAAK,QAAQ,EAAE;QAC7B,IAAI;UACHA,IAAI,GAAGpE,IAAI,CAACme,SAAS,CAAC/Z,IAAI,CAAC;QAC5B,CAAC,CAAC,OAAOlE,KAAK,EAAE;UACfkU,OAAO,CAACgc,GAAG,CAAC,YAAY,EAAElwB,KAAK,CAAC;UAChC,OAAOkE,IAAI;QACZ;MACD;;MAEA;MACA,IAAM4zB,OAAO,GAAG95B,MAAM,CAACkG,IAAI,CAAC;;MAE5B;MACA,IAAM6zB,MAAM,GAAG/6B,GAAG,GAAG66B,EAAE;MACvB,IAAIG,SAAS,GAAG,EAAE;MAElB,KAAK,IAAIt5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGo5B,OAAO,CAACz5B,MAAM,EAAEK,CAAC,EAAE,EAAE;QACxC,IAAMu5B,QAAQ,GAAGH,OAAO,CAAC54B,UAAU,CAACR,CAAC,CAAC;QACtC,IAAMw5B,OAAO,GAAGH,MAAM,CAAC74B,UAAU,CAACR,CAAC,GAAGq5B,MAAM,CAAC15B,MAAM,CAAC;QACpD;QACA,IAAM85B,aAAa,GAAGF,QAAQ,GAAGC,OAAO;QACxCF,SAAS,IAAIh6B,MAAM,CAACY,YAAY,CAACu5B,aAAa,CAAC;MAChD;;MAEA;MACA,OAAO,IAAI,CAACjB,YAAY,CAACc,SAAS,CAAC;IACpC,CAAC,CAAC,OAAOh4B,KAAK,EAAE;MACfkU,OAAO,CAAClU,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B;MACA,IAAI;QACH,OAAO,IAAI,CAACs3B,YAAY,CAACt5B,MAAM,CAACkG,IAAI,CAAC,CAAC;MACvC,CAAC,CAAC,OAAOk0B,aAAa,EAAE;QACvBlkB,OAAO,CAAClU,KAAK,CAAC,UAAU,EAAEo4B,aAAa,CAAC;QACxC,OAAOp6B,MAAM,CAACkG,IAAI,CAAC;MACpB;IACD;EACD,CAAC;EAED;EACAm0B,OAAO,mBAACC,aAAa,EAA0C;IAAA,IAAxCt7B,GAAG,uEAAG,aAAa;IAAA,IAAE66B,EAAE,uEAAG,YAAY;IAC5D,IAAI;MACH,IAAI,CAACS,aAAa,EAAE,OAAO,EAAE;;MAE7B;MACA,IAAIC,OAAO;MACX,IAAI;QACHA,OAAO,GAAG,IAAI,CAACnB,YAAY,CAACkB,aAAa,CAAC;MAC3C,CAAC,CAAC,OAAOE,WAAW,EAAE;QACrBtkB,OAAO,CAAClU,KAAK,CAAC,oBAAoB,EAAEw4B,WAAW,CAAC;QAChD;QACAD,OAAO,GAAG,IAAI,CAACd,YAAY,CAACa,aAAa,CAAC;MAC3C;MAEA,IAAI,CAACC,OAAO,EAAE,OAAOD,aAAa;;MAElC;MACA,IAAMP,MAAM,GAAG/6B,GAAG,GAAG66B,EAAE;MACvB,IAAIY,SAAS,GAAG,EAAE;MAElB,KAAK,IAAI/5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG65B,OAAO,CAACl6B,MAAM,EAAEK,CAAC,EAAE,EAAE;QACxC,IAAMy5B,aAAa,GAAGI,OAAO,CAACr5B,UAAU,CAACR,CAAC,CAAC;QAC3C,IAAMw5B,OAAO,GAAGH,MAAM,CAAC74B,UAAU,CAACR,CAAC,GAAGq5B,MAAM,CAAC15B,MAAM,CAAC;QACpD;QACA,IAAMq6B,aAAa,GAAGP,aAAa,GAAGD,OAAO;QAC7CO,SAAS,IAAIz6B,MAAM,CAACY,YAAY,CAAC85B,aAAa,CAAC;MAChD;MAEA,OAAOD,SAAS;IACjB,CAAC,CAAC,OAAOz4B,KAAK,EAAE;MACfkU,OAAO,CAAClU,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,OAAOhC,MAAM,CAACs6B,aAAa,CAAC;IAC7B;EACD,CAAC;EAID;EACAK,oBAAoB,kCAAG;IACtB,IAAM93B,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,IAAMg4B,IAAI,GAAG/3B,GAAG,CAACg4B,WAAW,EAAE;IAC9B,IAAMC,KAAK,GAAG96B,MAAM,CAAC6C,GAAG,CAACk4B,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACvB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,IAAMwB,GAAG,GAAGh7B,MAAM,CAAC6C,GAAG,CAACo4B,OAAO,EAAE,CAAC,CAACzB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,IAAM0B,KAAK,GAAGl7B,MAAM,CAAC6C,GAAG,CAACs4B,QAAQ,EAAE,CAAC,CAAC3B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrD,IAAM4B,OAAO,GAAGp7B,MAAM,CAAC6C,GAAG,CAACw4B,UAAU,EAAE,CAAC,CAAC7B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,IAAM8B,OAAO,GAAGt7B,MAAM,CAAC6C,GAAG,CAAC04B,UAAU,EAAE,CAAC,CAAC/B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEzD,iBAAUoB,IAAI,cAAIE,KAAK,cAAIE,GAAG,cAAIE,KAAK,cAAIE,OAAO,cAAIE,OAAO;EAC9D,CAAC;EAED;EACAE,QAAQ,oBAACzM,IAAI,EAAE;IACd,OAAO,IAAIxoB,OAAO,CAAC,UAACC,OAAO,EAAEgB,MAAM,EAAK;MACvCkjB,GAAG,CAAC+Q,gBAAgB,CAAC;QACpBv1B,IAAI,EAAE6oB,IAAI;QACVpmB,OAAO,EAAE,mBAAM;UACd+hB,GAAG,CAAC+H,SAAS,CAAC;YACb9d,KAAK,EAAE,MAAM;YACb+d,IAAI,EAAE,SAAS;YACfC,QAAQ,EAAE;UACX,CAAC,CAAC;UACFnsB,OAAO,EAAE;QACV,CAAC;QACDoC,IAAI,EAAE,cAACT,GAAG,EAAK;UACduiB,GAAG,CAAC+H,SAAS,CAAC;YACb9d,KAAK,EAAE,MAAM;YACb+d,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;UACX,CAAC,CAAC;UACFnrB,MAAM,CAACW,GAAG,CAAC;QACZ;MACD,CAAC,CAAC;IACH,CAAC,CAAC;EACH,CAAC;EAED;EACAuzB,UAAU,sBAACC,IAAI,EAAyB;IAAA,IAAvBhN,MAAM,uEAAG,YAAY;IACrC,IAAMsG,CAAC,GAAG,IAAIryB,IAAI,CAAC+4B,IAAI,CAAC;IACxB,IAAMf,IAAI,GAAG3F,CAAC,CAAC4F,WAAW,EAAE;IAC5B,IAAMC,KAAK,GAAG96B,MAAM,CAACi1B,CAAC,CAAC8F,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACvB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,IAAMwB,GAAG,GAAGh7B,MAAM,CAACi1B,CAAC,CAACgG,OAAO,EAAE,CAAC,CAACzB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChD,IAAM0B,KAAK,GAAGl7B,MAAM,CAACi1B,CAAC,CAACkG,QAAQ,EAAE,CAAC,CAAC3B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,IAAM4B,OAAO,GAAGp7B,MAAM,CAACi1B,CAAC,CAACoG,UAAU,EAAE,CAAC,CAAC7B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,IAAM8B,OAAO,GAAGt7B,MAAM,CAACi1B,CAAC,CAACsG,UAAU,EAAE,CAAC,CAAC/B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEvD,OAAO7K,MAAM,CACX1uB,OAAO,CAAC,MAAM,EAAE26B,IAAI,CAAC,CACrB36B,OAAO,CAAC,IAAI,EAAE66B,KAAK,CAAC,CACpB76B,OAAO,CAAC,IAAI,EAAE+6B,GAAG,CAAC,CAClB/6B,OAAO,CAAC,IAAI,EAAEi7B,KAAK,CAAC,CACpBj7B,OAAO,CAAC,IAAI,EAAEm7B,OAAO,CAAC,CACtBn7B,OAAO,CAAC,IAAI,EAAEq7B,OAAO,CAAC;EACzB;AACD,CAAC;AAAA,sB;;;;;;;;;;;;;;;;;;;;;ACtRD;AACA;AAA0C;AAAA;AAE1C,IAAMM,QAAQ,GAAGC,cAAM,CAACC,OAAO;;AAE/B;AACA,IAAMC,OAAO,GAAG,SAAVA,OAAO,GAAqB;EAAA,IAAjBp1B,OAAO,uEAAG,CAAC,CAAC;EAC5B,OAAO,IAAIJ,OAAO,CAAC,UAACC,OAAO,EAAEgB,MAAM,EAAK;IACvC;IACA,IAAIb,OAAO,CAACq1B,WAAW,KAAK,KAAK,EAAE;MAClCtR,GAAG,CAACsR,WAAW,CAAC;QACfrnB,KAAK,EAAE,QAAQ;QACfsnB,IAAI,EAAE;MACP,CAAC,CAAC;IACH;;IAEA;IACA,IAAM56B,KAAK,GAAGqpB,GAAG,CAACnpB,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE;;IAEpD;IACA,IAAIq6B,QAAQ,CAACr8B,QAAQ,CAAC,qBAAqB,CAAC,EAAE;MAC7C;MACA,IAAIoH,OAAO,CAACq1B,WAAW,KAAK,KAAK,EAAE;QAClCtR,GAAG,CAACwR,WAAW,EAAE;MAClB;MAEA,IAAMC,QAAQ,GAAG,gBAAgB;MACjC,IAAIx1B,OAAO,CAACy1B,SAAS,KAAK,KAAK,EAAE;QAChC1R,GAAG,CAACqI,SAAS,CAAC;UACbpe,KAAK,EAAE,MAAM;UACbqe,OAAO,EAAE,0CAA0C;UACnDqJ,UAAU,EAAE;QACb,CAAC,CAAC;MACH;MACA70B,MAAM,CAAC,IAAIrH,KAAK,CAACg8B,QAAQ,CAAC,CAAC;MAC3B;IACD;;IAEA;IACA,IAAMG,aAAa,GAAG;MACrBluB,GAAG,EAAEwtB,QAAQ,GAAGj1B,OAAO,CAACyH,GAAG;MAC3BtI,MAAM,EAAEa,OAAO,CAACb,MAAM,IAAI,KAAK;MAC/BI,IAAI,EAAES,OAAO,CAACT,IAAI,IAAI,CAAC,CAAC;MACxBsyB,MAAM;QACL,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAEn3B,KAAK,oBAAaA,KAAK,IAAK;MAAE,GAC5CsF,OAAO,CAAC6xB,MAAM,CACjB;MACD+D,OAAO,EAAE51B,OAAO,CAAC41B,OAAO,IAAI;IAC7B,CAAC;;IAED;IACA,IAAI51B,OAAO,CAAC6xB,MAAM,IAAI7xB,OAAO,CAAC6xB,MAAM,CAAC,cAAc,CAAC,KAAK,mCAAmC,EAAE;MAC7F;MACA,IAAI8D,aAAa,CAACp2B,IAAI,IAAI,sBAAOo2B,aAAa,CAACp2B,IAAI,MAAK,QAAQ,EAAE;QACjE,IAAMs2B,QAAQ,GAAGz5B,MAAM,CAACsB,IAAI,CAACi4B,aAAa,CAACp2B,IAAI,CAAC,CAC9CqJ,MAAM,CAAC,UAAAvQ,GAAG;UAAA,OAAIs9B,aAAa,CAACp2B,IAAI,CAAClH,GAAG,CAAC,KAAKqU,SAAS,IAAIipB,aAAa,CAACp2B,IAAI,CAAClH,GAAG,CAAC,KAAK,IAAI;QAAA,EAAC,CACxFgC,GAAG,CAAC,UAAAhC,GAAG;UAAA,iBAAO0oB,kBAAkB,CAAC1oB,GAAG,CAAC,cAAI0oB,kBAAkB,CAAC4U,aAAa,CAACp2B,IAAI,CAAClH,GAAG,CAAC,CAAC;QAAA,CAAE,CAAC,CACvFC,IAAI,CAAC,GAAG,CAAC;QACXq9B,aAAa,CAACp2B,IAAI,GAAGs2B,QAAQ;MAC9B;IACD;;IAEA;IACA,IAAI71B,OAAO,CAACV,MAAM,EAAE;MACnB;MACA,IAAMA,MAAM,GAAGlD,MAAM,CAACsB,IAAI,CAACsC,OAAO,CAACV,MAAM,CAAC,CACxCsJ,MAAM,CAAC,UAAAvQ,GAAG;QAAA,OAAI2H,OAAO,CAACV,MAAM,CAACjH,GAAG,CAAC,KAAKqU,SAAS,IAAI1M,OAAO,CAACV,MAAM,CAACjH,GAAG,CAAC,KAAK,IAAI;MAAA,EAAC,CAChFgC,GAAG,CAAC,UAAAhC,GAAG;QAAA,iBAAO0oB,kBAAkB,CAAC1oB,GAAG,CAAC,cAAI0oB,kBAAkB,CAAC/gB,OAAO,CAACV,MAAM,CAACjH,GAAG,CAAC,CAAC;MAAA,CAAE,CAAC,CACnFC,IAAI,CAAC,GAAG,CAAC;MAEX,IAAIgH,MAAM,EAAE;QACXq2B,aAAa,CAACluB,GAAG,IAAI,CAACkuB,aAAa,CAACluB,GAAG,CAAC7O,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI0G,MAAM;MAC5E;IACD;;IAEA;IACAykB,GAAG,CAACqR,OAAO,iCACPO,aAAa;MAChB3zB,OAAO,EAAE,iBAAC7D,GAAG,EAAK;QACjB;QACA,IAAI6B,OAAO,CAACq1B,WAAW,KAAK,KAAK,EAAE;UAClCtR,GAAG,CAACwR,WAAW,EAAE;QAClB;;QAEA;QACA,IAAIp3B,GAAG,CAAC23B,UAAU,KAAK,GAAG,EAAE;UAC3B;UACA,IAAI33B,GAAG,CAACoB,IAAI,CAACw2B,IAAI,KAAK,GAAG,EAAE;YAC1Bl2B,OAAO,CAAC1B,GAAG,CAACoB,IAAI,CAAC;UAClB,CAAC,MAAM,IAAIpB,GAAG,CAACoB,IAAI,CAACw2B,IAAI,KAAK,GAAG,EAAE;YACjC;YACAxmB,OAAO,CAACgc,GAAG,CAAC,wBAAwB,CAAC;YACrC,IAAAC,iBAAW,GAAE;YAEb,IAAMgK,SAAQ,GAAGr3B,GAAG,CAACoB,IAAI,CAACy2B,GAAG,IAAI,aAAa;YAC9C,IAAIh2B,OAAO,CAACy1B,SAAS,KAAK,KAAK,EAAE;cAChC1R,GAAG,CAAC+H,SAAS,CAAC;gBACb9d,KAAK,EAAEwnB,SAAQ;gBACfzJ,IAAI,EAAE,MAAM;gBACZC,QAAQ,EAAE;cACX,CAAC,CAAC;YACH;;YAEA;YACAC,UAAU,CAAC,YAAM;cAChBlI,GAAG,CAACwI,QAAQ,CAAC;gBACZ9kB,GAAG,EAAE;cACN,CAAC,CAAC;YACH,CAAC,EAAE,IAAI,CAAC;YAER5G,MAAM,CAAC,IAAIrH,KAAK,CAACg8B,SAAQ,CAAC,CAAC;UAC5B,CAAC,MAAM;YACN;YACA,IAAMA,UAAQ,GAAGr3B,GAAG,CAACoB,IAAI,CAACy2B,GAAG,IAAI73B,GAAG,CAACoB,IAAI,CAACjE,OAAO,IAAI,MAAM;YAC3D,IAAI0E,OAAO,CAACy1B,SAAS,KAAK,KAAK,EAAE;cAChC1R,GAAG,CAAC+H,SAAS,CAAC;gBACb9d,KAAK,EAAEwnB,UAAQ;gBACfzJ,IAAI,EAAE,MAAM;gBACZC,QAAQ,EAAE;cACX,CAAC,CAAC;YACH;YACAnrB,MAAM,CAAC,IAAIrH,KAAK,CAACg8B,UAAQ,CAAC,CAAC;UAC5B;QACD,CAAC,MAAM,IAAIr3B,GAAG,CAAC23B,UAAU,KAAK,GAAG,EAAE;UAClC;UACAvmB,OAAO,CAACgc,GAAG,CAAC,6BAA6B,CAAC;UAC1C,IAAAC,iBAAW,GAAE;UAEb,IAAMgK,UAAQ,GAAG,aAAa;UAC9B,IAAIx1B,OAAO,CAACy1B,SAAS,KAAK,KAAK,EAAE;YAChC1R,GAAG,CAAC+H,SAAS,CAAC;cACb9d,KAAK,EAAEwnB,UAAQ;cACfzJ,IAAI,EAAE,MAAM;cACZC,QAAQ,EAAE;YACX,CAAC,CAAC;UACH;;UAEA;UACAC,UAAU,CAAC,YAAM;YAChBlI,GAAG,CAACwI,QAAQ,CAAC;cACZ9kB,GAAG,EAAE;YACN,CAAC,CAAC;UACH,CAAC,EAAE,IAAI,CAAC;UAER5G,MAAM,CAAC,IAAIrH,KAAK,CAACg8B,UAAQ,CAAC,CAAC;QAC5B,CAAC,MAAM;UACN;UACA,IAAMA,UAAQ,uCAAYr3B,GAAG,CAAC23B,UAAU,MAAG;UAC3C,IAAI91B,OAAO,CAACy1B,SAAS,KAAK,KAAK,EAAE;YAChC1R,GAAG,CAAC+H,SAAS,CAAC;cACb9d,KAAK,EAAEwnB,UAAQ;cACfzJ,IAAI,EAAE,MAAM;cACZC,QAAQ,EAAE;YACX,CAAC,CAAC;UACH;UACAnrB,MAAM,CAAC,IAAIrH,KAAK,CAACg8B,UAAQ,CAAC,CAAC;QAC5B;MACD,CAAC;MACDvzB,IAAI,EAAE,cAACT,GAAG,EAAK;QACd;QACA,IAAIxB,OAAO,CAACq1B,WAAW,KAAK,KAAK,EAAE;UAClCtR,GAAG,CAACwR,WAAW,EAAE;QAClB;;QAEA;QACA,IAAMC,QAAQ,GAAGh0B,GAAG,CAAC2O,MAAM,IAAI,QAAQ;QACvC,IAAInQ,OAAO,CAACy1B,SAAS,KAAK,KAAK,EAAE;UAChC1R,GAAG,CAAC+H,SAAS,CAAC;YACb9d,KAAK,EAAEwnB,QAAQ;YACfzJ,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;UACX,CAAC,CAAC;QACH;QACAnrB,MAAM,CAACW,GAAG,CAAC;MACZ;IAAC,GACA;EACH,CAAC,CAAC;AACH,CAAC;;AAED;AACA,IAAM6vB,IAAI,GAAG;EACZzrB,GAAG,EAAE,aAAC6B,GAAG,EAAgC;IAAA,IAA9BnI,MAAM,uEAAG,CAAC,CAAC;IAAA,IAAEU,OAAO,uEAAG,CAAC,CAAC;IACnC,OAAOo1B,OAAO;MACb3tB,GAAG,EAAHA,GAAG;MACHtI,MAAM,EAAE,KAAK;MACbG,MAAM,EAANA;IAAM,GACHU,OAAO,EACT;EACH,CAAC;EAED4xB,IAAI,EAAE,cAACnqB,GAAG,EAA8B;IAAA,IAA5BlI,IAAI,uEAAG,CAAC,CAAC;IAAA,IAAES,OAAO,uEAAG,CAAC,CAAC;IAClC,OAAOo1B,OAAO;MACb3tB,GAAG,EAAHA,GAAG;MACHtI,MAAM,EAAE,MAAM;MACdI,IAAI,EAAJA;IAAI,GACDS,OAAO,EACT;EACH,CAAC;EAEDoyB,GAAG,EAAE,aAAC3qB,GAAG,EAA8B;IAAA,IAA5BlI,IAAI,uEAAG,CAAC,CAAC;IAAA,IAAES,OAAO,uEAAG,CAAC,CAAC;IACjC,OAAOo1B,OAAO;MACb3tB,GAAG,EAAHA,GAAG;MACHtI,MAAM,EAAE,KAAK;MACbI,IAAI,EAAJA;IAAI,GACDS,OAAO,EACT;EACH,CAAC;EAEDiV,MAAM,EAAE,iBAACxN,GAAG,EAAgC;IAAA,IAA9BnI,MAAM,uEAAG,CAAC,CAAC;IAAA,IAAEU,OAAO,uEAAG,CAAC,CAAC;IACtC,OAAOo1B,OAAO;MACb3tB,GAAG,EAAHA,GAAG;MACHtI,MAAM,EAAE,QAAQ;MAChBG,MAAM,EAANA;IAAM,GACHU,OAAO,EACT;EACH;AACD,CAAC;AAAA,eAEcqxB,IAAI;AAAA,2B;;;;;;;;;;;;;;;;;;AC5NnB;AACA,IAAM6D,MAAM,GAAG;EACd;EACAe,WAAW,EAAE;IACZ;IACAd,OAAO,EAAE,uBAAuB;IAChC;IACAe,KAAK,EAAE,IAAI;IACX;IACAN,OAAO,EAAE;EACV,CAAC;EAED;EACAO,UAAU,EAAE;IACX;IACAhB,OAAO,EAAE,uBAAuB;IAChC;IACAe,KAAK,EAAE,KAAK;IACZ;IACAN,OAAO,EAAE;EACV;AACD,CAAC;;AAED;AACA,SAASQ,SAAS,GAAG;EACpB;EACA,IAAMxoB,GAAG,GAAGtC,aAAoB,IAAI,KAAa;EACjD,OAAO4pB,MAAM,CAACtnB,GAAG,CAAC,IAAIsnB,MAAM,CAACe,WAAW;AACzC;AAAC,eAEcG,SAAS,EAAE;AAAA,2B;;;;;;;;;;;;;;;;;;AC9B1B;AACA,SAASC,iBAAiB,CAACj9B,GAAG,EAAE;EAC/B,IAAMk9B,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIv8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,GAAG,CAACM,MAAM,EAAEK,CAAC,EAAE,EAAE;IACpC,IAAIg8B,IAAI,GAAG38B,GAAG,CAACmB,UAAU,CAACR,CAAC,CAAC;IAC5B,IAAIg8B,IAAI,GAAG,IAAI,EAAE;MAChBO,KAAK,CAAC73B,IAAI,CAACs3B,IAAI,CAAC;IACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,KAAK,EAAE;MACxBO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAIs3B,IAAI,IAAI,CAAE,CAAC;MAC9BO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAIs3B,IAAI,GAAG,IAAK,CAAC;IACjC,CAAC,MAAM,IAAI,CAACA,IAAI,GAAG,MAAM,MAAM,MAAM,IAAIh8B,CAAC,GAAG,CAAC,GAAGX,GAAG,CAACM,MAAM,IAAI,CAACN,GAAG,CAACmB,UAAU,CAACR,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM,EAAE;MAC3G;MACA,IAAMw8B,EAAE,GAAGR,IAAI;MACf,IAAMS,EAAE,GAAGp9B,GAAG,CAACmB,UAAU,CAAC,EAAER,CAAC,CAAC;MAC9Bg8B,IAAI,GAAG,OAAO,IAAK,CAACQ,EAAE,GAAG,KAAK,KAAK,EAAE,GAAKC,EAAE,GAAG,KAAM,CAAC;MACtDF,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAIs3B,IAAI,IAAI,EAAG,CAAC;MAC/BO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAKs3B,IAAI,IAAI,EAAE,GAAI,IAAK,CAAC;MACxCO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAKs3B,IAAI,IAAI,CAAC,GAAI,IAAK,CAAC;MACvCO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAIs3B,IAAI,GAAG,IAAK,CAAC;IACjC,CAAC,MAAM;MACNO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAIs3B,IAAI,IAAI,EAAG,CAAC;MAC/BO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAKs3B,IAAI,IAAI,CAAC,GAAI,IAAK,CAAC;MACvCO,KAAK,CAAC73B,IAAI,CAAC,IAAI,GAAIs3B,IAAI,GAAG,IAAK,CAAC;IACjC;EACD;EACA,OAAOO,KAAK;AACb;;AAEA;AACA,SAASG,iBAAiB,CAACH,KAAK,EAAE;EACjC,IAAI18B,MAAM,GAAG,EAAE;EACf,IAAIG,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGu8B,KAAK,CAAC58B,MAAM,EAAE;IACxB,IAAIg9B,KAAK,GAAGJ,KAAK,CAACv8B,CAAC,EAAE,CAAC;;IAEtB;IACA,IAAI28B,KAAK,KAAKhqB,SAAS,EAAE;IAEzB,IAAIgqB,KAAK,GAAG,IAAI,EAAE;MACjB;MACA98B,MAAM,IAAIP,MAAM,CAACY,YAAY,CAACy8B,KAAK,CAAC;IACrC,CAAC,MAAM,IAAKA,KAAK,IAAI,CAAC,KAAM,IAAI,EAAE;MACjC;MACA,IAAIC,KAAK,GAAGL,KAAK,CAACv8B,CAAC,EAAE,CAAC;MACtB,IAAI48B,KAAK,KAAKjqB,SAAS,EAAE;MACzB9S,MAAM,IAAIP,MAAM,CAACY,YAAY,CAAE,CAACy8B,KAAK,GAAG,IAAI,KAAK,CAAC,GAAKC,KAAK,GAAG,IAAK,CAAC;IACtE,CAAC,MAAM,IAAKD,KAAK,IAAI,CAAC,KAAM,IAAI,EAAE;MACjC;MACA,IAAIC,KAAK,GAAGL,KAAK,CAACv8B,CAAC,EAAE,CAAC;MACtB,IAAI68B,KAAK,GAAGN,KAAK,CAACv8B,CAAC,EAAE,CAAC;MACtB,IAAI48B,KAAK,KAAKjqB,SAAS,IAAIkqB,KAAK,KAAKlqB,SAAS,EAAE;MAChD9S,MAAM,IAAIP,MAAM,CAACY,YAAY,CAAE,CAACy8B,KAAK,GAAG,IAAI,KAAK,EAAE,GAAK,CAACC,KAAK,GAAG,IAAI,KAAK,CAAE,GAAIC,KAAK,GAAG,IAAK,CAAC;IAC/F,CAAC,MAAM,IAAKF,KAAK,IAAI,CAAC,KAAM,IAAI,EAAE;MACjC;MACA,IAAIC,MAAK,GAAGL,KAAK,CAACv8B,CAAC,EAAE,CAAC;MACtB,IAAI68B,MAAK,GAAGN,KAAK,CAACv8B,CAAC,EAAE,CAAC;MACtB,IAAI88B,KAAK,GAAGP,KAAK,CAACv8B,CAAC,EAAE,CAAC;MACtB,IAAI48B,MAAK,KAAKjqB,SAAS,IAAIkqB,MAAK,KAAKlqB,SAAS,IAAImqB,KAAK,KAAKnqB,SAAS,EAAE;MAEvE,IAAIoqB,SAAS,GAAI,CAACJ,KAAK,GAAG,IAAI,KAAK,EAAE,GAAK,CAACC,MAAK,GAAG,IAAI,KAAK,EAAG,GAAI,CAACC,MAAK,GAAG,IAAI,KAAK,CAAE,GAAIC,KAAK,GAAG,IAAK;MACxGC,SAAS,IAAI,OAAO;MACpBl9B,MAAM,IAAIP,MAAM,CAACY,YAAY,CAAC,MAAM,IAAI68B,SAAS,IAAI,EAAE,CAAC,CAAC;MACzDl9B,MAAM,IAAIP,MAAM,CAACY,YAAY,CAAC,MAAM,IAAI68B,SAAS,GAAG,KAAK,CAAC,CAAC;IAC5D,CAAC,MAAM;MACN;MACAvnB,OAAO,CAACC,IAAI,CAAC,aAAa,EAAEknB,KAAK,CAACl8B,QAAQ,CAAC,EAAE,CAAC,CAAC;IAChD;EACD;EACA,OAAOZ,MAAM;AACd;;AAEA;AACA,SAAS24B,YAAY,CAACn5B,GAAG,EAAE;EAC1B,IAAI29B,MAAM,GAAG,IAAIC,MAAM,EAAE;EACzB,OAAOD,MAAM,CAACjW,MAAM,CAAC1nB,GAAG,CAAC;AAC1B;;AAEA;AACA,SAASq5B,YAAY,CAACr5B,GAAG,EAAE;EAC1B,IAAI29B,MAAM,GAAG,IAAIC,MAAM,EAAE;EACzB,OAAOD,MAAM,CAACE,MAAM,CAAC79B,GAAG,CAAC;AAC1B;;AAEA;AACA,SAAS49B,MAAM,GAAG;EAEjB;EACA,IAAME,WAAW,GAAG,mEAAmE;;EAEvF;EACA,IAAI,CAACpW,MAAM,GAAG,UAAU1nB,GAAG,EAAE;IAC5B;IACA,IAAM+9B,SAAS,GAAGd,iBAAiB,CAACj9B,GAAG,CAAC;IAExC,IAAIQ,MAAM,GAAG,EAAE;IACf,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGo9B,SAAS,CAACz9B,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;MAC7C,IAAIqrB,CAAC,GAAG+R,SAAS,CAACp9B,CAAC,CAAC;MACpB,IAAIq9B,CAAC,GAAGr9B,CAAC,GAAG,CAAC,GAAGo9B,SAAS,CAACz9B,MAAM,GAAGy9B,SAAS,CAACp9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACvD,IAAIO,CAAC,GAAGP,CAAC,GAAG,CAAC,GAAGo9B,SAAS,CAACz9B,MAAM,GAAGy9B,SAAS,CAACp9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAEvD,IAAIs9B,EAAE,GAAGjS,CAAC,IAAI,CAAC;QACdkS,EAAE,GAAI,CAAClS,CAAC,GAAG,CAAC,KAAK,CAAC,GAAKgS,CAAC,IAAI,CAAE;QAC9BG,EAAE,GAAI,CAACH,CAAC,GAAG,EAAE,KAAK,CAAC,GAAK98B,CAAC,IAAI,CAAE;QAC/Bk9B,EAAE,GAAGl9B,CAAC,GAAG,EAAE;MAEZV,MAAM,IAAIs9B,WAAW,CAACG,EAAE,CAAC,GAAGH,WAAW,CAACI,EAAE,CAAC,IACzCv9B,CAAC,GAAG,CAAC,GAAGo9B,SAAS,CAACz9B,MAAM,GAAGw9B,WAAW,CAACK,EAAE,CAAC,GAAG,GAAG,CAAC,IACjDx9B,CAAC,GAAG,CAAC,GAAGo9B,SAAS,CAACz9B,MAAM,GAAGw9B,WAAW,CAACM,EAAE,CAAC,GAAG,GAAG,CAAC;IACpD;IACA,OAAO59B,MAAM;EACd,CAAC;;EAED;EACA,IAAI,CAACq9B,MAAM,GAAG,UAAU79B,GAAG,EAAE;IAC5B;IACA,IAAMq+B,WAAW,GAAGr+B,GAAG;;IAEvB;IACAA,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;IAEzC,IAAMg9B,KAAK,GAAG,EAAE;IAChB,IAAIv8B,CAAC,GAAG,CAAC;IAET,OAAOA,CAAC,GAAGX,GAAG,CAACM,MAAM,EAAE;MACtB,IAAI0rB,CAAC,GAAG8R,WAAW,CAACr+B,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;MAC5C,IAAIq9B,CAAC,GAAGF,WAAW,CAACr+B,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;MAC5C,IAAIO,CAAC,GAAG48B,WAAW,CAACr+B,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;MAC5C,IAAIu0B,CAAC,GAAG4I,WAAW,CAACr+B,OAAO,CAACO,GAAG,CAACY,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;;MAE5C;MACA,IAAIqrB,CAAC,KAAK,CAAC,CAAC,IAAIgS,CAAC,KAAK,CAAC,CAAC,EAAE;;MAE1B;MACA,IAAIM,WAAW,GAAGt+B,GAAG,CAACY,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG;MAC3C,IAAI49B,WAAW,GAAGv+B,GAAG,CAACY,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG;MAE3C,IAAIO,CAAC,KAAK,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC;MACnB,IAAIg0B,CAAC,KAAK,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC;MAEnB,IAAI+I,EAAE,GAAIjS,CAAC,IAAI,CAAC,GAAKgS,CAAC,IAAI,CAAE;MAC5B,IAAIE,EAAE,GAAI,CAACF,CAAC,GAAG,EAAE,KAAK,CAAC,GAAK98B,CAAC,IAAI,CAAE;MACnC,IAAIi9B,EAAE,GAAI,CAACj9B,CAAC,GAAG,CAAC,KAAK,CAAC,GAAIg0B,CAAC;MAE3BgI,KAAK,CAAC73B,IAAI,CAAC44B,EAAE,CAAC;;MAEd;MACA,IAAI,CAACK,WAAW,EAAE;QACjBpB,KAAK,CAAC73B,IAAI,CAAC64B,EAAE,CAAC;MACf;MACA,IAAI,CAACK,WAAW,IAAI,CAACD,WAAW,EAAE;QACjCpB,KAAK,CAAC73B,IAAI,CAAC84B,EAAE,CAAC;MACf;IACD;;IAEA;IACA,OAAOd,iBAAiB,CAACH,KAAK,CAAC;EAChC,CAAC;AACF;;AAEA,oB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/JA;AAEA;AACO,SAASsB,QAAQ,CAACC,QAAQ,EAAEC,QAAQ,EAAE/B,IAAI,EAAEgC,IAAI,EAAE;EACxD,IAAMx4B,IAAI,GAAG;IACZs4B,QAAQ,EAARA,QAAQ;IACRC,QAAQ,EAARA,QAAQ;IACR/B,IAAI,EAAJA,IAAI;IACJgC,IAAI,EAAJA;EACD,CAAC;EAED,OAAO1G,gBAAI,CAACO,IAAI,CAAC,QAAQ,EAAEryB,IAAI,EAAE;IAChCsyB,MAAM,EAAE;MACPmG,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE;IACf;EACD,CAAC,CAAC;AACH;;AAEA;AACO,SAASC,QAAQ,CAAC34B,IAAI,EAAE;EAC9B,OAAO8xB,gBAAI,CAACO,IAAI,CAAC,WAAW,EAAEryB,IAAI,EAAE;IACnCsyB,MAAM,EAAE;MACPmG,OAAO,EAAE;IACV,CAAC;IACDvC,SAAS,EAAE,KAAK,CAAC;EAClB,CAAC,CAAC;AACH;;AAEA;AACO,SAAS/J,WAAW,GAAG;EAC7B,OAAO2F,gBAAI,CAACzrB,GAAG,CAAC,UAAU,CAAC;AAC5B;;AAEA;AACO,SAASumB,MAAM,GAAG;EACxB,OAAOkF,gBAAI,CAACO,IAAI,CAAC,SAAS,CAAC;AAC5B;;AAEA;AACO,SAASuG,UAAU,GAAG;EAC5B,OAAO9G,gBAAI,CAACzrB,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE;IACpCisB,MAAM,EAAE;MACPmG,OAAO,EAAE;IACV,CAAC;IACDpC,OAAO,EAAE;EACV,CAAC,CAAC;AACH;;AAEA;AACO,SAASwC,SAAS,GAAG;EAC3B,OAAO/G,gBAAI,CAACzrB,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;IAClCisB,MAAM,EAAE;MACPmG,OAAO,EAAE;IACV,CAAC;IACDpC,OAAO,EAAE;EACV,CAAC,CAAC;AACH;;AAEA;AACO,SAASyC,WAAW,CAACC,KAAK,EAAE;EAClC,OAAOjH,gBAAI,CAACzrB,GAAG,CAAC,gBAAgB,EAAE;IACjC0yB,KAAK,EAALA;EACD,CAAC,EAAE;IACFzG,MAAM,EAAE;MACPmG,OAAO,EAAE;IACV,CAAC;IACDpC,OAAO,EAAE;EACV,CAAC,CAAC;AACH;;AAEA;AACO,SAAS2C,aAAa,CAACD,KAAK,EAAE;EACpC,OAAOjH,gBAAI,CAACzrB,GAAG,CAAC,qBAAqB,EAAE;IACtC0yB,KAAK,EAALA;EACD,CAAC,EAAE;IACFzG,MAAM,EAAE;MACPmG,OAAO,EAAE;IACV,CAAC;IACDpC,OAAO,EAAE;EACV,CAAC,CAAC;AACH;;AAEA;AACO,SAAS4C,UAAU,CAACF,KAAK,EAAEvC,IAAI,EAAE;EACvC,OAAO1E,gBAAI,CAACO,IAAI,CAAC,iBAAiB,EAAE;IACnC6G,WAAW,EAAEH,KAAK;IAClBI,OAAO,EAAE3C;EACV,CAAC,EAAE;IACFlE,MAAM,EAAE;MACPmG,OAAO,EAAE;IACV,CAAC;IACDpC,OAAO,EAAE;EACV,CAAC,CAAC;AACH,C", "file": "common/vendor.js", "sourcesContent": ["const objectKeys = [\r\n  'qy',\r\n  'env',\r\n  'error',\r\n  'version',\r\n  'lanDebug',\r\n  'cloud',\r\n  'serviceMarket',\r\n  'router',\r\n  'worklet',\r\n  '__webpack_require_UNI_MP_PLUGIN__'\r\n]\r\nconst singlePageDisableKey = [\r\n  'lanDebug',\r\n  'router',\r\n  'worklet'\r\n]\r\nconst target = typeof globalThis !== 'undefined' ? globalThis : (function () {\r\n  return this\r\n})()\r\n\r\nconst key = ['w', 'x'].join('')\r\nconst oldWx = target[key]\r\nconst launchOption = oldWx.getLaunchOptionsSync ? oldWx.getLaunchOptionsSync() : null\r\n\r\nfunction isWxKey (key) {\r\n  if (launchOption && launchOption.scene === 1154 && singlePageDisableKey.includes(key)) {\r\n    return false\r\n  }\r\n  return objectKeys.indexOf(key) > -1 || typeof oldWx[key] === 'function'\r\n}\r\n\r\nfunction initWx () {\r\n  const newWx = {}\r\n  for (const key in oldWx) {\r\n    if (isWx<PERSON>ey(key)) {\r\n      // TODO wrapper function\r\n      newWx[key] = oldWx[key]\r\n    }\r\n  }\r\n  return newWx\r\n}\r\ntarget[key] = initWx()\r\nexport default target[key]\r\n", "import { initVueI18n } from '@dcloudio/uni-i18n';\r\nimport Vue from 'vue';\r\n\r\nlet realAtob;\r\n\r\nconst b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\nconst b64re = /^(?:[A-Za-z\\d+/]{4})*?(?:[A-Za-z\\d+/]{2}(?:==)?|[A-Za-z\\d+/]{3}=?)?$/;\r\n\r\nif (typeof atob !== 'function') {\r\n  realAtob = function (str) {\r\n    str = String(str).replace(/[\\t\\n\\f\\r ]+/g, '');\r\n    if (!b64re.test(str)) { throw new Error(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\") }\r\n\r\n    // Adding the padding if missing, for semplicity\r\n    str += '=='.slice(2 - (str.length & 3));\r\n    var bitmap; var result = ''; var r1; var r2; var i = 0;\r\n    for (; i < str.length;) {\r\n      bitmap = b64.indexOf(str.charAt(i++)) << 18 | b64.indexOf(str.charAt(i++)) << 12 |\r\n                    (r1 = b64.indexOf(str.charAt(i++))) << 6 | (r2 = b64.indexOf(str.charAt(i++)));\r\n\r\n      result += r1 === 64 ? String.fromCharCode(bitmap >> 16 & 255)\r\n        : r2 === 64 ? String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255)\r\n          : String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255, bitmap & 255);\r\n    }\r\n    return result\r\n  };\r\n} else {\r\n  // 注意atob只能在全局对象上调用，例如：`const Base64 = {atob};Base64.atob('xxxx')`是错误的用法\r\n  realAtob = atob;\r\n}\r\n\r\nfunction b64DecodeUnicode (str) {\r\n  return decodeURIComponent(realAtob(str).split('').map(function (c) {\r\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)\r\n  }).join(''))\r\n}\r\n\r\nfunction getCurrentUserInfo () {\r\n  const token = ( wx).getStorageSync('uni_id_token') || '';\r\n  const tokenArr = token.split('.');\r\n  if (!token || tokenArr.length !== 3) {\r\n    return {\r\n      uid: null,\r\n      role: [],\r\n      permission: [],\r\n      tokenExpired: 0\r\n    }\r\n  }\r\n  let userInfo;\r\n  try {\r\n    userInfo = JSON.parse(b64DecodeUnicode(tokenArr[1]));\r\n  } catch (error) {\r\n    throw new Error('获取当前用户信息出错，详细错误信息为：' + error.message)\r\n  }\r\n  userInfo.tokenExpired = userInfo.exp * 1000;\r\n  delete userInfo.exp;\r\n  delete userInfo.iat;\r\n  return userInfo\r\n}\r\n\r\nfunction uniIdMixin (Vue) {\r\n  Vue.prototype.uniIDHasRole = function (roleId) {\r\n    const {\r\n      role\r\n    } = getCurrentUserInfo();\r\n    return role.indexOf(roleId) > -1\r\n  };\r\n  Vue.prototype.uniIDHasPermission = function (permissionId) {\r\n    const {\r\n      permission\r\n    } = getCurrentUserInfo();\r\n    return this.uniIDHasRole('admin') || permission.indexOf(permissionId) > -1\r\n  };\r\n  Vue.prototype.uniIDTokenValid = function () {\r\n    const {\r\n      tokenExpired\r\n    } = getCurrentUserInfo();\r\n    return tokenExpired > Date.now()\r\n  };\r\n}\r\n\r\nconst _toString = Object.prototype.toString;\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\r\nfunction isFn (fn) {\r\n  return typeof fn === 'function'\r\n}\r\n\r\nfunction isStr (str) {\r\n  return typeof str === 'string'\r\n}\r\n\r\nfunction isObject (obj) {\r\n  return obj !== null && typeof obj === 'object'\r\n}\r\n\r\nfunction isPlainObject (obj) {\r\n  return _toString.call(obj) === '[object Object]'\r\n}\r\n\r\nfunction hasOwn (obj, key) {\r\n  return hasOwnProperty.call(obj, key)\r\n}\r\n\r\nfunction noop () {}\r\n\r\n/**\r\n * Create a cached version of a pure function.\r\n */\r\nfunction cached (fn) {\r\n  const cache = Object.create(null);\r\n  return function cachedFn (str) {\r\n    const hit = cache[str];\r\n    return hit || (cache[str] = fn(str))\r\n  }\r\n}\r\n\r\n/**\r\n * Camelize a hyphen-delimited string.\r\n */\r\nconst camelizeRE = /-(\\w)/g;\r\nconst camelize = cached((str) => {\r\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '')\r\n});\r\n\r\nfunction sortObject (obj) {\r\n  const sortObj = {};\r\n  if (isPlainObject(obj)) {\r\n    Object.keys(obj).sort().forEach(key => {\r\n      sortObj[key] = obj[key];\r\n    });\r\n  }\r\n  return !Object.keys(sortObj) ? obj : sortObj\r\n}\r\n\r\nconst HOOKS = [\r\n  'invoke',\r\n  'success',\r\n  'fail',\r\n  'complete',\r\n  'returnValue'\r\n];\r\n\r\nconst globalInterceptors = {};\r\nconst scopedInterceptors = {};\r\n\r\nfunction mergeHook (parentVal, childVal) {\r\n  const res = childVal\r\n    ? parentVal\r\n      ? parentVal.concat(childVal)\r\n      : Array.isArray(childVal)\r\n        ? childVal : [childVal]\r\n    : parentVal;\r\n  return res\r\n    ? dedupeHooks(res)\r\n    : res\r\n}\r\n\r\nfunction dedupeHooks (hooks) {\r\n  const res = [];\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    if (res.indexOf(hooks[i]) === -1) {\r\n      res.push(hooks[i]);\r\n    }\r\n  }\r\n  return res\r\n}\r\n\r\nfunction removeHook (hooks, hook) {\r\n  const index = hooks.indexOf(hook);\r\n  if (index !== -1) {\r\n    hooks.splice(index, 1);\r\n  }\r\n}\r\n\r\nfunction mergeInterceptorHook (interceptor, option) {\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      interceptor[hook] = mergeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction removeInterceptorHook (interceptor, option) {\r\n  if (!interceptor || !option) {\r\n    return\r\n  }\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      removeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction addInterceptor (method, option) {\r\n  if (typeof method === 'string' && isPlainObject(option)) {\r\n    mergeInterceptorHook(scopedInterceptors[method] || (scopedInterceptors[method] = {}), option);\r\n  } else if (isPlainObject(method)) {\r\n    mergeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction removeInterceptor (method, option) {\r\n  if (typeof method === 'string') {\r\n    if (isPlainObject(option)) {\r\n      removeInterceptorHook(scopedInterceptors[method], option);\r\n    } else {\r\n      delete scopedInterceptors[method];\r\n    }\r\n  } else if (isPlainObject(method)) {\r\n    removeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction wrapperHook (hook, params) {\r\n  return function (data) {\r\n    return hook(data, params) || data\r\n  }\r\n}\r\n\r\nfunction isPromise (obj) {\r\n  return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function'\r\n}\r\n\r\nfunction queue (hooks, data, params) {\r\n  let promise = false;\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    const hook = hooks[i];\r\n    if (promise) {\r\n      promise = Promise.resolve(wrapperHook(hook, params));\r\n    } else {\r\n      const res = hook(data, params);\r\n      if (isPromise(res)) {\r\n        promise = Promise.resolve(res);\r\n      }\r\n      if (res === false) {\r\n        return {\r\n          then () { }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return promise || {\r\n    then (callback) {\r\n      return callback(data)\r\n    }\r\n  }\r\n}\r\n\r\nfunction wrapperOptions (interceptor, options = {}) {\r\n  ['success', 'fail', 'complete'].forEach(name => {\r\n    if (Array.isArray(interceptor[name])) {\r\n      const oldCallback = options[name];\r\n      options[name] = function callbackInterceptor (res) {\r\n        queue(interceptor[name], res, options).then((res) => {\r\n          /* eslint-disable no-mixed-operators */\r\n          return isFn(oldCallback) && oldCallback(res) || res\r\n        });\r\n      };\r\n    }\r\n  });\r\n  return options\r\n}\r\n\r\nfunction wrapperReturnValue (method, returnValue) {\r\n  const returnValueHooks = [];\r\n  if (Array.isArray(globalInterceptors.returnValue)) {\r\n    returnValueHooks.push(...globalInterceptors.returnValue);\r\n  }\r\n  const interceptor = scopedInterceptors[method];\r\n  if (interceptor && Array.isArray(interceptor.returnValue)) {\r\n    returnValueHooks.push(...interceptor.returnValue);\r\n  }\r\n  returnValueHooks.forEach(hook => {\r\n    returnValue = hook(returnValue) || returnValue;\r\n  });\r\n  return returnValue\r\n}\r\n\r\nfunction getApiInterceptorHooks (method) {\r\n  const interceptor = Object.create(null);\r\n  Object.keys(globalInterceptors).forEach(hook => {\r\n    if (hook !== 'returnValue') {\r\n      interceptor[hook] = globalInterceptors[hook].slice();\r\n    }\r\n  });\r\n  const scopedInterceptor = scopedInterceptors[method];\r\n  if (scopedInterceptor) {\r\n    Object.keys(scopedInterceptor).forEach(hook => {\r\n      if (hook !== 'returnValue') {\r\n        interceptor[hook] = (interceptor[hook] || []).concat(scopedInterceptor[hook]);\r\n      }\r\n    });\r\n  }\r\n  return interceptor\r\n}\r\n\r\nfunction invokeApi (method, api, options, ...params) {\r\n  const interceptor = getApiInterceptorHooks(method);\r\n  if (interceptor && Object.keys(interceptor).length) {\r\n    if (Array.isArray(interceptor.invoke)) {\r\n      const res = queue(interceptor.invoke, options);\r\n      return res.then((options) => {\r\n        // 重新访问 getApiInterceptorHooks, 允许 invoke 中再次调用 addInterceptor,removeInterceptor\r\n        return api(\r\n          wrapperOptions(getApiInterceptorHooks(method), options),\r\n          ...params\r\n        )\r\n      })\r\n    } else {\r\n      return api(wrapperOptions(interceptor, options), ...params)\r\n    }\r\n  }\r\n  return api(options, ...params)\r\n}\r\n\r\nconst promiseInterceptor = {\r\n  returnValue (res) {\r\n    if (!isPromise(res)) {\r\n      return res\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      res.then(res => {\r\n        if (!res) {\r\n          resolve(res);\r\n          return\r\n        }\r\n        if (res[0]) {\r\n          reject(res[0]);\r\n        } else {\r\n          resolve(res[1]);\r\n        }\r\n      });\r\n    })\r\n  }\r\n};\r\n\r\nconst SYNC_API_RE =\r\n  /^\\$|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/;\r\n\r\nconst CONTEXT_API_RE = /^create|Manager$/;\r\n\r\n// Context例外情况\r\nconst CONTEXT_API_RE_EXC = ['createBLEConnection'];\r\n\r\n// 同步例外情况\r\nconst ASYNC_API = ['createBLEConnection', 'createPushMessage'];\r\n\r\nconst CALLBACK_API_RE = /^on|^off/;\r\n\r\nfunction isContextApi (name) {\r\n  return CONTEXT_API_RE.test(name) && CONTEXT_API_RE_EXC.indexOf(name) === -1\r\n}\r\nfunction isSyncApi (name) {\r\n  return SYNC_API_RE.test(name) && ASYNC_API.indexOf(name) === -1\r\n}\r\n\r\nfunction isCallbackApi (name) {\r\n  return CALLBACK_API_RE.test(name) && name !== 'onPush'\r\n}\r\n\r\nfunction handlePromise (promise) {\r\n  return promise.then(data => {\r\n    return [null, data]\r\n  })\r\n    .catch(err => [err])\r\n}\r\n\r\nfunction shouldPromise (name) {\r\n  if (\r\n    isContextApi(name) ||\r\n    isSyncApi(name) ||\r\n    isCallbackApi(name)\r\n  ) {\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/* eslint-disable no-extend-native */\r\nif (!Promise.prototype.finally) {\r\n  Promise.prototype.finally = function (callback) {\r\n    const promise = this.constructor;\r\n    return this.then(\r\n      value => promise.resolve(callback()).then(() => value),\r\n      reason => promise.resolve(callback()).then(() => {\r\n        throw reason\r\n      })\r\n    )\r\n  };\r\n}\r\n\r\nfunction promisify (name, api) {\r\n  if (!shouldPromise(name) || !isFn(api)) {\r\n    return api\r\n  }\r\n  return function promiseApi (options = {}, ...params) {\r\n    if (isFn(options.success) || isFn(options.fail) || isFn(options.complete)) {\r\n      return wrapperReturnValue(name, invokeApi(name, api, options, ...params))\r\n    }\r\n    return wrapperReturnValue(name, handlePromise(new Promise((resolve, reject) => {\r\n      invokeApi(name, api, Object.assign({}, options, {\r\n        success: resolve,\r\n        fail: reject\r\n      }), ...params);\r\n    })))\r\n  }\r\n}\r\n\r\nconst EPS = 1e-4;\r\nconst BASE_DEVICE_WIDTH = 750;\r\nlet isIOS = false;\r\nlet deviceWidth = 0;\r\nlet deviceDPR = 0;\r\n\r\nfunction checkDeviceWidth () {\r\n  const { windowWidth, pixelRatio, platform } =  Object.assign({}, wx.getWindowInfo(), {\r\n      platform: wx.getDeviceInfo().platform\r\n    })\r\n    ; // uni=>wx runtime 编译目标是 uni 对象，内部不允许直接使用 uni\r\n\r\n  deviceWidth = windowWidth;\r\n  deviceDPR = pixelRatio;\r\n  isIOS = platform === 'ios';\r\n}\r\n\r\nfunction upx2px (number, newDeviceWidth) {\r\n  if (deviceWidth === 0) {\r\n    checkDeviceWidth();\r\n  }\r\n\r\n  number = Number(number);\r\n  if (number === 0) {\r\n    return 0\r\n  }\r\n  let result = (number / BASE_DEVICE_WIDTH) * (newDeviceWidth || deviceWidth);\r\n  if (result < 0) {\r\n    result = -result;\r\n  }\r\n  result = Math.floor(result + EPS);\r\n  if (result === 0) {\r\n    if (deviceDPR === 1 || !isIOS) {\r\n      result = 1;\r\n    } else {\r\n      result = 0.5;\r\n    }\r\n  }\r\n  return number < 0 ? -result : result\r\n}\r\n\r\nconst LOCALE_ZH_HANS = 'zh-Hans';\r\nconst LOCALE_ZH_HANT = 'zh-Hant';\r\nconst LOCALE_EN = 'en';\r\nconst LOCALE_FR = 'fr';\r\nconst LOCALE_ES = 'es';\r\n\r\nconst messages = {};\r\n\r\nlet locale;\r\n\r\n{\r\n  locale = normalizeLocale( wx.getAppBaseInfo().language ) || LOCALE_EN;\r\n}\r\n\r\nfunction initI18nMessages () {\r\n  if (!isEnableLocale()) {\r\n    return\r\n  }\r\n  const localeKeys = Object.keys(__uniConfig.locales);\r\n  if (localeKeys.length) {\r\n    localeKeys.forEach((locale) => {\r\n      const curMessages = messages[locale];\r\n      const userMessages = __uniConfig.locales[locale];\r\n      if (curMessages) {\r\n        Object.assign(curMessages, userMessages);\r\n      } else {\r\n        messages[locale] = userMessages;\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\ninitI18nMessages();\r\n\r\nconst i18n = initVueI18n(\r\n  locale,\r\n   {}\r\n);\r\nconst t = i18n.t;\r\nconst i18nMixin = (i18n.mixin = {\r\n  beforeCreate () {\r\n    const unwatch = i18n.i18n.watchLocale(() => {\r\n      this.$forceUpdate();\r\n    });\r\n    this.$once('hook:beforeDestroy', function () {\r\n      unwatch();\r\n    });\r\n  },\r\n  methods: {\r\n    $$t (key, values) {\r\n      return t(key, values)\r\n    }\r\n  }\r\n});\r\nconst setLocale = i18n.setLocale;\r\nconst getLocale = i18n.getLocale;\r\n\r\nfunction initAppLocale (Vue, appVm, locale) {\r\n  const state = Vue.observable({\r\n    locale: locale || i18n.getLocale()\r\n  });\r\n  const localeWatchers = [];\r\n  appVm.$watchLocale = fn => {\r\n    localeWatchers.push(fn);\r\n  };\r\n  Object.defineProperty(appVm, '$locale', {\r\n    get () {\r\n      return state.locale\r\n    },\r\n    set (v) {\r\n      state.locale = v;\r\n      localeWatchers.forEach(watch => watch(v));\r\n    }\r\n  });\r\n}\r\n\r\nfunction isEnableLocale () {\r\n  return typeof __uniConfig !== 'undefined' && __uniConfig.locales && !!Object.keys(__uniConfig.locales).length\r\n}\r\n\r\nfunction include (str, parts) {\r\n  return !!parts.find((part) => str.indexOf(part) !== -1)\r\n}\r\n\r\nfunction startsWith (str, parts) {\r\n  return parts.find((part) => str.indexOf(part) === 0)\r\n}\r\n\r\nfunction normalizeLocale (locale, messages) {\r\n  if (!locale) {\r\n    return\r\n  }\r\n  locale = locale.trim().replace(/_/g, '-');\r\n  if (messages && messages[locale]) {\r\n    return locale\r\n  }\r\n  locale = locale.toLowerCase();\r\n  if (locale === 'chinese') {\r\n    // 支付宝\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  if (locale.indexOf('zh') === 0) {\r\n    if (locale.indexOf('-hans') > -1) {\r\n      return LOCALE_ZH_HANS\r\n    }\r\n    if (locale.indexOf('-hant') > -1) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  const lang = startsWith(locale, [LOCALE_EN, LOCALE_FR, LOCALE_ES]);\r\n  if (lang) {\r\n    return lang\r\n  }\r\n}\r\n// export function initI18n() {\r\n//   const localeKeys = Object.keys(__uniConfig.locales || {})\r\n//   if (localeKeys.length) {\r\n//     localeKeys.forEach((locale) =>\r\n//       i18n.add(locale, __uniConfig.locales[locale])\r\n//     )\r\n//   }\r\n// }\r\n\r\nfunction getLocale$1 () {\r\n  // 优先使用 $locale\r\n  if (isFn(getApp)) {\r\n    const app = getApp({\r\n      allowDefault: true\r\n    });\r\n    if (app && app.$vm) {\r\n      return app.$vm.$locale\r\n    }\r\n  }\r\n  return normalizeLocale( wx.getAppBaseInfo().language ) || LOCALE_EN\r\n}\r\n\r\nfunction setLocale$1 (locale) {\r\n  const app = isFn(getApp) ? getApp() : false;\r\n  if (!app) {\r\n    return false\r\n  }\r\n  const oldLocale = app.$vm.$locale;\r\n  if (oldLocale !== locale) {\r\n    app.$vm.$locale = locale;\r\n    onLocaleChangeCallbacks.forEach((fn) => fn({\r\n      locale\r\n    }));\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\nconst onLocaleChangeCallbacks = [];\r\nfunction onLocaleChange (fn) {\r\n  if (onLocaleChangeCallbacks.indexOf(fn) === -1) {\r\n    onLocaleChangeCallbacks.push(fn);\r\n  }\r\n}\r\n\r\nif (typeof global !== 'undefined') {\r\n  global.getLocale = getLocale$1;\r\n}\r\n\r\nconst interceptors = {\r\n  promiseInterceptor\r\n};\r\n\r\nvar baseApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  upx2px: upx2px,\r\n  rpx2px: upx2px,\r\n  getLocale: getLocale$1,\r\n  setLocale: setLocale$1,\r\n  onLocaleChange: onLocaleChange,\r\n  addInterceptor: addInterceptor,\r\n  removeInterceptor: removeInterceptor,\r\n  interceptors: interceptors\r\n});\r\n\r\nfunction findExistsPageIndex (url) {\r\n  const pages = getCurrentPages();\r\n  let len = pages.length;\r\n  while (len--) {\r\n    const page = pages[len];\r\n    if (page.$page && page.$page.fullPath === url) {\r\n      return len\r\n    }\r\n  }\r\n  return -1\r\n}\r\n\r\nvar redirectTo = {\r\n  name (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.delta) {\r\n      return 'navigateBack'\r\n    }\r\n    return 'redirectTo'\r\n  },\r\n  args (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.url) {\r\n      const existsPageIndex = findExistsPageIndex(fromArgs.url);\r\n      if (existsPageIndex !== -1) {\r\n        const delta = getCurrentPages().length - 1 - existsPageIndex;\r\n        if (delta > 0) {\r\n          fromArgs.delta = delta;\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nvar previewImage = {\r\n  args (fromArgs) {\r\n    let currentIndex = parseInt(fromArgs.current);\r\n    if (isNaN(currentIndex)) {\r\n      return\r\n    }\r\n    const urls = fromArgs.urls;\r\n    if (!Array.isArray(urls)) {\r\n      return\r\n    }\r\n    const len = urls.length;\r\n    if (!len) {\r\n      return\r\n    }\r\n    if (currentIndex < 0) {\r\n      currentIndex = 0;\r\n    } else if (currentIndex >= len) {\r\n      currentIndex = len - 1;\r\n    }\r\n    if (currentIndex > 0) {\r\n      fromArgs.current = urls[currentIndex];\r\n      fromArgs.urls = urls.filter(\r\n        (item, index) => index < currentIndex ? item !== urls[currentIndex] : true\r\n      );\r\n    } else {\r\n      fromArgs.current = urls[0];\r\n    }\r\n    return {\r\n      indicator: false,\r\n      loop: false\r\n    }\r\n  }\r\n};\r\n\r\nconst UUID_KEY = '__DC_STAT_UUID';\r\nlet deviceId;\r\nfunction useDeviceId (result) {\r\n  deviceId = deviceId || wx.getStorageSync(UUID_KEY);\r\n  if (!deviceId) {\r\n    deviceId = Date.now() + '' + Math.floor(Math.random() * 1e7);\r\n    wx.setStorage({\r\n      key: UUID_KEY,\r\n      data: deviceId\r\n    });\r\n  }\r\n  result.deviceId = deviceId;\r\n}\r\n\r\nfunction addSafeAreaInsets (result) {\r\n  if (result.safeArea) {\r\n    const safeArea = result.safeArea;\r\n    result.safeAreaInsets = {\r\n      top: safeArea.top,\r\n      left: safeArea.left,\r\n      right: result.windowWidth - safeArea.right,\r\n      bottom: result.screenHeight - safeArea.bottom\r\n    };\r\n  }\r\n}\r\n\r\nfunction populateParameters (result) {\r\n  const {\r\n    brand = '', model = '', system = '',\r\n    language = '', theme, version,\r\n    platform, fontSizeSetting,\r\n    SDKVersion, pixelRatio, deviceOrientation\r\n  } = result;\r\n  // const isQuickApp = \"mp-weixin\".indexOf('quickapp-webview') !== -1\r\n\r\n  const extraParam = {};\r\n\r\n  // osName osVersion\r\n  let osName = '';\r\n  let osVersion = '';\r\n  {\r\n    osName = system.split(' ')[0] || '';\r\n    osVersion = system.split(' ')[1] || '';\r\n  }\r\n  let hostVersion = version;\r\n\r\n  // deviceType\r\n  const deviceType = getGetDeviceType(result, model);\r\n\r\n  // deviceModel\r\n  const deviceBrand = getDeviceBrand(brand);\r\n\r\n  // hostName\r\n  const _hostName = getHostName(result);\r\n\r\n  // deviceOrientation\r\n  let _deviceOrientation = deviceOrientation; // 仅 微信 百度 支持\r\n\r\n  // devicePixelRatio\r\n  let _devicePixelRatio = pixelRatio;\r\n\r\n  // SDKVersion\r\n  let _SDKVersion = SDKVersion;\r\n\r\n  // hostLanguage\r\n  const hostLanguage = (language || '').replace(/_/g, '-');\r\n\r\n  // wx.getAccountInfoSync\r\n\r\n  const parameters = {\r\n    appId: process.env.UNI_APP_ID,\r\n    appName: process.env.UNI_APP_NAME,\r\n    appVersion: process.env.UNI_APP_VERSION_NAME,\r\n    appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n    appLanguage: getAppLanguage(hostLanguage),\r\n    uniCompileVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniRuntimeVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\r\n    deviceBrand,\r\n    deviceModel: model,\r\n    deviceType,\r\n    devicePixelRatio: _devicePixelRatio,\r\n    deviceOrientation: _deviceOrientation,\r\n    osName: osName.toLocaleLowerCase(),\r\n    osVersion,\r\n    hostTheme: theme,\r\n    hostVersion,\r\n    hostLanguage,\r\n    hostName: _hostName,\r\n    hostSDKVersion: _SDKVersion,\r\n    hostFontSizeSetting: fontSizeSetting,\r\n    windowTop: 0,\r\n    windowBottom: 0,\r\n    // TODO\r\n    osLanguage: undefined,\r\n    osTheme: undefined,\r\n    ua: undefined,\r\n    hostPackageName: undefined,\r\n    browserName: undefined,\r\n    browserVersion: undefined,\r\n    isUniAppX: false\r\n  };\r\n\r\n  Object.assign(result, parameters, extraParam);\r\n}\r\n\r\nfunction getGetDeviceType (result, model) {\r\n  let deviceType = result.deviceType || 'phone';\r\n  {\r\n    const deviceTypeMaps = {\r\n      ipad: 'pad',\r\n      windows: 'pc',\r\n      mac: 'pc'\r\n    };\r\n    const deviceTypeMapsKeys = Object.keys(deviceTypeMaps);\r\n    const _model = model.toLocaleLowerCase();\r\n    for (let index = 0; index < deviceTypeMapsKeys.length; index++) {\r\n      const _m = deviceTypeMapsKeys[index];\r\n      if (_model.indexOf(_m) !== -1) {\r\n        deviceType = deviceTypeMaps[_m];\r\n        break\r\n      }\r\n    }\r\n  }\r\n  return deviceType\r\n}\r\n\r\nfunction getDeviceBrand (brand) {\r\n  let deviceBrand = brand;\r\n  if (deviceBrand) {\r\n    deviceBrand = brand.toLocaleLowerCase();\r\n  }\r\n  return deviceBrand\r\n}\r\n\r\nfunction getAppLanguage (defaultLanguage) {\r\n  return getLocale$1\r\n    ? getLocale$1()\r\n    : defaultLanguage\r\n}\r\n\r\nfunction getHostName (result) {\r\n  const _platform =  'WeChat' ;\r\n  let _hostName = result.hostName || _platform; // mp-jd\r\n  {\r\n    if (result.environment) {\r\n      _hostName = result.environment;\r\n    } else if (result.host && result.host.env) {\r\n      _hostName = result.host.env;\r\n    }\r\n  }\r\n\r\n  return _hostName\r\n}\r\n\r\nvar getSystemInfo = {\r\n  returnValue: function (result) {\r\n    useDeviceId(result);\r\n    addSafeAreaInsets(result);\r\n    populateParameters(result);\r\n  }\r\n};\r\n\r\nvar showActionSheet = {\r\n  args (fromArgs) {\r\n    if (typeof fromArgs === 'object') {\r\n      fromArgs.alertText = fromArgs.title;\r\n    }\r\n  }\r\n};\r\n\r\nvar getAppBaseInfo = {\r\n  returnValue: function (result) {\r\n    const { version, language, SDKVersion, theme } = result;\r\n\r\n    const _hostName = getHostName(result);\r\n\r\n    const hostLanguage = (language || '').replace('_', '-');\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      appId: process.env.UNI_APP_ID,\r\n      appName: process.env.UNI_APP_NAME,\r\n      appVersion: process.env.UNI_APP_VERSION_NAME,\r\n      appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n      appLanguage: getAppLanguage(hostLanguage),\r\n      hostVersion: version,\r\n      hostLanguage,\r\n      hostName: _hostName,\r\n      hostSDKVersion: SDKVersion,\r\n      hostTheme: theme,\r\n      isUniAppX: false,\r\n      uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\r\n      uniCompileVersion: process.env.UNI_COMPILER_VERSION,\r\n      uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\r\n      uniRuntimeVersion: process.env.UNI_COMPILER_VERSION\r\n    }));\r\n  }\r\n};\r\n\r\nvar getDeviceInfo = {\r\n  returnValue: function (result) {\r\n    const { brand, model } = result;\r\n    const deviceType = getGetDeviceType(result, model);\r\n    const deviceBrand = getDeviceBrand(brand);\r\n    useDeviceId(result);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      deviceType,\r\n      deviceBrand,\r\n      deviceModel: model\r\n    }));\r\n  }\r\n};\r\n\r\nvar getWindowInfo = {\r\n  returnValue: function (result) {\r\n    addSafeAreaInsets(result);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      windowTop: 0,\r\n      windowBottom: 0\r\n    }));\r\n  }\r\n};\r\n\r\nvar getAppAuthorizeSetting = {\r\n  returnValue: function (result) {\r\n    const { locationReducedAccuracy } = result;\r\n\r\n    result.locationAccuracy = 'unsupported';\r\n    if (locationReducedAccuracy === true) {\r\n      result.locationAccuracy = 'reduced';\r\n    } else if (locationReducedAccuracy === false) {\r\n      result.locationAccuracy = 'full';\r\n    }\r\n  }\r\n};\r\n\r\n// import navigateTo from 'uni-helpers/navigate-to'\r\n\r\nconst compressImage = {\r\n  args (fromArgs) {\r\n    // https://developers.weixin.qq.com/community/develop/doc/000c08940c865011298e0a43256800?highLine=compressHeight\r\n    if (fromArgs.compressedHeight && !fromArgs.compressHeight) {\r\n      fromArgs.compressHeight = fromArgs.compressedHeight;\r\n    }\r\n    if (fromArgs.compressedWidth && !fromArgs.compressWidth) {\r\n      fromArgs.compressWidth = fromArgs.compressedWidth;\r\n    }\r\n  }\r\n};\r\n\r\nconst protocols = {\r\n  redirectTo,\r\n  // navigateTo,  // 由于在微信开发者工具的页面参数，会显示__id__参数，因此暂时关闭mp-weixin对于navigateTo的AOP\r\n  previewImage,\r\n  getSystemInfo,\r\n  getSystemInfoSync: getSystemInfo,\r\n  showActionSheet,\r\n  getAppBaseInfo,\r\n  getDeviceInfo,\r\n  getWindowInfo,\r\n  getAppAuthorizeSetting,\r\n  compressImage\r\n};\r\nconst todos = [\r\n  'vibrate',\r\n  'preloadPage',\r\n  'unPreloadPage',\r\n  'loadSubPackage'\r\n];\r\nconst canIUses = [];\r\n\r\nconst CALLBACKS = ['success', 'fail', 'cancel', 'complete'];\r\n\r\nfunction processCallback (methodName, method, returnValue) {\r\n  return function (res) {\r\n    return method(processReturnValue(methodName, res, returnValue))\r\n  }\r\n}\r\n\r\nfunction processArgs (methodName, fromArgs, argsOption = {}, returnValue = {}, keepFromArgs = false) {\r\n  if (isPlainObject(fromArgs)) { // 一般 api 的参数解析\r\n    const toArgs = keepFromArgs === true ? fromArgs : {}; // returnValue 为 false 时，说明是格式化返回值，直接在返回值对象上修改赋值\r\n    if (isFn(argsOption)) {\r\n      argsOption = argsOption(fromArgs, toArgs) || {};\r\n    }\r\n    for (const key in fromArgs) {\r\n      if (hasOwn(argsOption, key)) {\r\n        let keyOption = argsOption[key];\r\n        if (isFn(keyOption)) {\r\n          keyOption = keyOption(fromArgs[key], fromArgs, toArgs);\r\n        }\r\n        if (!keyOption) { // 不支持的参数\r\n          console.warn(`The '${methodName}' method of platform '微信小程序' does not support option '${key}'`);\r\n        } else if (isStr(keyOption)) { // 重写参数 key\r\n          toArgs[keyOption] = fromArgs[key];\r\n        } else if (isPlainObject(keyOption)) { // {name:newName,value:value}可重新指定参数 key:value\r\n          toArgs[keyOption.name ? keyOption.name : key] = keyOption.value;\r\n        }\r\n      } else if (CALLBACKS.indexOf(key) !== -1) {\r\n        if (isFn(fromArgs[key])) {\r\n          toArgs[key] = processCallback(methodName, fromArgs[key], returnValue);\r\n        }\r\n      } else {\r\n        if (!keepFromArgs) {\r\n          toArgs[key] = fromArgs[key];\r\n        }\r\n      }\r\n    }\r\n    return toArgs\r\n  } else if (isFn(fromArgs)) {\r\n    fromArgs = processCallback(methodName, fromArgs, returnValue);\r\n  }\r\n  return fromArgs\r\n}\r\n\r\nfunction processReturnValue (methodName, res, returnValue, keepReturnValue = false) {\r\n  if (isFn(protocols.returnValue)) { // 处理通用 returnValue\r\n    res = protocols.returnValue(methodName, res);\r\n  }\r\n  return processArgs(methodName, res, returnValue, {}, keepReturnValue)\r\n}\r\n\r\nfunction wrapper (methodName, method) {\r\n  if (hasOwn(protocols, methodName)) {\r\n    const protocol = protocols[methodName];\r\n    if (!protocol) { // 暂不支持的 api\r\n      return function () {\r\n        console.error(`Platform '微信小程序' does not support '${methodName}'.`);\r\n      }\r\n    }\r\n    return function (arg1, arg2) { // 目前 api 最多两个参数\r\n      let options = protocol;\r\n      if (isFn(protocol)) {\r\n        options = protocol(arg1);\r\n      }\r\n\r\n      arg1 = processArgs(methodName, arg1, options.args, options.returnValue);\r\n\r\n      const args = [arg1];\r\n      if (typeof arg2 !== 'undefined') {\r\n        args.push(arg2);\r\n      }\r\n      if (isFn(options.name)) {\r\n        methodName = options.name(arg1);\r\n      } else if (isStr(options.name)) {\r\n        methodName = options.name;\r\n      }\r\n      const returnValue = wx[methodName].apply(wx, args);\r\n      if (isSyncApi(methodName)) { // 同步 api\r\n        return processReturnValue(methodName, returnValue, options.returnValue, isContextApi(methodName))\r\n      }\r\n      return returnValue\r\n    }\r\n  }\r\n  return method\r\n}\r\n\r\nconst todoApis = Object.create(null);\r\n\r\nconst TODOS = [\r\n  'onTabBarMidButtonTap',\r\n  'subscribePush',\r\n  'unsubscribePush',\r\n  'onPush',\r\n  'offPush',\r\n  'share'\r\n];\r\n\r\nfunction createTodoApi (name) {\r\n  return function todoApi ({\r\n    fail,\r\n    complete\r\n  }) {\r\n    const res = {\r\n      errMsg: `${name}:fail method '${name}' not supported`\r\n    };\r\n    isFn(fail) && fail(res);\r\n    isFn(complete) && complete(res);\r\n  }\r\n}\r\n\r\nTODOS.forEach(function (name) {\r\n  todoApis[name] = createTodoApi(name);\r\n});\r\n\r\nvar providers = {\r\n  oauth: ['weixin'],\r\n  share: ['weixin'],\r\n  payment: ['wxpay'],\r\n  push: ['weixin']\r\n};\r\n\r\nfunction getProvider ({\r\n  service,\r\n  success,\r\n  fail,\r\n  complete\r\n}) {\r\n  let res = false;\r\n  if (providers[service]) {\r\n    res = {\r\n      errMsg: 'getProvider:ok',\r\n      service,\r\n      provider: providers[service]\r\n    };\r\n    isFn(success) && success(res);\r\n  } else {\r\n    res = {\r\n      errMsg: 'getProvider:fail service not found'\r\n    };\r\n    isFn(fail) && fail(res);\r\n  }\r\n  isFn(complete) && complete(res);\r\n}\r\n\r\nvar extraApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  getProvider: getProvider\r\n});\r\n\r\nconst getEmitter = (function () {\r\n  let Emitter;\r\n  return function getUniEmitter () {\r\n    if (!Emitter) {\r\n      Emitter = new Vue();\r\n    }\r\n    return Emitter\r\n  }\r\n})();\r\n\r\nfunction apply (ctx, method, args) {\r\n  return ctx[method].apply(ctx, args)\r\n}\r\n\r\nfunction $on () {\r\n  return apply(getEmitter(), '$on', [...arguments])\r\n}\r\nfunction $off () {\r\n  return apply(getEmitter(), '$off', [...arguments])\r\n}\r\nfunction $once () {\r\n  return apply(getEmitter(), '$once', [...arguments])\r\n}\r\nfunction $emit () {\r\n  return apply(getEmitter(), '$emit', [...arguments])\r\n}\r\n\r\nvar eventApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  $on: $on,\r\n  $off: $off,\r\n  $once: $once,\r\n  $emit: $emit\r\n});\r\n\r\n/**\r\n * 框架内 try-catch\r\n */\r\n/**\r\n * 开发者 try-catch\r\n */\r\nfunction tryCatch (fn) {\r\n  return function () {\r\n    try {\r\n      return fn.apply(fn, arguments)\r\n    } catch (e) {\r\n      // TODO\r\n      console.error(e);\r\n    }\r\n  }\r\n}\r\n\r\nfunction getApiCallbacks (params) {\r\n  const apiCallbacks = {};\r\n  for (const name in params) {\r\n    const param = params[name];\r\n    if (isFn(param)) {\r\n      apiCallbacks[name] = tryCatch(param);\r\n      delete params[name];\r\n    }\r\n  }\r\n  return apiCallbacks\r\n}\r\n\r\nlet cid;\r\nlet cidErrMsg;\r\nlet enabled;\r\n\r\nfunction normalizePushMessage (message) {\r\n  try {\r\n    return JSON.parse(message)\r\n  } catch (e) {}\r\n  return message\r\n}\r\n\r\nfunction invokePushCallback (\r\n  args\r\n) {\r\n  if (args.type === 'enabled') {\r\n    enabled = true;\r\n  } else if (args.type === 'clientId') {\r\n    cid = args.cid;\r\n    cidErrMsg = args.errMsg;\r\n    invokeGetPushCidCallbacks(cid, args.errMsg);\r\n  } else if (args.type === 'pushMsg') {\r\n    const message = {\r\n      type: 'receive',\r\n      data: normalizePushMessage(args.message)\r\n    };\r\n    for (let i = 0; i < onPushMessageCallbacks.length; i++) {\r\n      const callback = onPushMessageCallbacks[i];\r\n      callback(message);\r\n      // 该消息已被阻止\r\n      if (message.stopped) {\r\n        break\r\n      }\r\n    }\r\n  } else if (args.type === 'click') {\r\n    onPushMessageCallbacks.forEach((callback) => {\r\n      callback({\r\n        type: 'click',\r\n        data: normalizePushMessage(args.message)\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\nconst getPushCidCallbacks = [];\r\n\r\nfunction invokeGetPushCidCallbacks (cid, errMsg) {\r\n  getPushCidCallbacks.forEach((callback) => {\r\n    callback(cid, errMsg);\r\n  });\r\n  getPushCidCallbacks.length = 0;\r\n}\r\n\r\nfunction getPushClientId (args) {\r\n  if (!isPlainObject(args)) {\r\n    args = {};\r\n  }\r\n  const {\r\n    success,\r\n    fail,\r\n    complete\r\n  } = getApiCallbacks(args);\r\n  const hasSuccess = isFn(success);\r\n  const hasFail = isFn(fail);\r\n  const hasComplete = isFn(complete);\r\n\r\n  Promise.resolve().then(() => {\r\n    if (typeof enabled === 'undefined') {\r\n      enabled = false;\r\n      cid = '';\r\n      cidErrMsg = 'uniPush is not enabled';\r\n    }\r\n    getPushCidCallbacks.push((cid, errMsg) => {\r\n      let res;\r\n      if (cid) {\r\n        res = {\r\n          errMsg: 'getPushClientId:ok',\r\n          cid\r\n        };\r\n        hasSuccess && success(res);\r\n      } else {\r\n        res = {\r\n          errMsg: 'getPushClientId:fail' + (errMsg ? ' ' + errMsg : '')\r\n        };\r\n        hasFail && fail(res);\r\n      }\r\n      hasComplete && complete(res);\r\n    });\r\n    if (typeof cid !== 'undefined') {\r\n      invokeGetPushCidCallbacks(cid, cidErrMsg);\r\n    }\r\n  });\r\n}\r\n\r\nconst onPushMessageCallbacks = [];\r\n// 不使用 defineOnApi 实现，是因为 defineOnApi 依赖 UniServiceJSBridge ，该对象目前在小程序上未提供，故简单实现\r\nconst onPushMessage = (fn) => {\r\n  if (onPushMessageCallbacks.indexOf(fn) === -1) {\r\n    onPushMessageCallbacks.push(fn);\r\n  }\r\n};\r\n\r\nconst offPushMessage = (fn) => {\r\n  if (!fn) {\r\n    onPushMessageCallbacks.length = 0;\r\n  } else {\r\n    const index = onPushMessageCallbacks.indexOf(fn);\r\n    if (index > -1) {\r\n      onPushMessageCallbacks.splice(index, 1);\r\n    }\r\n  }\r\n};\r\n\r\nlet baseInfo = wx.getAppBaseInfo && wx.getAppBaseInfo();\r\nif (!baseInfo) {\r\n  baseInfo = wx.getSystemInfoSync();\r\n}\r\nconst host = baseInfo ? baseInfo.host : null;\r\nconst shareVideoMessage =\r\n  host && host.env === 'SAAASDK' ? wx.miniapp.shareVideoMessage : wx.shareVideoMessage;\r\n\r\nvar api = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  shareVideoMessage: shareVideoMessage,\r\n  getPushClientId: getPushClientId,\r\n  onPushMessage: onPushMessage,\r\n  offPushMessage: offPushMessage,\r\n  invokePushCallback: invokePushCallback\r\n});\r\n\r\nconst mocks = ['__route__', '__wxExparserNodeId__', '__wxWebviewId__'];\r\n\r\nfunction findVmByVueId (vm, vuePid) {\r\n  const $children = vm.$children;\r\n  // 优先查找直属(反向查找:https://github.com/dcloudio/uni-app/issues/1200)\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    const childVm = $children[i];\r\n    if (childVm.$scope._$vueId === vuePid) {\r\n      return childVm\r\n    }\r\n  }\r\n  // 反向递归查找\r\n  let parentVm;\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    parentVm = findVmByVueId($children[i], vuePid);\r\n    if (parentVm) {\r\n      return parentVm\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehavior (options) {\r\n  return Behavior(options)\r\n}\r\n\r\nfunction isPage () {\r\n  return !!this.route\r\n}\r\n\r\nfunction initRelation (detail) {\r\n  this.triggerEvent('__l', detail);\r\n}\r\n\r\nfunction selectAllComponents (mpInstance, selector, $refs) {\r\n  const components = mpInstance.selectAllComponents(selector) || [];\r\n  components.forEach(component => {\r\n    const ref = component.dataset.ref;\r\n    $refs[ref] = component.$vm || toSkip(component);\r\n    {\r\n      if (component.dataset.vueGeneric === 'scoped') {\r\n        component.selectAllComponents('.scoped-ref').forEach(scopedComponent => {\r\n          selectAllComponents(scopedComponent, selector, $refs);\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction syncRefs (refs, newRefs) {\r\n  const oldKeys = new Set(...Object.keys(refs));\r\n  const newKeys = Object.keys(newRefs);\r\n  newKeys.forEach(key => {\r\n    const oldValue = refs[key];\r\n    const newValue = newRefs[key];\r\n    if (Array.isArray(oldValue) && Array.isArray(newValue) && oldValue.length === newValue.length && newValue.every(value => oldValue.includes(value))) {\r\n      return\r\n    }\r\n    refs[key] = newValue;\r\n    oldKeys.delete(key);\r\n  });\r\n  oldKeys.forEach(key => {\r\n    delete refs[key];\r\n  });\r\n  return refs\r\n}\r\n\r\nfunction initRefs (vm) {\r\n  const mpInstance = vm.$scope;\r\n  const refs = {};\r\n  Object.defineProperty(vm, '$refs', {\r\n    get () {\r\n      const $refs = {};\r\n      selectAllComponents(mpInstance, '.vue-ref', $refs);\r\n      // TODO 暂不考虑 for 中的 scoped\r\n      const forComponents = mpInstance.selectAllComponents('.vue-ref-in-for') || [];\r\n      forComponents.forEach(component => {\r\n        const ref = component.dataset.ref;\r\n        if (!$refs[ref]) {\r\n          $refs[ref] = [];\r\n        }\r\n        $refs[ref].push(component.$vm || toSkip(component));\r\n      });\r\n      return syncRefs(refs, $refs)\r\n    }\r\n  });\r\n}\r\n\r\nfunction handleLink (event) {\r\n  const {\r\n    vuePid,\r\n    vueOptions\r\n  } = event.detail || event.value; // detail 是微信,value 是百度(dipatch)\r\n\r\n  let parentVm;\r\n\r\n  if (vuePid) {\r\n    parentVm = findVmByVueId(this.$vm, vuePid);\r\n  }\r\n\r\n  if (!parentVm) {\r\n    parentVm = this.$vm;\r\n  }\r\n\r\n  vueOptions.parent = parentVm;\r\n}\r\n\r\nfunction markMPComponent (component) {\r\n  // 在 Vue 中标记为小程序组件\r\n  const IS_MP = '__v_isMPComponent';\r\n  Object.defineProperty(component, IS_MP, {\r\n    configurable: true,\r\n    enumerable: false,\r\n    value: true\r\n  });\r\n  return component\r\n}\r\n\r\nfunction toSkip (obj) {\r\n  const OB = '__ob__';\r\n  const SKIP = '__v_skip';\r\n  if (isObject(obj) && Object.isExtensible(obj)) {\r\n    // 避免被 @vue/composition-api 观测\r\n    Object.defineProperty(obj, OB, {\r\n      configurable: true,\r\n      enumerable: false,\r\n      value: {\r\n        [SKIP]: true\r\n      }\r\n    });\r\n  }\r\n  return obj\r\n}\r\n\r\nconst WORKLET_RE = /_(.*)_worklet_factory_/;\r\nfunction initWorkletMethods (mpMethods, vueMethods) {\r\n  if (vueMethods) {\r\n    Object.keys(vueMethods).forEach((name) => {\r\n      const matches = name.match(WORKLET_RE);\r\n      if (matches) {\r\n        const workletName = matches[1];\r\n        mpMethods[name] = vueMethods[name];\r\n        mpMethods[workletName] = vueMethods[workletName];\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nconst MPPage = Page;\r\nconst MPComponent = Component;\r\n\r\nconst customizeRE = /:/g;\r\n\r\nconst customize = cached((str) => {\r\n  return camelize(str.replace(customizeRE, '-'))\r\n});\r\n\r\nfunction initTriggerEvent (mpInstance) {\r\n  const oldTriggerEvent = mpInstance.triggerEvent;\r\n  const newTriggerEvent = function (event, ...args) {\r\n    // 事件名统一转驼峰格式，仅处理：当前组件为 vue 组件、当前组件为 vue 组件子组件\r\n    if (this.$vm || (this.dataset && this.dataset.comType)) {\r\n      event = customize(event);\r\n    } else {\r\n      // 针对微信/QQ小程序单独补充驼峰格式事件，以兼容历史项目\r\n      const newEvent = customize(event);\r\n      if (newEvent !== event) {\r\n        oldTriggerEvent.apply(this, [newEvent, ...args]);\r\n      }\r\n    }\r\n    return oldTriggerEvent.apply(this, [event, ...args])\r\n  };\r\n  try {\r\n    // 京东小程序 triggerEvent 为只读\r\n    mpInstance.triggerEvent = newTriggerEvent;\r\n  } catch (error) {\r\n    mpInstance._triggerEvent = newTriggerEvent;\r\n  }\r\n}\r\n\r\nfunction initHook (name, options, isComponent) {\r\n  const oldHook = options[name];\r\n  options[name] = function (...args) {\r\n    markMPComponent(this);\r\n    initTriggerEvent(this);\r\n    if (oldHook) {\r\n      return oldHook.apply(this, args)\r\n    }\r\n  };\r\n}\r\nif (!MPPage.__$wrappered) {\r\n  MPPage.__$wrappered = true;\r\n  Page = function (options = {}) {\r\n    initHook('onLoad', options);\r\n    return MPPage(options)\r\n  };\r\n  Page.after = MPPage.after;\r\n\r\n  Component = function (options = {}) {\r\n    initHook('created', options);\r\n    return MPComponent(options)\r\n  };\r\n}\r\n\r\nconst PAGE_EVENT_HOOKS = [\r\n  'onPullDownRefresh',\r\n  'onReachBottom',\r\n  'onAddToFavorites',\r\n  'onShareTimeline',\r\n  'onShareAppMessage',\r\n  'onPageScroll',\r\n  'onResize',\r\n  'onTabItemTap'\r\n];\r\n\r\nfunction initMocks (vm, mocks) {\r\n  const mpInstance = vm.$mp[vm.mpType];\r\n  mocks.forEach(mock => {\r\n    if (hasOwn(mpInstance, mock)) {\r\n      vm[mock] = mpInstance[mock];\r\n    }\r\n  });\r\n}\r\n\r\nfunction hasHook (hook, vueOptions) {\r\n  if (!vueOptions) {\r\n    return true\r\n  }\r\n\r\n  if (Vue.options && Array.isArray(Vue.options[hook])) {\r\n    return true\r\n  }\r\n\r\n  vueOptions = vueOptions.default || vueOptions;\r\n\r\n  if (isFn(vueOptions)) {\r\n    if (isFn(vueOptions.extendOptions[hook])) {\r\n      return true\r\n    }\r\n    if (vueOptions.super &&\r\n      vueOptions.super.options &&\r\n      Array.isArray(vueOptions.super.options[hook])) {\r\n      return true\r\n    }\r\n    return false\r\n  }\r\n\r\n  if (isFn(vueOptions[hook]) || Array.isArray(vueOptions[hook])) {\r\n    return true\r\n  }\r\n  const mixins = vueOptions.mixins;\r\n  if (Array.isArray(mixins)) {\r\n    return !!mixins.find(mixin => hasHook(hook, mixin))\r\n  }\r\n}\r\n\r\nfunction initHooks (mpOptions, hooks, vueOptions) {\r\n  hooks.forEach(hook => {\r\n    if (hasHook(hook, vueOptions)) {\r\n      mpOptions[hook] = function (args) {\r\n        return this.$vm && this.$vm.__call_hook(hook, args)\r\n      };\r\n    }\r\n  });\r\n}\r\n\r\nfunction initUnknownHooks (mpOptions, vueOptions, excludes = []) {\r\n  findHooks(vueOptions).forEach((hook) => initHook$1(mpOptions, hook, excludes));\r\n}\r\n\r\nfunction findHooks (vueOptions, hooks = []) {\r\n  if (vueOptions) {\r\n    Object.keys(vueOptions).forEach((name) => {\r\n      if (name.indexOf('on') === 0 && isFn(vueOptions[name])) {\r\n        hooks.push(name);\r\n      }\r\n    });\r\n  }\r\n  return hooks\r\n}\r\n\r\nfunction initHook$1 (mpOptions, hook, excludes) {\r\n  if (excludes.indexOf(hook) === -1 && !hasOwn(mpOptions, hook)) {\r\n    mpOptions[hook] = function (args) {\r\n      return this.$vm && this.$vm.__call_hook(hook, args)\r\n    };\r\n  }\r\n}\r\n\r\nfunction initVueComponent (Vue, vueOptions) {\r\n  vueOptions = vueOptions.default || vueOptions;\r\n  let VueComponent;\r\n  if (isFn(vueOptions)) {\r\n    VueComponent = vueOptions;\r\n  } else {\r\n    VueComponent = Vue.extend(vueOptions);\r\n  }\r\n  vueOptions = VueComponent.options;\r\n  return [VueComponent, vueOptions]\r\n}\r\n\r\nfunction initSlots (vm, vueSlots) {\r\n  if (Array.isArray(vueSlots) && vueSlots.length) {\r\n    const $slots = Object.create(null);\r\n    vueSlots.forEach(slotName => {\r\n      $slots[slotName] = true;\r\n    });\r\n    vm.$scopedSlots = vm.$slots = $slots;\r\n  }\r\n}\r\n\r\nfunction initVueIds (vueIds, mpInstance) {\r\n  vueIds = (vueIds || '').split(',');\r\n  const len = vueIds.length;\r\n\r\n  if (len === 1) {\r\n    mpInstance._$vueId = vueIds[0];\r\n  } else if (len === 2) {\r\n    mpInstance._$vueId = vueIds[0];\r\n    mpInstance._$vuePid = vueIds[1];\r\n  }\r\n}\r\n\r\nfunction initData (vueOptions, context) {\r\n  let data = vueOptions.data || {};\r\n  const methods = vueOptions.methods || {};\r\n\r\n  if (typeof data === 'function') {\r\n    try {\r\n      data = data.call(context); // 支持 Vue.prototype 上挂的数据\r\n    } catch (e) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.warn('根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。', data);\r\n      }\r\n    }\r\n  } else {\r\n    try {\r\n      // 对 data 格式化\r\n      data = JSON.parse(JSON.stringify(data));\r\n    } catch (e) { }\r\n  }\r\n\r\n  if (!isPlainObject(data)) {\r\n    data = {};\r\n  }\r\n\r\n  Object.keys(methods).forEach(methodName => {\r\n    if (context.__lifecycle_hooks__.indexOf(methodName) === -1 && !hasOwn(data, methodName)) {\r\n      data[methodName] = methods[methodName];\r\n    }\r\n  });\r\n\r\n  return data\r\n}\r\n\r\nconst PROP_TYPES = [String, Number, Boolean, Object, Array, null];\r\n\r\nfunction createObserver (name) {\r\n  return function observer (newVal, oldVal) {\r\n    if (this.$vm) {\r\n      this.$vm[name] = newVal; // 为了触发其他非 render watcher\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehaviors (vueOptions, initBehavior) {\r\n  const vueBehaviors = vueOptions.behaviors;\r\n  const vueExtends = vueOptions.extends;\r\n  const vueMixins = vueOptions.mixins;\r\n\r\n  let vueProps = vueOptions.props;\r\n\r\n  if (!vueProps) {\r\n    vueOptions.props = vueProps = [];\r\n  }\r\n\r\n  const behaviors = [];\r\n  if (Array.isArray(vueBehaviors)) {\r\n    vueBehaviors.forEach(behavior => {\r\n      behaviors.push(behavior.replace('uni://', `${\"wx\"}://`));\r\n      if (behavior === 'uni://form-field') {\r\n        if (Array.isArray(vueProps)) {\r\n          vueProps.push('name');\r\n          vueProps.push('value');\r\n        } else {\r\n          vueProps.name = {\r\n            type: String,\r\n            default: ''\r\n          };\r\n          vueProps.value = {\r\n            type: [String, Number, Boolean, Array, Object, Date],\r\n            default: ''\r\n          };\r\n        }\r\n      }\r\n    });\r\n  }\r\n  if (isPlainObject(vueExtends) && vueExtends.props) {\r\n    behaviors.push(\r\n      initBehavior({\r\n        properties: initProperties(vueExtends.props, true)\r\n      })\r\n    );\r\n  }\r\n  if (Array.isArray(vueMixins)) {\r\n    vueMixins.forEach(vueMixin => {\r\n      if (isPlainObject(vueMixin) && vueMixin.props) {\r\n        behaviors.push(\r\n          initBehavior({\r\n            properties: initProperties(vueMixin.props, true)\r\n          })\r\n        );\r\n      }\r\n    });\r\n  }\r\n  return behaviors\r\n}\r\n\r\nfunction parsePropType (key, type, defaultValue, file) {\r\n  // [String]=>String\r\n  if (Array.isArray(type) && type.length === 1) {\r\n    return type[0]\r\n  }\r\n  return type\r\n}\r\n\r\nfunction initProperties (props, isBehavior = false, file = '', options) {\r\n  const properties = {};\r\n  if (!isBehavior) {\r\n    properties.vueId = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    {\r\n      if ( options.virtualHost) {\r\n        properties.virtualHostStyle = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n        properties.virtualHostClass = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n      }\r\n    }\r\n    // scopedSlotsCompiler auto\r\n    properties.scopedSlotsCompiler = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    properties.vueSlots = { // 小程序不能直接定义 $slots 的 props，所以通过 vueSlots 转换到 $slots\r\n      type: null,\r\n      value: [],\r\n      observer: function (newVal, oldVal) {\r\n        const $slots = Object.create(null);\r\n        newVal.forEach(slotName => {\r\n          $slots[slotName] = true;\r\n        });\r\n        this.setData({\r\n          $slots\r\n        });\r\n      }\r\n    };\r\n  }\r\n  if (Array.isArray(props)) { // ['title']\r\n    props.forEach(key => {\r\n      properties[key] = {\r\n        type: null,\r\n        observer: createObserver(key)\r\n      };\r\n    });\r\n  } else if (isPlainObject(props)) { // {title:{type:String,default:''},content:String}\r\n    Object.keys(props).forEach(key => {\r\n      const opts = props[key];\r\n      if (isPlainObject(opts)) { // title:{type:String,default:''}\r\n        let value = opts.default;\r\n        if (isFn(value)) {\r\n          value = value();\r\n        }\r\n\r\n        opts.type = parsePropType(key, opts.type);\r\n\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(opts.type) !== -1 ? opts.type : null,\r\n          value,\r\n          observer: createObserver(key)\r\n        };\r\n      } else { // content:String\r\n        const type = parsePropType(key, opts);\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(type) !== -1 ? type : null,\r\n          observer: createObserver(key)\r\n        };\r\n      }\r\n    });\r\n  }\r\n  return properties\r\n}\r\n\r\nfunction wrapper$1 (event) {\r\n  // TODO 又得兼容 mpvue 的 mp 对象\r\n  try {\r\n    event.mp = JSON.parse(JSON.stringify(event));\r\n  } catch (e) { }\r\n\r\n  event.stopPropagation = noop;\r\n  event.preventDefault = noop;\r\n\r\n  event.target = event.target || {};\r\n\r\n  if (!hasOwn(event, 'detail')) {\r\n    event.detail = {};\r\n  }\r\n\r\n  if (hasOwn(event, 'markerId')) {\r\n    event.detail = typeof event.detail === 'object' ? event.detail : {};\r\n    event.detail.markerId = event.markerId;\r\n  }\r\n\r\n  if (isPlainObject(event.detail)) {\r\n    event.target = Object.assign({}, event.target, event.detail);\r\n  }\r\n\r\n  return event\r\n}\r\n\r\nfunction getExtraValue (vm, dataPathsArray) {\r\n  let context = vm;\r\n  dataPathsArray.forEach(dataPathArray => {\r\n    const dataPath = dataPathArray[0];\r\n    const value = dataPathArray[2];\r\n    if (dataPath || typeof value !== 'undefined') { // ['','',index,'disable']\r\n      const propPath = dataPathArray[1];\r\n      const valuePath = dataPathArray[3];\r\n\r\n      let vFor;\r\n      if (Number.isInteger(dataPath)) {\r\n        vFor = dataPath;\r\n      } else if (!dataPath) {\r\n        vFor = context;\r\n      } else if (typeof dataPath === 'string' && dataPath) {\r\n        if (dataPath.indexOf('#s#') === 0) {\r\n          vFor = dataPath.substr(3);\r\n        } else {\r\n          vFor = vm.__get_value(dataPath, context);\r\n        }\r\n      }\r\n\r\n      if (Number.isInteger(vFor)) {\r\n        context = value;\r\n      } else if (!propPath) {\r\n        context = vFor[value];\r\n      } else {\r\n        if (Array.isArray(vFor)) {\r\n          context = vFor.find(vForItem => {\r\n            return vm.__get_value(propPath, vForItem) === value\r\n          });\r\n        } else if (isPlainObject(vFor)) {\r\n          context = Object.keys(vFor).find(vForKey => {\r\n            return vm.__get_value(propPath, vFor[vForKey]) === value\r\n          });\r\n        } else {\r\n          console.error('v-for 暂不支持循环数据：', vFor);\r\n        }\r\n      }\r\n\r\n      if (valuePath) {\r\n        context = vm.__get_value(valuePath, context);\r\n      }\r\n    }\r\n  });\r\n  return context\r\n}\r\n\r\nfunction processEventExtra (vm, extra, event, __args__) {\r\n  const extraObj = {};\r\n\r\n  if (Array.isArray(extra) && extra.length) {\r\n    /**\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *'test'\r\n     */\r\n    extra.forEach((dataPath, index) => {\r\n      if (typeof dataPath === 'string') {\r\n        if (!dataPath) { // model,prop.sync\r\n          extraObj['$' + index] = vm;\r\n        } else {\r\n          if (dataPath === '$event') { // $event\r\n            extraObj['$' + index] = event;\r\n          } else if (dataPath === 'arguments') {\r\n            extraObj['$' + index] = event.detail ? event.detail.__args__ || __args__ : __args__;\r\n          } else if (dataPath.indexOf('$event.') === 0) { // $event.target.value\r\n            extraObj['$' + index] = vm.__get_value(dataPath.replace('$event.', ''), event);\r\n          } else {\r\n            extraObj['$' + index] = vm.__get_value(dataPath);\r\n          }\r\n        }\r\n      } else {\r\n        extraObj['$' + index] = getExtraValue(vm, dataPath);\r\n      }\r\n    });\r\n  }\r\n\r\n  return extraObj\r\n}\r\n\r\nfunction getObjByArray (arr) {\r\n  const obj = {};\r\n  for (let i = 1; i < arr.length; i++) {\r\n    const element = arr[i];\r\n    obj[element[0]] = element[1];\r\n  }\r\n  return obj\r\n}\r\n\r\nfunction processEventArgs (vm, event, args = [], extra = [], isCustom, methodName) {\r\n  let isCustomMPEvent = false; // wxcomponent 组件，传递原始 event 对象\r\n\r\n  // fixed 用户直接触发 mpInstance.triggerEvent\r\n  const __args__ = isPlainObject(event.detail)\r\n    ? event.detail.__args__ || [event.detail]\r\n    : [event.detail];\r\n\r\n  if (isCustom) { // 自定义事件\r\n    isCustomMPEvent = event.currentTarget &&\r\n      event.currentTarget.dataset &&\r\n      event.currentTarget.dataset.comType === 'wx';\r\n    if (!args.length) { // 无参数，直接传入 event 或 detail 数组\r\n      if (isCustomMPEvent) {\r\n        return [event]\r\n      }\r\n      return __args__\r\n    }\r\n  }\r\n\r\n  const extraObj = processEventExtra(vm, extra, event, __args__);\r\n\r\n  const ret = [];\r\n  args.forEach(arg => {\r\n    if (arg === '$event') {\r\n      if (methodName === '__set_model' && !isCustom) { // input v-model value\r\n        ret.push(event.target.value);\r\n      } else {\r\n        if (isCustom && !isCustomMPEvent) {\r\n          ret.push(__args__[0]);\r\n        } else { // wxcomponent 组件或内置组件\r\n          ret.push(event);\r\n        }\r\n      }\r\n    } else {\r\n      if (Array.isArray(arg) && arg[0] === 'o') {\r\n        ret.push(getObjByArray(arg));\r\n      } else if (typeof arg === 'string' && hasOwn(extraObj, arg)) {\r\n        ret.push(extraObj[arg]);\r\n      } else {\r\n        ret.push(arg);\r\n      }\r\n    }\r\n  });\r\n\r\n  return ret\r\n}\r\n\r\nconst ONCE = '~';\r\nconst CUSTOM = '^';\r\n\r\nfunction isMatchEventType (eventType, optType) {\r\n  return (eventType === optType) ||\r\n    (\r\n      optType === 'regionchange' &&\r\n      (\r\n        eventType === 'begin' ||\r\n        eventType === 'end'\r\n      )\r\n    )\r\n}\r\n\r\nfunction getContextVm (vm) {\r\n  let $parent = vm.$parent;\r\n  // 父组件是 scoped slots 或者其他自定义组件时继续查找\r\n  while ($parent && $parent.$parent && ($parent.$options.generic || $parent.$parent.$options.generic || $parent.$scope._$vuePid)) {\r\n    $parent = $parent.$parent;\r\n  }\r\n  return $parent && $parent.$parent\r\n}\r\n\r\nfunction handleEvent (event) {\r\n  event = wrapper$1(event);\r\n\r\n  // [['tap',[['handle',[1,2,a]],['handle1',[1,2,a]]]]]\r\n  const dataset = (event.currentTarget || event.target).dataset;\r\n  if (!dataset) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n  const eventOpts = dataset.eventOpts || dataset['event-opts']; // 支付宝 web-view 组件 dataset 非驼峰\r\n  if (!eventOpts) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n\r\n  // [['handle',[1,2,a]],['handle1',[1,2,a]]]\r\n  const eventType = event.type;\r\n\r\n  const ret = [];\r\n\r\n  eventOpts.forEach(eventOpt => {\r\n    let type = eventOpt[0];\r\n    const eventsArray = eventOpt[1];\r\n\r\n    const isCustom = type.charAt(0) === CUSTOM;\r\n    type = isCustom ? type.slice(1) : type;\r\n    const isOnce = type.charAt(0) === ONCE;\r\n    type = isOnce ? type.slice(1) : type;\r\n\r\n    if (eventsArray && isMatchEventType(eventType, type)) {\r\n      eventsArray.forEach(eventArray => {\r\n        const methodName = eventArray[0];\r\n        if (methodName) {\r\n          let handlerCtx = this.$vm;\r\n          if (handlerCtx.$options.generic) { // mp-weixin,mp-toutiao 抽象节点模拟 scoped slots\r\n            handlerCtx = getContextVm(handlerCtx) || handlerCtx;\r\n          }\r\n          if (methodName === '$emit') {\r\n            handlerCtx.$emit.apply(handlerCtx,\r\n              processEventArgs(\r\n                this.$vm,\r\n                event,\r\n                eventArray[1],\r\n                eventArray[2],\r\n                isCustom,\r\n                methodName\r\n              ));\r\n            return\r\n          }\r\n          const handler = handlerCtx[methodName];\r\n          if (!isFn(handler)) {\r\n            const type = this.$vm.mpType === 'page' ? 'Page' : 'Component';\r\n            const path = this.route || this.is;\r\n            throw new Error(`${type} \"${path}\" does not have a method \"${methodName}\"`)\r\n          }\r\n          if (isOnce) {\r\n            if (handler.once) {\r\n              return\r\n            }\r\n            handler.once = true;\r\n          }\r\n          let params = processEventArgs(\r\n            this.$vm,\r\n            event,\r\n            eventArray[1],\r\n            eventArray[2],\r\n            isCustom,\r\n            methodName\r\n          );\r\n          params = Array.isArray(params) ? params : [];\r\n          // 参数尾部增加原始事件对象用于复杂表达式内获取额外数据\r\n          if (/=\\s*\\S+\\.eventParams\\s*\\|\\|\\s*\\S+\\[['\"]event-params['\"]\\]/.test(handler.toString())) {\r\n            // eslint-disable-next-line no-sparse-arrays\r\n            params = params.concat([, , , , , , , , , , event]);\r\n          }\r\n          ret.push(handler.apply(handlerCtx, params));\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  if (\r\n    eventType === 'input' &&\r\n    ret.length === 1 &&\r\n    typeof ret[0] !== 'undefined'\r\n  ) {\r\n    return ret[0]\r\n  }\r\n}\r\n\r\nconst eventChannels = {};\r\n\r\nfunction getEventChannel (id) {\r\n  const eventChannel = eventChannels[id];\r\n  delete eventChannels[id];\r\n  return eventChannel\r\n}\r\n\r\nconst hooks = [\r\n  'onShow',\r\n  'onHide',\r\n  'onError',\r\n  'onPageNotFound',\r\n  'onThemeChange',\r\n  'onUnhandledRejection'\r\n];\r\n\r\nfunction initEventChannel () {\r\n  Vue.prototype.getOpenerEventChannel = function () {\r\n    // 微信小程序使用自身getOpenerEventChannel\r\n    {\r\n      return this.$scope.getOpenerEventChannel()\r\n    }\r\n  };\r\n  const callHook = Vue.prototype.__call_hook;\r\n  Vue.prototype.__call_hook = function (hook, args) {\r\n    if (hook === 'onLoad' && args && args.__id__) {\r\n      this.__eventChannel__ = getEventChannel(args.__id__);\r\n      delete args.__id__;\r\n    }\r\n    return callHook.call(this, hook, args)\r\n  };\r\n}\r\n\r\nfunction initScopedSlotsParams () {\r\n  const center = {};\r\n  const parents = {};\r\n\r\n  function currentId (fn) {\r\n    const vueIds = this.$options.propsData.vueId;\r\n    if (vueIds) {\r\n      const vueId = vueIds.split(',')[0];\r\n      fn(vueId);\r\n    }\r\n  }\r\n\r\n  Vue.prototype.$hasSSP = function (vueId) {\r\n    const slot = center[vueId];\r\n    if (!slot) {\r\n      parents[vueId] = this;\r\n      this.$on('hook:destroyed', () => {\r\n        delete parents[vueId];\r\n      });\r\n    }\r\n    return slot\r\n  };\r\n\r\n  Vue.prototype.$getSSP = function (vueId, name, needAll) {\r\n    const slot = center[vueId];\r\n    if (slot) {\r\n      const params = slot[name] || [];\r\n      if (needAll) {\r\n        return params\r\n      }\r\n      return params[0]\r\n    }\r\n  };\r\n\r\n  Vue.prototype.$setSSP = function (name, value) {\r\n    let index = 0;\r\n    currentId.call(this, vueId => {\r\n      const slot = center[vueId];\r\n      const params = slot[name] = slot[name] || [];\r\n      params.push(value);\r\n      index = params.length - 1;\r\n    });\r\n    return index\r\n  };\r\n\r\n  Vue.prototype.$initSSP = function () {\r\n    currentId.call(this, vueId => {\r\n      center[vueId] = {};\r\n    });\r\n  };\r\n\r\n  Vue.prototype.$callSSP = function () {\r\n    currentId.call(this, vueId => {\r\n      if (parents[vueId]) {\r\n        parents[vueId].$forceUpdate();\r\n      }\r\n    });\r\n  };\r\n\r\n  Vue.mixin({\r\n    destroyed () {\r\n      const propsData = this.$options.propsData;\r\n      const vueId = propsData && propsData.vueId;\r\n      if (vueId) {\r\n        delete center[vueId];\r\n        delete parents[vueId];\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction parseBaseApp (vm, {\r\n  mocks,\r\n  initRefs\r\n}) {\r\n  initEventChannel();\r\n  {\r\n    initScopedSlotsParams();\r\n  }\r\n  if (vm.$options.store) {\r\n    Vue.prototype.$store = vm.$options.store;\r\n  }\r\n  uniIdMixin(Vue);\r\n\r\n  Vue.prototype.mpHost = \"mp-weixin\";\r\n\r\n  Vue.mixin({\r\n    beforeCreate () {\r\n      if (!this.$options.mpType) {\r\n        return\r\n      }\r\n\r\n      this.mpType = this.$options.mpType;\r\n\r\n      this.$mp = {\r\n        data: {},\r\n        [this.mpType]: this.$options.mpInstance\r\n      };\r\n\r\n      this.$scope = this.$options.mpInstance;\r\n\r\n      delete this.$options.mpType;\r\n      delete this.$options.mpInstance;\r\n      if (\r\n        ( this.mpType === 'page') &&\r\n        typeof getApp === 'function'\r\n      ) { // hack vue-i18n\r\n        const app = getApp();\r\n        if (app.$vm && app.$vm.$i18n) {\r\n          this._i18n = app.$vm.$i18n;\r\n        }\r\n      }\r\n      if (this.mpType !== 'app') {\r\n        initRefs(this);\r\n        initMocks(this, mocks);\r\n      }\r\n    }\r\n  });\r\n\r\n  const appOptions = {\r\n    onLaunch (args) {\r\n      if (this.$vm) { // 已经初始化过了，主要是为了百度，百度 onShow 在 onLaunch 之前\r\n        return\r\n      }\r\n      {\r\n        if (wx.canIUse && !wx.canIUse('nextTick')) { // 事实 上2.2.3 即可，简单使用 2.3.0 的 nextTick 判断\r\n          console.error('当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上');\r\n        }\r\n      }\r\n\r\n      this.$vm = vm;\r\n\r\n      this.$vm.$mp = {\r\n        app: this\r\n      };\r\n\r\n      this.$vm.$scope = this;\r\n      // vm 上也挂载 globalData\r\n      this.$vm.globalData = this.globalData;\r\n\r\n      this.$vm._isMounted = true;\r\n      this.$vm.__call_hook('mounted', args);\r\n\r\n      this.$vm.__call_hook('onLaunch', args);\r\n    }\r\n  };\r\n\r\n  // 兼容旧版本 globalData\r\n  appOptions.globalData = vm.$options.globalData || {};\r\n  // 将 methods 中的方法挂在 getApp() 中\r\n  const methods = vm.$options.methods;\r\n  if (methods) {\r\n    Object.keys(methods).forEach(name => {\r\n      appOptions[name] = methods[name];\r\n    });\r\n  }\r\n\r\n  initAppLocale(Vue, vm,  normalizeLocale(wx.getAppBaseInfo().language) || LOCALE_EN\r\n    );\r\n\r\n  initHooks(appOptions, hooks);\r\n  initUnknownHooks(appOptions, vm.$options);\r\n\r\n  return appOptions\r\n}\r\n\r\nfunction parseApp (vm) {\r\n  return parseBaseApp(vm, {\r\n    mocks,\r\n    initRefs\r\n  })\r\n}\r\n\r\nfunction createApp (vm) {\r\n  App(parseApp(vm));\r\n  return vm\r\n}\r\n\r\nconst encodeReserveRE = /[!'()*]/g;\r\nconst encodeReserveReplacer = c => '%' + c.charCodeAt(0).toString(16);\r\nconst commaRE = /%2C/g;\r\n\r\n// fixed encodeURIComponent which is more conformant to RFC3986:\r\n// - escapes [!'()*]\r\n// - preserve commas\r\nconst encode = str => encodeURIComponent(str)\r\n  .replace(encodeReserveRE, encodeReserveReplacer)\r\n  .replace(commaRE, ',');\r\n\r\nfunction stringifyQuery (obj, encodeStr = encode) {\r\n  const res = obj ? Object.keys(obj).map(key => {\r\n    const val = obj[key];\r\n\r\n    if (val === undefined) {\r\n      return ''\r\n    }\r\n\r\n    if (val === null) {\r\n      return encodeStr(key)\r\n    }\r\n\r\n    if (Array.isArray(val)) {\r\n      const result = [];\r\n      val.forEach(val2 => {\r\n        if (val2 === undefined) {\r\n          return\r\n        }\r\n        if (val2 === null) {\r\n          result.push(encodeStr(key));\r\n        } else {\r\n          result.push(encodeStr(key) + '=' + encodeStr(val2));\r\n        }\r\n      });\r\n      return result.join('&')\r\n    }\r\n\r\n    return encodeStr(key) + '=' + encodeStr(val)\r\n  }).filter(x => x.length > 0).join('&') : null;\r\n  return res ? `?${res}` : ''\r\n}\r\n\r\nfunction parseBaseComponent (vueComponentOptions, {\r\n  isPage,\r\n  initRelation\r\n} = {}, needVueOptions) {\r\n  const [VueComponent, vueOptions] = initVueComponent(Vue, vueComponentOptions);\r\n\r\n  const options = {\r\n    multipleSlots: true,\r\n    // styleIsolation: 'apply-shared',\r\n    addGlobalClass: true,\r\n    ...(vueOptions.options || {})\r\n  };\r\n\r\n  {\r\n    // 微信 multipleSlots 部分情况有 bug，导致内容顺序错乱 如 u-list，提供覆盖选项\r\n    if (vueOptions['mp-weixin'] && vueOptions['mp-weixin'].options) {\r\n      Object.assign(options, vueOptions['mp-weixin'].options);\r\n    }\r\n  }\r\n\r\n  const componentOptions = {\r\n    options,\r\n    data: initData(vueOptions, Vue.prototype),\r\n    behaviors: initBehaviors(vueOptions, initBehavior),\r\n    properties: initProperties(vueOptions.props, false, vueOptions.__file, options),\r\n    lifetimes: {\r\n      attached () {\r\n        const properties = this.properties;\r\n\r\n        const options = {\r\n          mpType: isPage.call(this) ? 'page' : 'component',\r\n          mpInstance: this,\r\n          propsData: properties\r\n        };\r\n\r\n        initVueIds(properties.vueId, this);\r\n\r\n        // 处理父子关系\r\n        initRelation.call(this, {\r\n          vuePid: this._$vuePid,\r\n          vueOptions: options\r\n        });\r\n\r\n        // 初始化 vue 实例\r\n        this.$vm = new VueComponent(options);\r\n\r\n        // 处理$slots,$scopedSlots（暂不支持动态变化$slots）\r\n        initSlots(this.$vm, properties.vueSlots);\r\n\r\n        // 触发首次 setData\r\n        this.$vm.$mount();\r\n      },\r\n      ready () {\r\n        // 当组件 props 默认值为 true，初始化时传入 false 会导致 created,ready 触发, 但 attached 不触发\r\n        // https://developers.weixin.qq.com/community/develop/doc/00066ae2844cc0f8eb883e2a557800\r\n        if (this.$vm) {\r\n          this.$vm._isMounted = true;\r\n          this.$vm.__call_hook('mounted');\r\n          this.$vm.__call_hook('onReady');\r\n        }\r\n      },\r\n      detached () {\r\n        this.$vm && this.$vm.$destroy();\r\n      }\r\n    },\r\n    pageLifetimes: {\r\n      show (args) {\r\n        this.$vm && this.$vm.__call_hook('onPageShow', args);\r\n      },\r\n      hide () {\r\n        this.$vm && this.$vm.__call_hook('onPageHide');\r\n      },\r\n      resize (size) {\r\n        this.$vm && this.$vm.__call_hook('onPageResize', size);\r\n      }\r\n    },\r\n    methods: {\r\n      __l: handleLink,\r\n      __e: handleEvent\r\n    }\r\n  };\r\n  // externalClasses\r\n  if (vueOptions.externalClasses) {\r\n    componentOptions.externalClasses = vueOptions.externalClasses;\r\n  }\r\n\r\n  if (Array.isArray(vueOptions.wxsCallMethods)) {\r\n    vueOptions.wxsCallMethods.forEach(callMethod => {\r\n      componentOptions.methods[callMethod] = function (args) {\r\n        return this.$vm[callMethod](args)\r\n      };\r\n    });\r\n  }\r\n\r\n  if (needVueOptions) {\r\n    return [componentOptions, vueOptions, VueComponent]\r\n  }\r\n  if (isPage) {\r\n    return componentOptions\r\n  }\r\n  return [componentOptions, VueComponent]\r\n}\r\n\r\nfunction parseComponent (vueComponentOptions, needVueOptions) {\r\n  return parseBaseComponent(vueComponentOptions, {\r\n    isPage,\r\n    initRelation\r\n  }, needVueOptions)\r\n}\r\n\r\nconst hooks$1 = [\r\n  'onShow',\r\n  'onHide',\r\n  'onUnload'\r\n];\r\n\r\nhooks$1.push(...PAGE_EVENT_HOOKS);\r\n\r\nfunction parseBasePage (vuePageOptions) {\r\n  const [pageOptions, vueOptions] = parseComponent(vuePageOptions, true);\r\n\r\n  initHooks(pageOptions.methods, hooks$1, vueOptions);\r\n\r\n  pageOptions.methods.onLoad = function (query) {\r\n    this.options = query;\r\n    const copyQuery = Object.assign({}, query);\r\n    delete copyQuery.__id__;\r\n    this.$page = {\r\n      fullPath: '/' + (this.route || this.is) + stringifyQuery(copyQuery)\r\n    };\r\n    this.$vm.$mp.query = query; // 兼容 mpvue\r\n    this.$vm.__call_hook('onLoad', query);\r\n  };\r\n  {\r\n    initUnknownHooks(pageOptions.methods, vuePageOptions, ['onReady']);\r\n  }\r\n  {\r\n    initWorkletMethods(pageOptions.methods, vueOptions.methods);\r\n  }\r\n\r\n  return pageOptions\r\n}\r\n\r\nfunction parsePage (vuePageOptions) {\r\n  return parseBasePage(vuePageOptions)\r\n}\r\n\r\nfunction createPage (vuePageOptions) {\r\n  {\r\n    return Component(parsePage(vuePageOptions))\r\n  }\r\n}\r\n\r\nfunction createComponent (vueOptions) {\r\n  {\r\n    return Component(parseComponent(vueOptions))\r\n  }\r\n}\r\n\r\nfunction createSubpackageApp (vm) {\r\n  const appOptions = parseApp(vm);\r\n  const app = getApp({\r\n    allowDefault: true\r\n  });\r\n  vm.$scope = app;\r\n  const globalData = app.globalData;\r\n  if (globalData) {\r\n    Object.keys(appOptions.globalData).forEach(name => {\r\n      if (!hasOwn(globalData, name)) {\r\n        globalData[name] = appOptions.globalData[name];\r\n      }\r\n    });\r\n  }\r\n  Object.keys(appOptions).forEach(name => {\r\n    if (!hasOwn(app, name)) {\r\n      app[name] = appOptions[name];\r\n    }\r\n  });\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\nfunction createPlugin (vm) {\r\n  const appOptions = parseApp(vm);\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\ntodos.forEach(todoApi => {\r\n  protocols[todoApi] = false;\r\n});\r\n\r\ncanIUses.forEach(canIUseApi => {\r\n  const apiName = protocols[canIUseApi] && protocols[canIUseApi].name ? protocols[canIUseApi].name\r\n    : canIUseApi;\r\n  if (!wx.canIUse(apiName)) {\r\n    protocols[canIUseApi] = false;\r\n  }\r\n});\r\n\r\nlet uni = {};\r\n\r\nif (typeof Proxy !== 'undefined' && \"mp-weixin\" !== 'app-plus') {\r\n  uni = new Proxy({}, {\r\n    get (target, name) {\r\n      if (hasOwn(target, name)) {\r\n        return target[name]\r\n      }\r\n      if (baseApi[name]) {\r\n        return baseApi[name]\r\n      }\r\n      if (api[name]) {\r\n        return promisify(name, api[name])\r\n      }\r\n      {\r\n        if (extraApi[name]) {\r\n          return promisify(name, extraApi[name])\r\n        }\r\n        if (todoApis[name]) {\r\n          return promisify(name, todoApis[name])\r\n        }\r\n      }\r\n      if (eventApi[name]) {\r\n        return eventApi[name]\r\n      }\r\n      return promisify(name, wrapper(name, wx[name]))\r\n    },\r\n    set (target, name, value) {\r\n      target[name] = value;\r\n      return true\r\n    }\r\n  });\r\n} else {\r\n  Object.keys(baseApi).forEach(name => {\r\n    uni[name] = baseApi[name];\r\n  });\r\n\r\n  {\r\n    Object.keys(todoApis).forEach(name => {\r\n      uni[name] = promisify(name, todoApis[name]);\r\n    });\r\n    Object.keys(extraApi).forEach(name => {\r\n      uni[name] = promisify(name, extraApi[name]);\r\n    });\r\n  }\r\n\r\n  Object.keys(eventApi).forEach(name => {\r\n    uni[name] = eventApi[name];\r\n  });\r\n\r\n  Object.keys(api).forEach(name => {\r\n    uni[name] = promisify(name, api[name]);\r\n  });\r\n\r\n  Object.keys(wx).forEach(name => {\r\n    if (hasOwn(wx, name) || hasOwn(protocols, name)) {\r\n      uni[name] = promisify(name, wrapper(name, wx[name]));\r\n    }\r\n  });\r\n}\r\n\r\nwx.createApp = createApp;\r\nwx.createPage = createPage;\r\nwx.createComponent = createComponent;\r\nwx.createSubpackageApp = createSubpackageApp;\r\nwx.createPlugin = createPlugin;\r\n\r\nvar uni$1 = uni;\r\n\r\nexport default uni$1;\r\nexport { createApp, createComponent, createPage, createPlugin, createSubpackageApp };\r\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "function _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayWithHoles = require(\"./arrayWithHoles.js\");\nvar iterableToArrayLimit = require(\"./iterableToArrayLimit.js\");\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\nvar nonIterableRest = require(\"./nonIterableRest.js\");\nfunction _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || unsupportedIterableToArray(arr, i) || nonIterableRest();\n}\nmodule.exports = _slicedToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nmodule.exports = _arrayWithHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nmodule.exports = _iterableToArrayLimit, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return arrayLikeToArray(o, minLen);\n}\nmodule.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nmodule.exports = _arrayLikeToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableRest, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var setPrototypeOf = require(\"./setPrototypeOf.js\");\nvar isNativeReflectConstruct = require(\"./isNativeReflectConstruct.js\");\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nmodule.exports = _construct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _setPrototypeOf(o, p);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (module.exports = _isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayWithoutHoles = require(\"./arrayWithoutHoles.js\");\nvar iterableToArray = require(\"./iterableToArray.js\");\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\nvar nonIterableSpread = require(\"./nonIterableSpread.js\");\nfunction _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\nmodule.exports = _toConsumableArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return arrayLikeToArray(arr);\n}\nmodule.exports = _arrayWithoutHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nmodule.exports = _iterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableSpread, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "const isObject = (val) => val !== null && typeof val === 'object';\nconst defaultDelimiters = ['{', '}'];\nclass BaseFormatter {\n    constructor() {\n        this._caches = Object.create(null);\n    }\n    interpolate(message, values, delimiters = defaultDelimiters) {\n        if (!values) {\n            return [message];\n        }\n        let tokens = this._caches[message];\n        if (!tokens) {\n            tokens = parse(message, delimiters);\n            this._caches[message] = tokens;\n        }\n        return compile(tokens, values);\n    }\n}\nconst RE_TOKEN_LIST_VALUE = /^(?:\\d)+/;\nconst RE_TOKEN_NAMED_VALUE = /^(?:\\w)+/;\nfunction parse(format, [startDelimiter, endDelimiter]) {\n    const tokens = [];\n    let position = 0;\n    let text = '';\n    while (position < format.length) {\n        let char = format[position++];\n        if (char === startDelimiter) {\n            if (text) {\n                tokens.push({ type: 'text', value: text });\n            }\n            text = '';\n            let sub = '';\n            char = format[position++];\n            while (char !== undefined && char !== endDelimiter) {\n                sub += char;\n                char = format[position++];\n            }\n            const isClosed = char === endDelimiter;\n            const type = RE_TOKEN_LIST_VALUE.test(sub)\n                ? 'list'\n                : isClosed && RE_TOKEN_NAMED_VALUE.test(sub)\n                    ? 'named'\n                    : 'unknown';\n            tokens.push({ value: sub, type });\n        }\n        //  else if (char === '%') {\n        //   // when found rails i18n syntax, skip text capture\n        //   if (format[position] !== '{') {\n        //     text += char\n        //   }\n        // }\n        else {\n            text += char;\n        }\n    }\n    text && tokens.push({ type: 'text', value: text });\n    return tokens;\n}\nfunction compile(tokens, values) {\n    const compiled = [];\n    let index = 0;\n    const mode = Array.isArray(values)\n        ? 'list'\n        : isObject(values)\n            ? 'named'\n            : 'unknown';\n    if (mode === 'unknown') {\n        return compiled;\n    }\n    while (index < tokens.length) {\n        const token = tokens[index];\n        switch (token.type) {\n            case 'text':\n                compiled.push(token.value);\n                break;\n            case 'list':\n                compiled.push(values[parseInt(token.value, 10)]);\n                break;\n            case 'named':\n                if (mode === 'named') {\n                    compiled.push(values[token.value]);\n                }\n                else {\n                    if (process.env.NODE_ENV !== 'production') {\n                        console.warn(`Type of token '${token.type}' and format of value '${mode}' don't match!`);\n                    }\n                }\n                break;\n            case 'unknown':\n                if (process.env.NODE_ENV !== 'production') {\n                    console.warn(`Detect 'unknown' type of token!`);\n                }\n                break;\n        }\n        index++;\n    }\n    return compiled;\n}\n\nconst LOCALE_ZH_HANS = 'zh-Hans';\nconst LOCALE_ZH_HANT = 'zh-Hant';\nconst LOCALE_EN = 'en';\nconst LOCALE_FR = 'fr';\nconst LOCALE_ES = 'es';\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst hasOwn = (val, key) => hasOwnProperty.call(val, key);\nconst defaultFormatter = new BaseFormatter();\nfunction include(str, parts) {\n    return !!parts.find((part) => str.indexOf(part) !== -1);\n}\nfunction startsWith(str, parts) {\n    return parts.find((part) => str.indexOf(part) === 0);\n}\nfunction normalizeLocale(locale, messages) {\n    if (!locale) {\n        return;\n    }\n    locale = locale.trim().replace(/_/g, '-');\n    if (messages && messages[locale]) {\n        return locale;\n    }\n    locale = locale.toLowerCase();\n    if (locale === 'chinese') {\n        // 支付宝\n        return LOCALE_ZH_HANS;\n    }\n    if (locale.indexOf('zh') === 0) {\n        if (locale.indexOf('-hans') > -1) {\n            return LOCALE_ZH_HANS;\n        }\n        if (locale.indexOf('-hant') > -1) {\n            return LOCALE_ZH_HANT;\n        }\n        if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\n            return LOCALE_ZH_HANT;\n        }\n        return LOCALE_ZH_HANS;\n    }\n    let locales = [LOCALE_EN, LOCALE_FR, LOCALE_ES];\n    if (messages && Object.keys(messages).length > 0) {\n        locales = Object.keys(messages);\n    }\n    const lang = startsWith(locale, locales);\n    if (lang) {\n        return lang;\n    }\n}\nclass I18n {\n    constructor({ locale, fallbackLocale, messages, watcher, formater, }) {\n        this.locale = LOCALE_EN;\n        this.fallbackLocale = LOCALE_EN;\n        this.message = {};\n        this.messages = {};\n        this.watchers = [];\n        if (fallbackLocale) {\n            this.fallbackLocale = fallbackLocale;\n        }\n        this.formater = formater || defaultFormatter;\n        this.messages = messages || {};\n        this.setLocale(locale || LOCALE_EN);\n        if (watcher) {\n            this.watchLocale(watcher);\n        }\n    }\n    setLocale(locale) {\n        const oldLocale = this.locale;\n        this.locale = normalizeLocale(locale, this.messages) || this.fallbackLocale;\n        if (!this.messages[this.locale]) {\n            // 可能初始化时不存在\n            this.messages[this.locale] = {};\n        }\n        this.message = this.messages[this.locale];\n        // 仅发生变化时，通知\n        if (oldLocale !== this.locale) {\n            this.watchers.forEach((watcher) => {\n                watcher(this.locale, oldLocale);\n            });\n        }\n    }\n    getLocale() {\n        return this.locale;\n    }\n    watchLocale(fn) {\n        const index = this.watchers.push(fn) - 1;\n        return () => {\n            this.watchers.splice(index, 1);\n        };\n    }\n    add(locale, message, override = true) {\n        const curMessages = this.messages[locale];\n        if (curMessages) {\n            if (override) {\n                Object.assign(curMessages, message);\n            }\n            else {\n                Object.keys(message).forEach((key) => {\n                    if (!hasOwn(curMessages, key)) {\n                        curMessages[key] = message[key];\n                    }\n                });\n            }\n        }\n        else {\n            this.messages[locale] = message;\n        }\n    }\n    f(message, values, delimiters) {\n        return this.formater.interpolate(message, values, delimiters).join('');\n    }\n    t(key, locale, values) {\n        let message = this.message;\n        if (typeof locale === 'string') {\n            locale = normalizeLocale(locale, this.messages);\n            locale && (message = this.messages[locale]);\n        }\n        else {\n            values = locale;\n        }\n        if (!hasOwn(message, key)) {\n            console.warn(`Cannot translate the value of keypath ${key}. Use the value of keypath as default.`);\n            return key;\n        }\n        return this.formater.interpolate(message[key], values).join('');\n    }\n}\n\nfunction watchAppLocale(appVm, i18n) {\n    // 需要保证 watch 的触发在组件渲染之前\n    if (appVm.$watchLocale) {\n        // vue2\n        appVm.$watchLocale((newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n    else {\n        appVm.$watch(() => appVm.$locale, (newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n}\nfunction getDefaultLocale() {\n    if (typeof uni !== 'undefined' && uni.getLocale) {\n        return uni.getLocale();\n    }\n    // 小程序平台，uni 和 uni-i18n 互相引用，导致访问不到 uni，故在 global 上挂了 getLocale\n    if (typeof global !== 'undefined' && global.getLocale) {\n        return global.getLocale();\n    }\n    return LOCALE_EN;\n}\nfunction initVueI18n(locale, messages = {}, fallbackLocale, watcher) {\n    // 兼容旧版本入参\n    if (typeof locale !== 'string') {\n        [locale, messages] = [\n            messages,\n            locale,\n        ];\n    }\n    if (typeof locale !== 'string') {\n        // 因为小程序平台，uni-i18n 和 uni 互相引用，导致此时访问 uni 时，为 undefined\n        locale = getDefaultLocale();\n    }\n    if (typeof fallbackLocale !== 'string') {\n        fallbackLocale =\n            (typeof __uniConfig !== 'undefined' && __uniConfig.fallbackLocale) ||\n                LOCALE_EN;\n    }\n    const i18n = new I18n({\n        locale,\n        fallbackLocale,\n        messages,\n        watcher,\n    });\n    let t = (key, values) => {\n        if (typeof getApp !== 'function') {\n            // app view\n            /* eslint-disable no-func-assign */\n            t = function (key, values) {\n                return i18n.t(key, values);\n            };\n        }\n        else {\n            let isWatchedAppLocale = false;\n            t = function (key, values) {\n                const appVm = getApp().$vm;\n                // 可能$vm还不存在，比如在支付宝小程序中，组件定义较早，在props的default里使用了t()函数（如uni-goods-nav），此时app还未初始化\n                // options: {\n                // \ttype: Array,\n                // \tdefault () {\n                // \t\treturn [{\n                // \t\t\ticon: 'shop',\n                // \t\t\ttext: t(\"uni-goods-nav.options.shop\"),\n                // \t\t}, {\n                // \t\t\ticon: 'cart',\n                // \t\t\ttext: t(\"uni-goods-nav.options.cart\")\n                // \t\t}]\n                // \t}\n                // },\n                if (appVm) {\n                    // 触发响应式\n                    appVm.$locale;\n                    if (!isWatchedAppLocale) {\n                        isWatchedAppLocale = true;\n                        watchAppLocale(appVm, i18n);\n                    }\n                }\n                return i18n.t(key, values);\n            };\n        }\n        return t(key, values);\n    };\n    return {\n        i18n,\n        f(message, values, delimiters) {\n            return i18n.f(message, values, delimiters);\n        },\n        t(key, values) {\n            return t(key, values);\n        },\n        add(locale, message, override = true) {\n            return i18n.add(locale, message, override);\n        },\n        watch(fn) {\n            return i18n.watchLocale(fn);\n        },\n        getLocale() {\n            return i18n.getLocale();\n        },\n        setLocale(newLocale) {\n            return i18n.setLocale(newLocale);\n        },\n    };\n}\n\nconst isString = (val) => typeof val === 'string';\nlet formater;\nfunction hasI18nJson(jsonObj, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    return walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                return true;\n            }\n        }\n        else {\n            return hasI18nJson(value, delimiters);\n        }\n    });\n}\nfunction parseI18nJson(jsonObj, values, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                jsonObj[key] = compileStr(value, values, delimiters);\n            }\n        }\n        else {\n            parseI18nJson(value, values, delimiters);\n        }\n    });\n    return jsonObj;\n}\nfunction compileI18nJsonStr(jsonStr, { locale, locales, delimiters, }) {\n    if (!isI18nStr(jsonStr, delimiters)) {\n        return jsonStr;\n    }\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    const localeValues = [];\n    Object.keys(locales).forEach((name) => {\n        if (name !== locale) {\n            localeValues.push({\n                locale: name,\n                values: locales[name],\n            });\n        }\n    });\n    localeValues.unshift({ locale, values: locales[locale] });\n    try {\n        return JSON.stringify(compileJsonObj(JSON.parse(jsonStr), localeValues, delimiters), null, 2);\n    }\n    catch (e) { }\n    return jsonStr;\n}\nfunction isI18nStr(value, delimiters) {\n    return value.indexOf(delimiters[0]) > -1;\n}\nfunction compileStr(value, values, delimiters) {\n    return formater.interpolate(value, values, delimiters).join('');\n}\nfunction compileValue(jsonObj, key, localeValues, delimiters) {\n    const value = jsonObj[key];\n    if (isString(value)) {\n        // 存在国际化\n        if (isI18nStr(value, delimiters)) {\n            jsonObj[key] = compileStr(value, localeValues[0].values, delimiters);\n            if (localeValues.length > 1) {\n                // 格式化国际化语言\n                const valueLocales = (jsonObj[key + 'Locales'] = {});\n                localeValues.forEach((localValue) => {\n                    valueLocales[localValue.locale] = compileStr(value, localValue.values, delimiters);\n                });\n            }\n        }\n    }\n    else {\n        compileJsonObj(value, localeValues, delimiters);\n    }\n}\nfunction compileJsonObj(jsonObj, localeValues, delimiters) {\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        compileValue(jsonObj, key, localeValues, delimiters);\n    });\n    return jsonObj;\n}\nfunction walkJsonObj(jsonObj, walk) {\n    if (Array.isArray(jsonObj)) {\n        for (let i = 0; i < jsonObj.length; i++) {\n            if (walk(jsonObj, i)) {\n                return true;\n            }\n        }\n    }\n    else if (isObject(jsonObj)) {\n        for (const key in jsonObj) {\n            if (walk(jsonObj, key)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\nfunction resolveLocale(locales) {\n    return (locale) => {\n        if (!locale) {\n            return locale;\n        }\n        locale = normalizeLocale(locale) || locale;\n        return resolveLocaleChain(locale).find((locale) => locales.indexOf(locale) > -1);\n    };\n}\nfunction resolveLocaleChain(locale) {\n    const chain = [];\n    const tokens = locale.split('-');\n    while (tokens.length) {\n        chain.push(tokens.join('-'));\n        tokens.pop();\n    }\n    return chain;\n}\n\nexport { BaseFormatter as Formatter, I18n, LOCALE_EN, LOCALE_ES, LOCALE_FR, LOCALE_ZH_HANS, LOCALE_ZH_HANT, compileI18nJsonStr, hasI18nJson, initVueI18n, isI18nStr, isString, normalizeLocale, parseI18nJson, resolveLocale };\n", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "/*!\n * Vue.js v2.6.11\n * (c) 2014-2024 Evan You\n * Released under the MIT License.\n */\n/*  */\n\nvar emptyObject = Object.freeze({});\n\n// These helpers produce better VM code in JS engines due to their\n// explicitness and function inlining.\nfunction isUndef (v) {\n  return v === undefined || v === null\n}\n\nfunction isDef (v) {\n  return v !== undefined && v !== null\n}\n\nfunction isTrue (v) {\n  return v === true\n}\n\nfunction isFalse (v) {\n  return v === false\n}\n\n/**\n * Check if value is primitive.\n */\nfunction isPrimitive (value) {\n  return (\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    // $flow-disable-line\n    typeof value === 'symbol' ||\n    typeof value === 'boolean'\n  )\n}\n\n/**\n * Quick object check - this is primarily used to tell\n * Objects from primitive values when we know the value\n * is a JSON-compliant type.\n */\nfunction isObject (obj) {\n  return obj !== null && typeof obj === 'object'\n}\n\n/**\n * Get the raw type string of a value, e.g., [object Object].\n */\nvar _toString = Object.prototype.toString;\n\nfunction toRawType (value) {\n  return _toString.call(value).slice(8, -1)\n}\n\n/**\n * Strict object type check. Only returns true\n * for plain JavaScript objects.\n */\nfunction isPlainObject (obj) {\n  return _toString.call(obj) === '[object Object]'\n}\n\nfunction isRegExp (v) {\n  return _toString.call(v) === '[object RegExp]'\n}\n\n/**\n * Check if val is a valid array index.\n */\nfunction isValidArrayIndex (val) {\n  var n = parseFloat(String(val));\n  return n >= 0 && Math.floor(n) === n && isFinite(val)\n}\n\nfunction isPromise (val) {\n  return (\n    isDef(val) &&\n    typeof val.then === 'function' &&\n    typeof val.catch === 'function'\n  )\n}\n\n/**\n * Convert a value to a string that is actually rendered.\n */\nfunction toString (val) {\n  return val == null\n    ? ''\n    : Array.isArray(val) || (isPlainObject(val) && val.toString === _toString)\n      ? JSON.stringify(val, null, 2)\n      : String(val)\n}\n\n/**\n * Convert an input value to a number for persistence.\n * If the conversion fails, return original string.\n */\nfunction toNumber (val) {\n  var n = parseFloat(val);\n  return isNaN(n) ? val : n\n}\n\n/**\n * Make a map and return a function for checking if a key\n * is in that map.\n */\nfunction makeMap (\n  str,\n  expectsLowerCase\n) {\n  var map = Object.create(null);\n  var list = str.split(',');\n  for (var i = 0; i < list.length; i++) {\n    map[list[i]] = true;\n  }\n  return expectsLowerCase\n    ? function (val) { return map[val.toLowerCase()]; }\n    : function (val) { return map[val]; }\n}\n\n/**\n * Check if a tag is a built-in tag.\n */\nvar isBuiltInTag = makeMap('slot,component', true);\n\n/**\n * Check if an attribute is a reserved attribute.\n */\nvar isReservedAttribute = makeMap('key,ref,slot,slot-scope,is');\n\n/**\n * Remove an item from an array.\n */\nfunction remove (arr, item) {\n  if (arr.length) {\n    var index = arr.indexOf(item);\n    if (index > -1) {\n      return arr.splice(index, 1)\n    }\n  }\n}\n\n/**\n * Check whether an object has the property.\n */\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn (obj, key) {\n  return hasOwnProperty.call(obj, key)\n}\n\n/**\n * Create a cached version of a pure function.\n */\nfunction cached (fn) {\n  var cache = Object.create(null);\n  return (function cachedFn (str) {\n    var hit = cache[str];\n    return hit || (cache[str] = fn(str))\n  })\n}\n\n/**\n * Camelize a hyphen-delimited string.\n */\nvar camelizeRE = /-(\\w)/g;\nvar camelize = cached(function (str) {\n  return str.replace(camelizeRE, function (_, c) { return c ? c.toUpperCase() : ''; })\n});\n\n/**\n * Capitalize a string.\n */\nvar capitalize = cached(function (str) {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n});\n\n/**\n * Hyphenate a camelCase string.\n */\nvar hyphenateRE = /\\B([A-Z])/g;\nvar hyphenate = cached(function (str) {\n  return str.replace(hyphenateRE, '-$1').toLowerCase()\n});\n\n/**\n * Simple bind polyfill for environments that do not support it,\n * e.g., PhantomJS 1.x. Technically, we don't need this anymore\n * since native bind is now performant enough in most browsers.\n * But removing it would mean breaking code that was able to run in\n * PhantomJS 1.x, so this must be kept for backward compatibility.\n */\n\n/* istanbul ignore next */\nfunction polyfillBind (fn, ctx) {\n  function boundFn (a) {\n    var l = arguments.length;\n    return l\n      ? l > 1\n        ? fn.apply(ctx, arguments)\n        : fn.call(ctx, a)\n      : fn.call(ctx)\n  }\n\n  boundFn._length = fn.length;\n  return boundFn\n}\n\nfunction nativeBind (fn, ctx) {\n  return fn.bind(ctx)\n}\n\nvar bind = Function.prototype.bind\n  ? nativeBind\n  : polyfillBind;\n\n/**\n * Convert an Array-like object to a real Array.\n */\nfunction toArray (list, start) {\n  start = start || 0;\n  var i = list.length - start;\n  var ret = new Array(i);\n  while (i--) {\n    ret[i] = list[i + start];\n  }\n  return ret\n}\n\n/**\n * Mix properties into target object.\n */\nfunction extend (to, _from) {\n  for (var key in _from) {\n    to[key] = _from[key];\n  }\n  return to\n}\n\n/**\n * Merge an Array of Objects into a single Object.\n */\nfunction toObject (arr) {\n  var res = {};\n  for (var i = 0; i < arr.length; i++) {\n    if (arr[i]) {\n      extend(res, arr[i]);\n    }\n  }\n  return res\n}\n\n/* eslint-disable no-unused-vars */\n\n/**\n * Perform no operation.\n * Stubbing args to make Flow happy without leaving useless transpiled code\n * with ...rest (https://flow.org/blog/2017/05/07/Strict-Function-Call-Arity/).\n */\nfunction noop (a, b, c) {}\n\n/**\n * Always return false.\n */\nvar no = function (a, b, c) { return false; };\n\n/* eslint-enable no-unused-vars */\n\n/**\n * Return the same value.\n */\nvar identity = function (_) { return _; };\n\n/**\n * Check if two values are loosely equal - that is,\n * if they are plain objects, do they have the same shape?\n */\nfunction looseEqual (a, b) {\n  if (a === b) { return true }\n  var isObjectA = isObject(a);\n  var isObjectB = isObject(b);\n  if (isObjectA && isObjectB) {\n    try {\n      var isArrayA = Array.isArray(a);\n      var isArrayB = Array.isArray(b);\n      if (isArrayA && isArrayB) {\n        return a.length === b.length && a.every(function (e, i) {\n          return looseEqual(e, b[i])\n        })\n      } else if (a instanceof Date && b instanceof Date) {\n        return a.getTime() === b.getTime()\n      } else if (!isArrayA && !isArrayB) {\n        var keysA = Object.keys(a);\n        var keysB = Object.keys(b);\n        return keysA.length === keysB.length && keysA.every(function (key) {\n          return looseEqual(a[key], b[key])\n        })\n      } else {\n        /* istanbul ignore next */\n        return false\n      }\n    } catch (e) {\n      /* istanbul ignore next */\n      return false\n    }\n  } else if (!isObjectA && !isObjectB) {\n    return String(a) === String(b)\n  } else {\n    return false\n  }\n}\n\n/**\n * Return the first index at which a loosely equal value can be\n * found in the array (if value is a plain object, the array must\n * contain an object of the same shape), or -1 if it is not present.\n */\nfunction looseIndexOf (arr, val) {\n  for (var i = 0; i < arr.length; i++) {\n    if (looseEqual(arr[i], val)) { return i }\n  }\n  return -1\n}\n\n/**\n * Ensure a function is called only once.\n */\nfunction once (fn) {\n  var called = false;\n  return function () {\n    if (!called) {\n      called = true;\n      fn.apply(this, arguments);\n    }\n  }\n}\n\nvar ASSET_TYPES = [\n  'component',\n  'directive',\n  'filter'\n];\n\nvar LIFECYCLE_HOOKS = [\n  'beforeCreate',\n  'created',\n  'beforeMount',\n  'mounted',\n  'beforeUpdate',\n  'updated',\n  'beforeDestroy',\n  'destroyed',\n  'activated',\n  'deactivated',\n  'errorCaptured',\n  'serverPrefetch'\n];\n\n/*  */\n\n\n\nvar config = ({\n  /**\n   * Option merge strategies (used in core/util/options)\n   */\n  // $flow-disable-line\n  optionMergeStrategies: Object.create(null),\n\n  /**\n   * Whether to suppress warnings.\n   */\n  silent: false,\n\n  /**\n   * Show production mode tip message on boot?\n   */\n  productionTip: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to enable devtools\n   */\n  devtools: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to record perf\n   */\n  performance: false,\n\n  /**\n   * Error handler for watcher errors\n   */\n  errorHandler: null,\n\n  /**\n   * Warn handler for watcher warns\n   */\n  warnHandler: null,\n\n  /**\n   * Ignore certain custom elements\n   */\n  ignoredElements: [],\n\n  /**\n   * Custom user key aliases for v-on\n   */\n  // $flow-disable-line\n  keyCodes: Object.create(null),\n\n  /**\n   * Check if a tag is reserved so that it cannot be registered as a\n   * component. This is platform-dependent and may be overwritten.\n   */\n  isReservedTag: no,\n\n  /**\n   * Check if an attribute is reserved so that it cannot be used as a component\n   * prop. This is platform-dependent and may be overwritten.\n   */\n  isReservedAttr: no,\n\n  /**\n   * Check if a tag is an unknown element.\n   * Platform-dependent.\n   */\n  isUnknownElement: no,\n\n  /**\n   * Get the namespace of an element\n   */\n  getTagNamespace: noop,\n\n  /**\n   * Parse the real tag name for the specific platform.\n   */\n  parsePlatformTagName: identity,\n\n  /**\n   * Check if an attribute must be bound using property, e.g. value\n   * Platform-dependent.\n   */\n  mustUseProp: no,\n\n  /**\n   * Perform updates asynchronously. Intended to be used by Vue Test Utils\n   * This will significantly reduce performance if set to false.\n   */\n  async: true,\n\n  /**\n   * Exposed for legacy reasons\n   */\n  _lifecycleHooks: LIFECYCLE_HOOKS\n});\n\n/*  */\n\n/**\n * unicode letters used for parsing html tags, component names and property paths.\n * using https://www.w3.org/TR/html53/semantics-scripting.html#potentialcustomelementname\n * skipping \\u10000-\\uEFFFF due to it freezing up PhantomJS\n */\nvar unicodeRegExp = /a-zA-Z\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD/;\n\n/**\n * Check if a string starts with $ or _\n */\nfunction isReserved (str) {\n  var c = (str + '').charCodeAt(0);\n  return c === 0x24 || c === 0x5F\n}\n\n/**\n * Define a property.\n */\nfunction def (obj, key, val, enumerable) {\n  Object.defineProperty(obj, key, {\n    value: val,\n    enumerable: !!enumerable,\n    writable: true,\n    configurable: true\n  });\n}\n\n/**\n * Parse simple path.\n */\nvar bailRE = new RegExp((\"[^\" + (unicodeRegExp.source) + \".$_\\\\d]\"));\nfunction parsePath (path) {\n  if (bailRE.test(path)) {\n    return\n  }\n  var segments = path.split('.');\n  return function (obj) {\n    for (var i = 0; i < segments.length; i++) {\n      if (!obj) { return }\n      obj = obj[segments[i]];\n    }\n    return obj\n  }\n}\n\n/*  */\n\n// can we use __proto__?\nvar hasProto = '__proto__' in {};\n\n// Browser environment sniffing\nvar inBrowser = typeof window !== 'undefined';\nvar inWeex = typeof WXEnvironment !== 'undefined' && !!WXEnvironment.platform;\nvar weexPlatform = inWeex && WXEnvironment.platform.toLowerCase();\nvar UA = inBrowser && window.navigator && window.navigator.userAgent.toLowerCase();\nvar isIE = UA && /msie|trident/.test(UA);\nvar isIE9 = UA && UA.indexOf('msie 9.0') > 0;\nvar isEdge = UA && UA.indexOf('edge/') > 0;\nvar isAndroid = (UA && UA.indexOf('android') > 0) || (weexPlatform === 'android');\nvar isIOS = (UA && /iphone|ipad|ipod|ios/.test(UA)) || (weexPlatform === 'ios');\nvar isChrome = UA && /chrome\\/\\d+/.test(UA) && !isEdge;\nvar isPhantomJS = UA && /phantomjs/.test(UA);\nvar isFF = UA && UA.match(/firefox\\/(\\d+)/);\n\n// Firefox has a \"watch\" function on Object.prototype...\nvar nativeWatch = ({}).watch;\nif (inBrowser) {\n  try {\n    var opts = {};\n    Object.defineProperty(opts, 'passive', ({\n      get: function get () {\n      }\n    })); // https://github.com/facebook/flow/issues/285\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}\n\n// this needs to be lazy-evaled because vue may be required before\n// vue-server-renderer can set VUE_ENV\nvar _isServer;\nvar isServerRendering = function () {\n  if (_isServer === undefined) {\n    /* istanbul ignore if */\n    if (!inBrowser && !inWeex && typeof global !== 'undefined') {\n      // detect presence of vue-server-renderer and avoid\n      // Webpack shimming the process\n      _isServer = global['process'] && global['process'].env.VUE_ENV === 'server';\n    } else {\n      _isServer = false;\n    }\n  }\n  return _isServer\n};\n\n// detect devtools\nvar devtools = inBrowser && window.__VUE_DEVTOOLS_GLOBAL_HOOK__;\n\n/* istanbul ignore next */\nfunction isNative (Ctor) {\n  return typeof Ctor === 'function' && /native code/.test(Ctor.toString())\n}\n\nvar hasSymbol =\n  typeof Symbol !== 'undefined' && isNative(Symbol) &&\n  typeof Reflect !== 'undefined' && isNative(Reflect.ownKeys);\n\nvar _Set;\n/* istanbul ignore if */ // $flow-disable-line\nif (typeof Set !== 'undefined' && isNative(Set)) {\n  // use native Set when available.\n  _Set = Set;\n} else {\n  // a non-standard Set polyfill that only works with primitive keys.\n  _Set = /*@__PURE__*/(function () {\n    function Set () {\n      this.set = Object.create(null);\n    }\n    Set.prototype.has = function has (key) {\n      return this.set[key] === true\n    };\n    Set.prototype.add = function add (key) {\n      this.set[key] = true;\n    };\n    Set.prototype.clear = function clear () {\n      this.set = Object.create(null);\n    };\n\n    return Set;\n  }());\n}\n\n/*  */\n\nvar warn = noop;\nvar tip = noop;\nvar generateComponentTrace = (noop); // work around flow check\nvar formatComponentName = (noop);\n\nif (process.env.NODE_ENV !== 'production') {\n  var hasConsole = typeof console !== 'undefined';\n  var classifyRE = /(?:^|[-_])(\\w)/g;\n  var classify = function (str) { return str\n    .replace(classifyRE, function (c) { return c.toUpperCase(); })\n    .replace(/[-_]/g, ''); };\n\n  warn = function (msg, vm) {\n    var trace = vm ? generateComponentTrace(vm) : '';\n\n    if (config.warnHandler) {\n      config.warnHandler.call(null, msg, vm, trace);\n    } else if (hasConsole && (!config.silent)) {\n      console.error((\"[Vue warn]: \" + msg + trace));\n    }\n  };\n\n  tip = function (msg, vm) {\n    if (hasConsole && (!config.silent)) {\n      console.warn(\"[Vue tip]: \" + msg + (\n        vm ? generateComponentTrace(vm) : ''\n      ));\n    }\n  };\n\n  formatComponentName = function (vm, includeFile) {\n    if (vm.$root === vm) {\n      if (vm.$options && vm.$options.__file) { // fixed by xxxxxx\n        return ('') + vm.$options.__file\n      }\n      return '<Root>'\n    }\n    var options = typeof vm === 'function' && vm.cid != null\n      ? vm.options\n      : vm._isVue\n        ? vm.$options || vm.constructor.options\n        : vm;\n    var name = options.name || options._componentTag;\n    var file = options.__file;\n    if (!name && file) {\n      var match = file.match(/([^/\\\\]+)\\.vue$/);\n      name = match && match[1];\n    }\n\n    return (\n      (name ? (\"<\" + (classify(name)) + \">\") : \"<Anonymous>\") +\n      (file && includeFile !== false ? (\" at \" + file) : '')\n    )\n  };\n\n  var repeat = function (str, n) {\n    var res = '';\n    while (n) {\n      if (n % 2 === 1) { res += str; }\n      if (n > 1) { str += str; }\n      n >>= 1;\n    }\n    return res\n  };\n\n  generateComponentTrace = function (vm) {\n    if (vm._isVue && vm.$parent) {\n      var tree = [];\n      var currentRecursiveSequence = 0;\n      while (vm && vm.$options.name !== 'PageBody') {\n        if (tree.length > 0) {\n          var last = tree[tree.length - 1];\n          if (last.constructor === vm.constructor) {\n            currentRecursiveSequence++;\n            vm = vm.$parent;\n            continue\n          } else if (currentRecursiveSequence > 0) {\n            tree[tree.length - 1] = [last, currentRecursiveSequence];\n            currentRecursiveSequence = 0;\n          }\n        }\n        !vm.$options.isReserved && tree.push(vm);\n        vm = vm.$parent;\n      }\n      return '\\n\\nfound in\\n\\n' + tree\n        .map(function (vm, i) { return (\"\" + (i === 0 ? '---> ' : repeat(' ', 5 + i * 2)) + (Array.isArray(vm)\n            ? ((formatComponentName(vm[0])) + \"... (\" + (vm[1]) + \" recursive calls)\")\n            : formatComponentName(vm))); })\n        .join('\\n')\n    } else {\n      return (\"\\n\\n(found in \" + (formatComponentName(vm)) + \")\")\n    }\n  };\n}\n\n/*  */\n\nvar uid = 0;\n\n/**\n * A dep is an observable that can have multiple\n * directives subscribing to it.\n */\nvar Dep = function Dep () {\n  this.id = uid++;\n  this.subs = [];\n};\n\nDep.prototype.addSub = function addSub (sub) {\n  this.subs.push(sub);\n};\n\nDep.prototype.removeSub = function removeSub (sub) {\n  remove(this.subs, sub);\n};\n\nDep.prototype.depend = function depend () {\n  if (Dep.SharedObject.target) {\n    Dep.SharedObject.target.addDep(this);\n  }\n};\n\nDep.prototype.notify = function notify () {\n  // stabilize the subscriber list first\n  var subs = this.subs.slice();\n  if (process.env.NODE_ENV !== 'production' && !config.async) {\n    // subs aren't sorted in scheduler if not running async\n    // we need to sort them now to make sure they fire in correct\n    // order\n    subs.sort(function (a, b) { return a.id - b.id; });\n  }\n  for (var i = 0, l = subs.length; i < l; i++) {\n    subs[i].update();\n  }\n};\n\n// The current target watcher being evaluated.\n// This is globally unique because only one watcher\n// can be evaluated at a time.\n// fixed by xxxxxx (nvue shared vuex)\n/* eslint-disable no-undef */\nDep.SharedObject = {};\nDep.SharedObject.target = null;\nDep.SharedObject.targetStack = [];\n\nfunction pushTarget (target) {\n  Dep.SharedObject.targetStack.push(target);\n  Dep.SharedObject.target = target;\n  Dep.target = target;\n}\n\nfunction popTarget () {\n  Dep.SharedObject.targetStack.pop();\n  Dep.SharedObject.target = Dep.SharedObject.targetStack[Dep.SharedObject.targetStack.length - 1];\n  Dep.target = Dep.SharedObject.target;\n}\n\n/*  */\n\nvar VNode = function VNode (\n  tag,\n  data,\n  children,\n  text,\n  elm,\n  context,\n  componentOptions,\n  asyncFactory\n) {\n  this.tag = tag;\n  this.data = data;\n  this.children = children;\n  this.text = text;\n  this.elm = elm;\n  this.ns = undefined;\n  this.context = context;\n  this.fnContext = undefined;\n  this.fnOptions = undefined;\n  this.fnScopeId = undefined;\n  this.key = data && data.key;\n  this.componentOptions = componentOptions;\n  this.componentInstance = undefined;\n  this.parent = undefined;\n  this.raw = false;\n  this.isStatic = false;\n  this.isRootInsert = true;\n  this.isComment = false;\n  this.isCloned = false;\n  this.isOnce = false;\n  this.asyncFactory = asyncFactory;\n  this.asyncMeta = undefined;\n  this.isAsyncPlaceholder = false;\n};\n\nvar prototypeAccessors = { child: { configurable: true } };\n\n// DEPRECATED: alias for componentInstance for backwards compat.\n/* istanbul ignore next */\nprototypeAccessors.child.get = function () {\n  return this.componentInstance\n};\n\nObject.defineProperties( VNode.prototype, prototypeAccessors );\n\nvar createEmptyVNode = function (text) {\n  if ( text === void 0 ) text = '';\n\n  var node = new VNode();\n  node.text = text;\n  node.isComment = true;\n  return node\n};\n\nfunction createTextVNode (val) {\n  return new VNode(undefined, undefined, undefined, String(val))\n}\n\n// optimized shallow clone\n// used for static nodes and slot nodes because they may be reused across\n// multiple renders, cloning them avoids errors when DOM manipulations rely\n// on their elm reference.\nfunction cloneVNode (vnode) {\n  var cloned = new VNode(\n    vnode.tag,\n    vnode.data,\n    // #7975\n    // clone children array to avoid mutating original in case of cloning\n    // a child.\n    vnode.children && vnode.children.slice(),\n    vnode.text,\n    vnode.elm,\n    vnode.context,\n    vnode.componentOptions,\n    vnode.asyncFactory\n  );\n  cloned.ns = vnode.ns;\n  cloned.isStatic = vnode.isStatic;\n  cloned.key = vnode.key;\n  cloned.isComment = vnode.isComment;\n  cloned.fnContext = vnode.fnContext;\n  cloned.fnOptions = vnode.fnOptions;\n  cloned.fnScopeId = vnode.fnScopeId;\n  cloned.asyncMeta = vnode.asyncMeta;\n  cloned.isCloned = true;\n  return cloned\n}\n\n/*\n * not type checking this file because flow doesn't play well with\n * dynamically accessing methods on Array prototype\n */\n\nvar arrayProto = Array.prototype;\nvar arrayMethods = Object.create(arrayProto);\n\nvar methodsToPatch = [\n  'push',\n  'pop',\n  'shift',\n  'unshift',\n  'splice',\n  'sort',\n  'reverse'\n];\n\n/**\n * Intercept mutating methods and emit events\n */\nmethodsToPatch.forEach(function (method) {\n  // cache original method\n  var original = arrayProto[method];\n  def(arrayMethods, method, function mutator () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var result = original.apply(this, args);\n    var ob = this.__ob__;\n    var inserted;\n    switch (method) {\n      case 'push':\n      case 'unshift':\n        inserted = args;\n        break\n      case 'splice':\n        inserted = args.slice(2);\n        break\n    }\n    if (inserted) { ob.observeArray(inserted); }\n    // notify change\n    ob.dep.notify();\n    return result\n  });\n});\n\n/*  */\n\nvar arrayKeys = Object.getOwnPropertyNames(arrayMethods);\n\n/**\n * In some cases we may want to disable observation inside a component's\n * update computation.\n */\nvar shouldObserve = true;\n\nfunction toggleObserving (value) {\n  shouldObserve = value;\n}\n\n/**\n * Observer class that is attached to each observed\n * object. Once attached, the observer converts the target\n * object's property keys into getter/setters that\n * collect dependencies and dispatch updates.\n */\nvar Observer = function Observer (value) {\n  this.value = value;\n  this.dep = new Dep();\n  this.vmCount = 0;\n  def(value, '__ob__', this);\n  if (Array.isArray(value)) {\n    if (hasProto) {\n      {// fixed by xxxxxx 微信小程序使用 plugins 之后，数组方法被直接挂载到了数组对象上，需要执行 copyAugment 逻辑\n        if(value.push !== value.__proto__.push){\n          copyAugment(value, arrayMethods, arrayKeys);\n        } else {\n          protoAugment(value, arrayMethods);\n        }\n      }\n    } else {\n      copyAugment(value, arrayMethods, arrayKeys);\n    }\n    this.observeArray(value);\n  } else {\n    this.walk(value);\n  }\n};\n\n/**\n * Walk through all properties and convert them into\n * getter/setters. This method should only be called when\n * value type is Object.\n */\nObserver.prototype.walk = function walk (obj) {\n  var keys = Object.keys(obj);\n  for (var i = 0; i < keys.length; i++) {\n    defineReactive$$1(obj, keys[i]);\n  }\n};\n\n/**\n * Observe a list of Array items.\n */\nObserver.prototype.observeArray = function observeArray (items) {\n  for (var i = 0, l = items.length; i < l; i++) {\n    observe(items[i]);\n  }\n};\n\n// helpers\n\n/**\n * Augment a target Object or Array by intercepting\n * the prototype chain using __proto__\n */\nfunction protoAugment (target, src) {\n  /* eslint-disable no-proto */\n  target.__proto__ = src;\n  /* eslint-enable no-proto */\n}\n\n/**\n * Augment a target Object or Array by defining\n * hidden properties.\n */\n/* istanbul ignore next */\nfunction copyAugment (target, src, keys) {\n  for (var i = 0, l = keys.length; i < l; i++) {\n    var key = keys[i];\n    def(target, key, src[key]);\n  }\n}\n\n/**\n * Attempt to create an observer instance for a value,\n * returns the new observer if successfully observed,\n * or the existing observer if the value already has one.\n */\nfunction observe (value, asRootData) {\n  if (!isObject(value) || value instanceof VNode) {\n    return\n  }\n  var ob;\n  if (hasOwn(value, '__ob__') && value.__ob__ instanceof Observer) {\n    ob = value.__ob__;\n  } else if (\n    shouldObserve &&\n    !isServerRendering() &&\n    (Array.isArray(value) || isPlainObject(value)) &&\n    Object.isExtensible(value) &&\n    !value._isVue &&\n    !value.__v_isMPComponent\n  ) {\n    ob = new Observer(value);\n  }\n  if (asRootData && ob) {\n    ob.vmCount++;\n  }\n  return ob\n}\n\n/**\n * Define a reactive property on an Object.\n */\nfunction defineReactive$$1 (\n  obj,\n  key,\n  val,\n  customSetter,\n  shallow\n) {\n  var dep = new Dep();\n\n  var property = Object.getOwnPropertyDescriptor(obj, key);\n  if (property && property.configurable === false) {\n    return\n  }\n\n  // cater for pre-defined getter/setters\n  var getter = property && property.get;\n  var setter = property && property.set;\n  if ((!getter || setter) && arguments.length === 2) {\n    val = obj[key];\n  }\n\n  var childOb = !shallow && observe(val);\n  Object.defineProperty(obj, key, {\n    enumerable: true,\n    configurable: true,\n    get: function reactiveGetter () {\n      var value = getter ? getter.call(obj) : val;\n      if (Dep.SharedObject.target) { // fixed by xxxxxx\n        dep.depend();\n        if (childOb) {\n          childOb.dep.depend();\n          if (Array.isArray(value)) {\n            dependArray(value);\n          }\n        }\n      }\n      return value\n    },\n    set: function reactiveSetter (newVal) {\n      var value = getter ? getter.call(obj) : val;\n      /* eslint-disable no-self-compare */\n      if (newVal === value || (newVal !== newVal && value !== value)) {\n        return\n      }\n      /* eslint-enable no-self-compare */\n      if (process.env.NODE_ENV !== 'production' && customSetter) {\n        customSetter();\n      }\n      // #7981: for accessor properties without setter\n      if (getter && !setter) { return }\n      if (setter) {\n        setter.call(obj, newVal);\n      } else {\n        val = newVal;\n      }\n      childOb = !shallow && observe(newVal);\n      dep.notify();\n    }\n  });\n}\n\n/**\n * Set a property on an object. Adds the new property and\n * triggers change notification if the property doesn't\n * already exist.\n */\nfunction set (target, key, val) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot set reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val\n  }\n  if (key in target && !(key in Object.prototype)) {\n    target[key] = val;\n    return val\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid adding reactive properties to a Vue instance or its root $data ' +\n      'at runtime - declare it upfront in the data option.'\n    );\n    return val\n  }\n  if (!ob) {\n    target[key] = val;\n    return val\n  }\n  defineReactive$$1(ob.value, key, val);\n  ob.dep.notify();\n  return val\n}\n\n/**\n * Delete a property and trigger change if necessary.\n */\nfunction del (target, key) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot delete reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.splice(key, 1);\n    return\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid deleting properties on a Vue instance or its root $data ' +\n      '- just set it to null.'\n    );\n    return\n  }\n  if (!hasOwn(target, key)) {\n    return\n  }\n  delete target[key];\n  if (!ob) {\n    return\n  }\n  ob.dep.notify();\n}\n\n/**\n * Collect dependencies on array elements when the array is touched, since\n * we cannot intercept array element access like property getters.\n */\nfunction dependArray (value) {\n  for (var e = (void 0), i = 0, l = value.length; i < l; i++) {\n    e = value[i];\n    e && e.__ob__ && e.__ob__.dep.depend();\n    if (Array.isArray(e)) {\n      dependArray(e);\n    }\n  }\n}\n\n/*  */\n\n/**\n * Option overwriting strategies are functions that handle\n * how to merge a parent option value and a child option\n * value into the final value.\n */\nvar strats = config.optionMergeStrategies;\n\n/**\n * Options with restrictions\n */\nif (process.env.NODE_ENV !== 'production') {\n  strats.el = strats.propsData = function (parent, child, vm, key) {\n    if (!vm) {\n      warn(\n        \"option \\\"\" + key + \"\\\" can only be used during instance \" +\n        'creation with the `new` keyword.'\n      );\n    }\n    return defaultStrat(parent, child)\n  };\n}\n\n/**\n * Helper that recursively merges two data objects together.\n */\nfunction mergeData (to, from) {\n  if (!from) { return to }\n  var key, toVal, fromVal;\n\n  var keys = hasSymbol\n    ? Reflect.ownKeys(from)\n    : Object.keys(from);\n\n  for (var i = 0; i < keys.length; i++) {\n    key = keys[i];\n    // in case the object is already observed...\n    if (key === '__ob__') { continue }\n    toVal = to[key];\n    fromVal = from[key];\n    if (!hasOwn(to, key)) {\n      set(to, key, fromVal);\n    } else if (\n      toVal !== fromVal &&\n      isPlainObject(toVal) &&\n      isPlainObject(fromVal)\n    ) {\n      mergeData(toVal, fromVal);\n    }\n  }\n  return to\n}\n\n/**\n * Data\n */\nfunction mergeDataOrFn (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    // in a Vue.extend merge, both should be functions\n    if (!childVal) {\n      return parentVal\n    }\n    if (!parentVal) {\n      return childVal\n    }\n    // when parentVal & childVal are both present,\n    // we need to return a function that returns the\n    // merged result of both functions... no need to\n    // check if parentVal is a function here because\n    // it has to be a function to pass previous merges.\n    return function mergedDataFn () {\n      return mergeData(\n        typeof childVal === 'function' ? childVal.call(this, this) : childVal,\n        typeof parentVal === 'function' ? parentVal.call(this, this) : parentVal\n      )\n    }\n  } else {\n    return function mergedInstanceDataFn () {\n      // instance merge\n      var instanceData = typeof childVal === 'function'\n        ? childVal.call(vm, vm)\n        : childVal;\n      var defaultData = typeof parentVal === 'function'\n        ? parentVal.call(vm, vm)\n        : parentVal;\n      if (instanceData) {\n        return mergeData(instanceData, defaultData)\n      } else {\n        return defaultData\n      }\n    }\n  }\n}\n\nstrats.data = function (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    if (childVal && typeof childVal !== 'function') {\n      process.env.NODE_ENV !== 'production' && warn(\n        'The \"data\" option should be a function ' +\n        'that returns a per-instance value in component ' +\n        'definitions.',\n        vm\n      );\n\n      return parentVal\n    }\n    return mergeDataOrFn(parentVal, childVal)\n  }\n\n  return mergeDataOrFn(parentVal, childVal, vm)\n};\n\n/**\n * Hooks and props are merged as arrays.\n */\nfunction mergeHook (\n  parentVal,\n  childVal\n) {\n  var res = childVal\n    ? parentVal\n      ? parentVal.concat(childVal)\n      : Array.isArray(childVal)\n        ? childVal\n        : [childVal]\n    : parentVal;\n  return res\n    ? dedupeHooks(res)\n    : res\n}\n\nfunction dedupeHooks (hooks) {\n  var res = [];\n  for (var i = 0; i < hooks.length; i++) {\n    if (res.indexOf(hooks[i]) === -1) {\n      res.push(hooks[i]);\n    }\n  }\n  return res\n}\n\nLIFECYCLE_HOOKS.forEach(function (hook) {\n  strats[hook] = mergeHook;\n});\n\n/**\n * Assets\n *\n * When a vm is present (instance creation), we need to do\n * a three-way merge between constructor options, instance\n * options and parent options.\n */\nfunction mergeAssets (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  var res = Object.create(parentVal || null);\n  if (childVal) {\n    process.env.NODE_ENV !== 'production' && assertObjectType(key, childVal, vm);\n    return extend(res, childVal)\n  } else {\n    return res\n  }\n}\n\nASSET_TYPES.forEach(function (type) {\n  strats[type + 's'] = mergeAssets;\n});\n\n/**\n * Watchers.\n *\n * Watchers hashes should not overwrite one\n * another, so we merge them as arrays.\n */\nstrats.watch = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  // work around Firefox's Object.prototype.watch...\n  if (parentVal === nativeWatch) { parentVal = undefined; }\n  if (childVal === nativeWatch) { childVal = undefined; }\n  /* istanbul ignore if */\n  if (!childVal) { return Object.create(parentVal || null) }\n  if (process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = {};\n  extend(ret, parentVal);\n  for (var key$1 in childVal) {\n    var parent = ret[key$1];\n    var child = childVal[key$1];\n    if (parent && !Array.isArray(parent)) {\n      parent = [parent];\n    }\n    ret[key$1] = parent\n      ? parent.concat(child)\n      : Array.isArray(child) ? child : [child];\n  }\n  return ret\n};\n\n/**\n * Other object hashes.\n */\nstrats.props =\nstrats.methods =\nstrats.inject =\nstrats.computed = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  if (childVal && process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = Object.create(null);\n  extend(ret, parentVal);\n  if (childVal) { extend(ret, childVal); }\n  return ret\n};\nstrats.provide = mergeDataOrFn;\n\n/**\n * Default strategy.\n */\nvar defaultStrat = function (parentVal, childVal) {\n  return childVal === undefined\n    ? parentVal\n    : childVal\n};\n\n/**\n * Validate component names\n */\nfunction checkComponents (options) {\n  for (var key in options.components) {\n    validateComponentName(key);\n  }\n}\n\nfunction validateComponentName (name) {\n  if (!new RegExp((\"^[a-zA-Z][\\\\-\\\\.0-9_\" + (unicodeRegExp.source) + \"]*$\")).test(name)) {\n    warn(\n      'Invalid component name: \"' + name + '\". Component names ' +\n      'should conform to valid custom element name in html5 specification.'\n    );\n  }\n  if (isBuiltInTag(name) || config.isReservedTag(name)) {\n    warn(\n      'Do not use built-in or reserved HTML elements as component ' +\n      'id: ' + name\n    );\n  }\n}\n\n/**\n * Ensure all props option syntax are normalized into the\n * Object-based format.\n */\nfunction normalizeProps (options, vm) {\n  var props = options.props;\n  if (!props) { return }\n  var res = {};\n  var i, val, name;\n  if (Array.isArray(props)) {\n    i = props.length;\n    while (i--) {\n      val = props[i];\n      if (typeof val === 'string') {\n        name = camelize(val);\n        res[name] = { type: null };\n      } else if (process.env.NODE_ENV !== 'production') {\n        warn('props must be strings when using array syntax.');\n      }\n    }\n  } else if (isPlainObject(props)) {\n    for (var key in props) {\n      val = props[key];\n      name = camelize(key);\n      res[name] = isPlainObject(val)\n        ? val\n        : { type: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"props\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(props)) + \".\",\n      vm\n    );\n  }\n  options.props = res;\n}\n\n/**\n * Normalize all injections into Object-based format\n */\nfunction normalizeInject (options, vm) {\n  var inject = options.inject;\n  if (!inject) { return }\n  var normalized = options.inject = {};\n  if (Array.isArray(inject)) {\n    for (var i = 0; i < inject.length; i++) {\n      normalized[inject[i]] = { from: inject[i] };\n    }\n  } else if (isPlainObject(inject)) {\n    for (var key in inject) {\n      var val = inject[key];\n      normalized[key] = isPlainObject(val)\n        ? extend({ from: key }, val)\n        : { from: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"inject\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(inject)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Normalize raw function directives into object format.\n */\nfunction normalizeDirectives (options) {\n  var dirs = options.directives;\n  if (dirs) {\n    for (var key in dirs) {\n      var def$$1 = dirs[key];\n      if (typeof def$$1 === 'function') {\n        dirs[key] = { bind: def$$1, update: def$$1 };\n      }\n    }\n  }\n}\n\nfunction assertObjectType (name, value, vm) {\n  if (!isPlainObject(value)) {\n    warn(\n      \"Invalid value for option \\\"\" + name + \"\\\": expected an Object, \" +\n      \"but got \" + (toRawType(value)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Merge two option objects into a new one.\n * Core utility used in both instantiation and inheritance.\n */\nfunction mergeOptions (\n  parent,\n  child,\n  vm\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    checkComponents(child);\n  }\n\n  if (typeof child === 'function') {\n    child = child.options;\n  }\n\n  normalizeProps(child, vm);\n  normalizeInject(child, vm);\n  normalizeDirectives(child);\n\n  // Apply extends and mixins on the child options,\n  // but only if it is a raw options object that isn't\n  // the result of another mergeOptions call.\n  // Only merged options has the _base property.\n  if (!child._base) {\n    if (child.extends) {\n      parent = mergeOptions(parent, child.extends, vm);\n    }\n    if (child.mixins) {\n      for (var i = 0, l = child.mixins.length; i < l; i++) {\n        parent = mergeOptions(parent, child.mixins[i], vm);\n      }\n    }\n  }\n\n  var options = {};\n  var key;\n  for (key in parent) {\n    mergeField(key);\n  }\n  for (key in child) {\n    if (!hasOwn(parent, key)) {\n      mergeField(key);\n    }\n  }\n  function mergeField (key) {\n    var strat = strats[key] || defaultStrat;\n    options[key] = strat(parent[key], child[key], vm, key);\n  }\n  return options\n}\n\n/**\n * Resolve an asset.\n * This function is used because child instances need access\n * to assets defined in its ancestor chain.\n */\nfunction resolveAsset (\n  options,\n  type,\n  id,\n  warnMissing\n) {\n  /* istanbul ignore if */\n  if (typeof id !== 'string') {\n    return\n  }\n  var assets = options[type];\n  // check local registration variations first\n  if (hasOwn(assets, id)) { return assets[id] }\n  var camelizedId = camelize(id);\n  if (hasOwn(assets, camelizedId)) { return assets[camelizedId] }\n  var PascalCaseId = capitalize(camelizedId);\n  if (hasOwn(assets, PascalCaseId)) { return assets[PascalCaseId] }\n  // fallback to prototype chain\n  var res = assets[id] || assets[camelizedId] || assets[PascalCaseId];\n  if (process.env.NODE_ENV !== 'production' && warnMissing && !res) {\n    warn(\n      'Failed to resolve ' + type.slice(0, -1) + ': ' + id,\n      options\n    );\n  }\n  return res\n}\n\n/*  */\n\n\n\nfunction validateProp (\n  key,\n  propOptions,\n  propsData,\n  vm\n) {\n  var prop = propOptions[key];\n  var absent = !hasOwn(propsData, key);\n  var value = propsData[key];\n  // boolean casting\n  var booleanIndex = getTypeIndex(Boolean, prop.type);\n  if (booleanIndex > -1) {\n    if (absent && !hasOwn(prop, 'default')) {\n      value = false;\n    } else if (value === '' || value === hyphenate(key)) {\n      // only cast empty string / same name to boolean if\n      // boolean has higher priority\n      var stringIndex = getTypeIndex(String, prop.type);\n      if (stringIndex < 0 || booleanIndex < stringIndex) {\n        value = true;\n      }\n    }\n  }\n  // check default value\n  if (value === undefined) {\n    value = getPropDefaultValue(vm, prop, key);\n    // since the default value is a fresh copy,\n    // make sure to observe it.\n    var prevShouldObserve = shouldObserve;\n    toggleObserving(true);\n    observe(value);\n    toggleObserving(prevShouldObserve);\n  }\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    // skip validation for weex recycle-list child component props\n    !(false)\n  ) {\n    assertProp(prop, key, value, vm, absent);\n  }\n  return value\n}\n\n/**\n * Get the default value of a prop.\n */\nfunction getPropDefaultValue (vm, prop, key) {\n  // no default, return undefined\n  if (!hasOwn(prop, 'default')) {\n    return undefined\n  }\n  var def = prop.default;\n  // warn against non-factory defaults for Object & Array\n  if (process.env.NODE_ENV !== 'production' && isObject(def)) {\n    warn(\n      'Invalid default value for prop \"' + key + '\": ' +\n      'Props with type Object/Array must use a factory function ' +\n      'to return the default value.',\n      vm\n    );\n  }\n  // the raw prop value was also undefined from previous render,\n  // return previous default value to avoid unnecessary watcher trigger\n  if (vm && vm.$options.propsData &&\n    vm.$options.propsData[key] === undefined &&\n    vm._props[key] !== undefined\n  ) {\n    return vm._props[key]\n  }\n  // call factory function for non-Function types\n  // a value is Function if its prototype is function even across different execution context\n  return typeof def === 'function' && getType(prop.type) !== 'Function'\n    ? def.call(vm)\n    : def\n}\n\n/**\n * Assert whether a prop is valid.\n */\nfunction assertProp (\n  prop,\n  name,\n  value,\n  vm,\n  absent\n) {\n  if (prop.required && absent) {\n    warn(\n      'Missing required prop: \"' + name + '\"',\n      vm\n    );\n    return\n  }\n  if (value == null && !prop.required) {\n    return\n  }\n  var type = prop.type;\n  var valid = !type || type === true;\n  var expectedTypes = [];\n  if (type) {\n    if (!Array.isArray(type)) {\n      type = [type];\n    }\n    for (var i = 0; i < type.length && !valid; i++) {\n      var assertedType = assertType(value, type[i]);\n      expectedTypes.push(assertedType.expectedType || '');\n      valid = assertedType.valid;\n    }\n  }\n\n  if (!valid) {\n    warn(\n      getInvalidTypeMessage(name, value, expectedTypes),\n      vm\n    );\n    return\n  }\n  var validator = prop.validator;\n  if (validator) {\n    if (!validator(value)) {\n      warn(\n        'Invalid prop: custom validator check failed for prop \"' + name + '\".',\n        vm\n      );\n    }\n  }\n}\n\nvar simpleCheckRE = /^(String|Number|Boolean|Function|Symbol)$/;\n\nfunction assertType (value, type) {\n  var valid;\n  var expectedType = getType(type);\n  if (simpleCheckRE.test(expectedType)) {\n    var t = typeof value;\n    valid = t === expectedType.toLowerCase();\n    // for primitive wrapper objects\n    if (!valid && t === 'object') {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === 'Object') {\n    valid = isPlainObject(value);\n  } else if (expectedType === 'Array') {\n    valid = Array.isArray(value);\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid: valid,\n    expectedType: expectedType\n  }\n}\n\n/**\n * Use function string name to check built-in types,\n * because a simple equality check will fail when running\n * across different vms / iframes.\n */\nfunction getType (fn) {\n  var match = fn && fn.toString().match(/^\\s*function (\\w+)/);\n  return match ? match[1] : ''\n}\n\nfunction isSameType (a, b) {\n  return getType(a) === getType(b)\n}\n\nfunction getTypeIndex (type, expectedTypes) {\n  if (!Array.isArray(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1\n  }\n  for (var i = 0, len = expectedTypes.length; i < len; i++) {\n    if (isSameType(expectedTypes[i], type)) {\n      return i\n    }\n  }\n  return -1\n}\n\nfunction getInvalidTypeMessage (name, value, expectedTypes) {\n  var message = \"Invalid prop: type check failed for prop \\\"\" + name + \"\\\".\" +\n    \" Expected \" + (expectedTypes.map(capitalize).join(', '));\n  var expectedType = expectedTypes[0];\n  var receivedType = toRawType(value);\n  var expectedValue = styleValue(value, expectedType);\n  var receivedValue = styleValue(value, receivedType);\n  // check if we need to specify expected value\n  if (expectedTypes.length === 1 &&\n      isExplicable(expectedType) &&\n      !isBoolean(expectedType, receivedType)) {\n    message += \" with value \" + expectedValue;\n  }\n  message += \", got \" + receivedType + \" \";\n  // check if we need to specify received value\n  if (isExplicable(receivedType)) {\n    message += \"with value \" + receivedValue + \".\";\n  }\n  return message\n}\n\nfunction styleValue (value, type) {\n  if (type === 'String') {\n    return (\"\\\"\" + value + \"\\\"\")\n  } else if (type === 'Number') {\n    return (\"\" + (Number(value)))\n  } else {\n    return (\"\" + value)\n  }\n}\n\nfunction isExplicable (value) {\n  var explicitTypes = ['string', 'number', 'boolean'];\n  return explicitTypes.some(function (elem) { return value.toLowerCase() === elem; })\n}\n\nfunction isBoolean () {\n  var args = [], len = arguments.length;\n  while ( len-- ) args[ len ] = arguments[ len ];\n\n  return args.some(function (elem) { return elem.toLowerCase() === 'boolean'; })\n}\n\n/*  */\n\nfunction handleError (err, vm, info) {\n  // Deactivate deps tracking while processing error handler to avoid possible infinite rendering.\n  // See: https://github.com/vuejs/vuex/issues/1505\n  pushTarget();\n  try {\n    if (vm) {\n      var cur = vm;\n      while ((cur = cur.$parent)) {\n        var hooks = cur.$options.errorCaptured;\n        if (hooks) {\n          for (var i = 0; i < hooks.length; i++) {\n            try {\n              var capture = hooks[i].call(cur, err, vm, info) === false;\n              if (capture) { return }\n            } catch (e) {\n              globalHandleError(e, cur, 'errorCaptured hook');\n            }\n          }\n        }\n      }\n    }\n    globalHandleError(err, vm, info);\n  } finally {\n    popTarget();\n  }\n}\n\nfunction invokeWithErrorHandling (\n  handler,\n  context,\n  args,\n  vm,\n  info\n) {\n  var res;\n  try {\n    res = args ? handler.apply(context, args) : handler.call(context);\n    if (res && !res._isVue && isPromise(res) && !res._handled) {\n      res.catch(function (e) { return handleError(e, vm, info + \" (Promise/async)\"); });\n      // issue #9511\n      // avoid catch triggering multiple times when nested calls\n      res._handled = true;\n    }\n  } catch (e) {\n    handleError(e, vm, info);\n  }\n  return res\n}\n\nfunction globalHandleError (err, vm, info) {\n  if (config.errorHandler) {\n    try {\n      return config.errorHandler.call(null, err, vm, info)\n    } catch (e) {\n      // if the user intentionally throws the original error in the handler,\n      // do not log it twice\n      if (e !== err) {\n        logError(e, null, 'config.errorHandler');\n      }\n    }\n  }\n  logError(err, vm, info);\n}\n\nfunction logError (err, vm, info) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\n  }\n  /* istanbul ignore else */\n  if ((inBrowser || inWeex) && typeof console !== 'undefined') {\n    console.error(err);\n  } else {\n    throw err\n  }\n}\n\n/*  */\n\nvar callbacks = [];\nvar pending = false;\n\nfunction flushCallbacks () {\n  pending = false;\n  var copies = callbacks.slice(0);\n  callbacks.length = 0;\n  for (var i = 0; i < copies.length; i++) {\n    copies[i]();\n  }\n}\n\n// Here we have async deferring wrappers using microtasks.\n// In 2.5 we used (macro) tasks (in combination with microtasks).\n// However, it has subtle problems when state is changed right before repaint\n// (e.g. #6813, out-in transitions).\n// Also, using (macro) tasks in event handler would cause some weird behaviors\n// that cannot be circumvented (e.g. #7109, #7153, #7546, #7834, #8109).\n// So we now use microtasks everywhere, again.\n// A major drawback of this tradeoff is that there are some scenarios\n// where microtasks have too high a priority and fire in between supposedly\n// sequential events (e.g. #4521, #6690, which have workarounds)\n// or even between bubbling of the same event (#6566).\nvar timerFunc;\n\n// The nextTick behavior leverages the microtask queue, which can be accessed\n// via either native Promise.then or MutationObserver.\n// MutationObserver has wider support, however it is seriously bugged in\n// UIWebView in iOS >= 9.3.3 when triggered in touch event handlers. It\n// completely stops working after triggering a few times... so, if native\n// Promise is available, we will use it:\n/* istanbul ignore next, $flow-disable-line */\nif (typeof Promise !== 'undefined' && isNative(Promise)) {\n  var p = Promise.resolve();\n  timerFunc = function () {\n    p.then(flushCallbacks);\n    // In problematic UIWebViews, Promise.then doesn't completely break, but\n    // it can get stuck in a weird state where callbacks are pushed into the\n    // microtask queue but the queue isn't being flushed, until the browser\n    // needs to do some other work, e.g. handle a timer. Therefore we can\n    // \"force\" the microtask queue to be flushed by adding an empty timer.\n    if (isIOS) { setTimeout(noop); }\n  };\n} else if (!isIE && typeof MutationObserver !== 'undefined' && (\n  isNative(MutationObserver) ||\n  // PhantomJS and iOS 7.x\n  MutationObserver.toString() === '[object MutationObserverConstructor]'\n)) {\n  // Use MutationObserver where native Promise is not available,\n  // e.g. PhantomJS, iOS7, Android 4.4\n  // (#6466 MutationObserver is unreliable in IE11)\n  var counter = 1;\n  var observer = new MutationObserver(flushCallbacks);\n  var textNode = document.createTextNode(String(counter));\n  observer.observe(textNode, {\n    characterData: true\n  });\n  timerFunc = function () {\n    counter = (counter + 1) % 2;\n    textNode.data = String(counter);\n  };\n} else if (typeof setImmediate !== 'undefined' && isNative(setImmediate)) {\n  // Fallback to setImmediate.\n  // Technically it leverages the (macro) task queue,\n  // but it is still a better choice than setTimeout.\n  timerFunc = function () {\n    setImmediate(flushCallbacks);\n  };\n} else {\n  // Fallback to setTimeout.\n  timerFunc = function () {\n    setTimeout(flushCallbacks, 0);\n  };\n}\n\nfunction nextTick (cb, ctx) {\n  var _resolve;\n  callbacks.push(function () {\n    if (cb) {\n      try {\n        cb.call(ctx);\n      } catch (e) {\n        handleError(e, ctx, 'nextTick');\n      }\n    } else if (_resolve) {\n      _resolve(ctx);\n    }\n  });\n  if (!pending) {\n    pending = true;\n    timerFunc();\n  }\n  // $flow-disable-line\n  if (!cb && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve) {\n      _resolve = resolve;\n    })\n  }\n}\n\n/*  */\n\n/* not type checking this file because flow doesn't play well with Proxy */\n\nvar initProxy;\n\nif (process.env.NODE_ENV !== 'production') {\n  var allowedGlobals = makeMap(\n    'Infinity,undefined,NaN,isFinite,isNaN,' +\n    'parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,' +\n    'Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,' +\n    'require' // for Webpack/Browserify\n  );\n\n  var warnNonPresent = function (target, key) {\n    warn(\n      \"Property or method \\\"\" + key + \"\\\" is not defined on the instance but \" +\n      'referenced during render. Make sure that this property is reactive, ' +\n      'either in the data option, or for class-based components, by ' +\n      'initializing the property. ' +\n      'See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.',\n      target\n    );\n  };\n\n  var warnReservedPrefix = function (target, key) {\n    warn(\n      \"Property \\\"\" + key + \"\\\" must be accessed with \\\"$data.\" + key + \"\\\" because \" +\n      'properties starting with \"$\" or \"_\" are not proxied in the Vue instance to ' +\n      'prevent conflicts with Vue internals. ' +\n      'See: https://vuejs.org/v2/api/#data',\n      target\n    );\n  };\n\n  var hasProxy =\n    typeof Proxy !== 'undefined' && isNative(Proxy);\n\n  if (hasProxy) {\n    var isBuiltInModifier = makeMap('stop,prevent,self,ctrl,shift,alt,meta,exact');\n    config.keyCodes = new Proxy(config.keyCodes, {\n      set: function set (target, key, value) {\n        if (isBuiltInModifier(key)) {\n          warn((\"Avoid overwriting built-in modifier in config.keyCodes: .\" + key));\n          return false\n        } else {\n          target[key] = value;\n          return true\n        }\n      }\n    });\n  }\n\n  var hasHandler = {\n    has: function has (target, key) {\n      var has = key in target;\n      var isAllowed = allowedGlobals(key) ||\n        (typeof key === 'string' && key.charAt(0) === '_' && !(key in target.$data));\n      if (!has && !isAllowed) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return has || !isAllowed\n    }\n  };\n\n  var getHandler = {\n    get: function get (target, key) {\n      if (typeof key === 'string' && !(key in target)) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return target[key]\n    }\n  };\n\n  initProxy = function initProxy (vm) {\n    if (hasProxy) {\n      // determine which proxy handler to use\n      var options = vm.$options;\n      var handlers = options.render && options.render._withStripped\n        ? getHandler\n        : hasHandler;\n      vm._renderProxy = new Proxy(vm, handlers);\n    } else {\n      vm._renderProxy = vm;\n    }\n  };\n}\n\n/*  */\n\nvar seenObjects = new _Set();\n\n/**\n * Recursively traverse an object to evoke all converted\n * getters, so that every nested property inside the object\n * is collected as a \"deep\" dependency.\n */\nfunction traverse (val) {\n  _traverse(val, seenObjects);\n  seenObjects.clear();\n}\n\nfunction _traverse (val, seen) {\n  var i, keys;\n  var isA = Array.isArray(val);\n  if ((!isA && !isObject(val)) || Object.isFrozen(val) || val instanceof VNode) {\n    return\n  }\n  if (val.__ob__) {\n    var depId = val.__ob__.dep.id;\n    if (seen.has(depId)) {\n      return\n    }\n    seen.add(depId);\n  }\n  if (isA) {\n    i = val.length;\n    while (i--) { _traverse(val[i], seen); }\n  } else {\n    keys = Object.keys(val);\n    i = keys.length;\n    while (i--) { _traverse(val[keys[i]], seen); }\n  }\n}\n\nvar mark;\nvar measure;\n\nif (process.env.NODE_ENV !== 'production') {\n  var perf = inBrowser && window.performance;\n  /* istanbul ignore if */\n  if (\n    perf &&\n    perf.mark &&\n    perf.measure &&\n    perf.clearMarks &&\n    perf.clearMeasures\n  ) {\n    mark = function (tag) { return perf.mark(tag); };\n    measure = function (name, startTag, endTag) {\n      perf.measure(name, startTag, endTag);\n      perf.clearMarks(startTag);\n      perf.clearMarks(endTag);\n      // perf.clearMeasures(name)\n    };\n  }\n}\n\n/*  */\n\nvar normalizeEvent = cached(function (name) {\n  var passive = name.charAt(0) === '&';\n  name = passive ? name.slice(1) : name;\n  var once$$1 = name.charAt(0) === '~'; // Prefixed last, checked first\n  name = once$$1 ? name.slice(1) : name;\n  var capture = name.charAt(0) === '!';\n  name = capture ? name.slice(1) : name;\n  return {\n    name: name,\n    once: once$$1,\n    capture: capture,\n    passive: passive\n  }\n});\n\nfunction createFnInvoker (fns, vm) {\n  function invoker () {\n    var arguments$1 = arguments;\n\n    var fns = invoker.fns;\n    if (Array.isArray(fns)) {\n      var cloned = fns.slice();\n      for (var i = 0; i < cloned.length; i++) {\n        invokeWithErrorHandling(cloned[i], null, arguments$1, vm, \"v-on handler\");\n      }\n    } else {\n      // return handler return value for single handlers\n      return invokeWithErrorHandling(fns, null, arguments, vm, \"v-on handler\")\n    }\n  }\n  invoker.fns = fns;\n  return invoker\n}\n\nfunction updateListeners (\n  on,\n  oldOn,\n  add,\n  remove$$1,\n  createOnceHandler,\n  vm\n) {\n  var name, def$$1, cur, old, event;\n  for (name in on) {\n    def$$1 = cur = on[name];\n    old = oldOn[name];\n    event = normalizeEvent(name);\n    if (isUndef(cur)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Invalid handler for event \\\"\" + (event.name) + \"\\\": got \" + String(cur),\n        vm\n      );\n    } else if (isUndef(old)) {\n      if (isUndef(cur.fns)) {\n        cur = on[name] = createFnInvoker(cur, vm);\n      }\n      if (isTrue(event.once)) {\n        cur = on[name] = createOnceHandler(event.name, cur, event.capture);\n      }\n      add(event.name, cur, event.capture, event.passive, event.params);\n    } else if (cur !== old) {\n      old.fns = cur;\n      on[name] = old;\n    }\n  }\n  for (name in oldOn) {\n    if (isUndef(on[name])) {\n      event = normalizeEvent(name);\n      remove$$1(event.name, oldOn[name], event.capture);\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\n// fixed by xxxxxx (mp properties)\r\nfunction extractPropertiesFromVNodeData(data, Ctor, res, context) {\r\n  var propOptions = Ctor.options.mpOptions && Ctor.options.mpOptions.properties;\r\n  if (isUndef(propOptions)) {\r\n    return res\r\n  }\n  var externalClasses = Ctor.options.mpOptions.externalClasses || [];\r\n  var attrs = data.attrs;\n  var props = data.props;\r\n  if (isDef(attrs) || isDef(props)) {\r\n    for (var key in propOptions) {\r\n      var altKey = hyphenate(key);\n      var result = checkProp(res, props, key, altKey, true) ||\n          checkProp(res, attrs, key, altKey, false);\n      // externalClass\n      if (\n        result &&\n        res[key] &&\n        externalClasses.indexOf(altKey) !== -1 &&\n        context[camelize(res[key])]\n      ) {\n        // 赋值 externalClass 真正的值(模板里 externalClass 的值可能是字符串)\n        res[key] = context[camelize(res[key])];\n      }\r\n    }\r\n  }\r\n  return res\r\n}\n\nfunction extractPropsFromVNodeData (\n  data,\n  Ctor,\n  tag,\n  context// fixed by xxxxxx\n) {\n  // we are only extracting raw values here.\n  // validation and default values are handled in the child\n  // component itself.\n  var propOptions = Ctor.options.props;\n  if (isUndef(propOptions)) {\n    // fixed by xxxxxx\n    return extractPropertiesFromVNodeData(data, Ctor, {}, context)\n  }\n  var res = {};\n  var attrs = data.attrs;\n  var props = data.props;\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n      if (process.env.NODE_ENV !== 'production') {\n        var keyInLowerCase = key.toLowerCase();\n        if (\n          key !== keyInLowerCase &&\n          attrs && hasOwn(attrs, keyInLowerCase)\n        ) {\n          tip(\n            \"Prop \\\"\" + keyInLowerCase + \"\\\" is passed to component \" +\n            (formatComponentName(tag || Ctor)) + \", but the declared prop name is\" +\n            \" \\\"\" + key + \"\\\". \" +\n            \"Note that HTML attributes are case-insensitive and camelCased \" +\n            \"props need to use their kebab-case equivalents when using in-DOM \" +\n            \"templates. You should probably use \\\"\" + altKey + \"\\\" instead of \\\"\" + key + \"\\\".\"\n          );\n        }\n      }\n      checkProp(res, props, key, altKey, true) ||\n      checkProp(res, attrs, key, altKey, false);\n    }\n  }\n  // fixed by xxxxxx\n  return extractPropertiesFromVNodeData(data, Ctor, res, context)\n}\n\nfunction checkProp (\n  res,\n  hash,\n  key,\n  altKey,\n  preserve\n) {\n  if (isDef(hash)) {\n    if (hasOwn(hash, key)) {\n      res[key] = hash[key];\n      if (!preserve) {\n        delete hash[key];\n      }\n      return true\n    } else if (hasOwn(hash, altKey)) {\n      res[key] = hash[altKey];\n      if (!preserve) {\n        delete hash[altKey];\n      }\n      return true\n    }\n  }\n  return false\n}\n\n/*  */\n\n// The template compiler attempts to minimize the need for normalization by\n// statically analyzing the template at compile time.\n//\n// For plain HTML markup, normalization can be completely skipped because the\n// generated render function is guaranteed to return Array<VNode>. There are\n// two cases where extra normalization is needed:\n\n// 1. When the children contains components - because a functional component\n// may return an Array instead of a single root. In this case, just a simple\n// normalization is needed - if any child is an Array, we flatten the whole\n// thing with Array.prototype.concat. It is guaranteed to be only 1-level deep\n// because functional components already normalize their own children.\nfunction simpleNormalizeChildren (children) {\n  for (var i = 0; i < children.length; i++) {\n    if (Array.isArray(children[i])) {\n      return Array.prototype.concat.apply([], children)\n    }\n  }\n  return children\n}\n\n// 2. When the children contains constructs that always generated nested Arrays,\n// e.g. <template>, <slot>, v-for, or when the children is provided by user\n// with hand-written render functions / JSX. In such cases a full normalization\n// is needed to cater to all possible types of children values.\nfunction normalizeChildren (children) {\n  return isPrimitive(children)\n    ? [createTextVNode(children)]\n    : Array.isArray(children)\n      ? normalizeArrayChildren(children)\n      : undefined\n}\n\nfunction isTextNode (node) {\n  return isDef(node) && isDef(node.text) && isFalse(node.isComment)\n}\n\nfunction normalizeArrayChildren (children, nestedIndex) {\n  var res = [];\n  var i, c, lastIndex, last;\n  for (i = 0; i < children.length; i++) {\n    c = children[i];\n    if (isUndef(c) || typeof c === 'boolean') { continue }\n    lastIndex = res.length - 1;\n    last = res[lastIndex];\n    //  nested\n    if (Array.isArray(c)) {\n      if (c.length > 0) {\n        c = normalizeArrayChildren(c, ((nestedIndex || '') + \"_\" + i));\n        // merge adjacent text nodes\n        if (isTextNode(c[0]) && isTextNode(last)) {\n          res[lastIndex] = createTextVNode(last.text + (c[0]).text);\n          c.shift();\n        }\n        res.push.apply(res, c);\n      }\n    } else if (isPrimitive(c)) {\n      if (isTextNode(last)) {\n        // merge adjacent text nodes\n        // this is necessary for SSR hydration because text nodes are\n        // essentially merged when rendered to HTML strings\n        res[lastIndex] = createTextVNode(last.text + c);\n      } else if (c !== '') {\n        // convert primitive to vnode\n        res.push(createTextVNode(c));\n      }\n    } else {\n      if (isTextNode(c) && isTextNode(last)) {\n        // merge adjacent text nodes\n        res[lastIndex] = createTextVNode(last.text + c.text);\n      } else {\n        // default key for nested array children (likely generated by v-for)\n        if (isTrue(children._isVList) &&\n          isDef(c.tag) &&\n          isUndef(c.key) &&\n          isDef(nestedIndex)) {\n          c.key = \"__vlist\" + nestedIndex + \"_\" + i + \"__\";\n        }\n        res.push(c);\n      }\n    }\n  }\n  return res\n}\n\n/*  */\n\nfunction initProvide (vm) {\n  var provide = vm.$options.provide;\n  if (provide) {\n    vm._provided = typeof provide === 'function'\n      ? provide.call(vm)\n      : provide;\n  }\n}\n\nfunction initInjections (vm) {\n  var result = resolveInject(vm.$options.inject, vm);\n  if (result) {\n    toggleObserving(false);\n    Object.keys(result).forEach(function (key) {\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        defineReactive$$1(vm, key, result[key], function () {\n          warn(\n            \"Avoid mutating an injected value directly since the changes will be \" +\n            \"overwritten whenever the provided component re-renders. \" +\n            \"injection being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        });\n      } else {\n        defineReactive$$1(vm, key, result[key]);\n      }\n    });\n    toggleObserving(true);\n  }\n}\n\nfunction resolveInject (inject, vm) {\n  if (inject) {\n    // inject is :any because flow is not smart enough to figure out cached\n    var result = Object.create(null);\n    var keys = hasSymbol\n      ? Reflect.ownKeys(inject)\n      : Object.keys(inject);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      // #6574 in case the inject object is observed...\n      if (key === '__ob__') { continue }\n      var provideKey = inject[key].from;\n      var source = vm;\n      while (source) {\n        if (source._provided && hasOwn(source._provided, provideKey)) {\n          result[key] = source._provided[provideKey];\n          break\n        }\n        source = source.$parent;\n      }\n      if (!source) {\n        if ('default' in inject[key]) {\n          var provideDefault = inject[key].default;\n          result[key] = typeof provideDefault === 'function'\n            ? provideDefault.call(vm)\n            : provideDefault;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn((\"Injection \\\"\" + key + \"\\\" not found\"), vm);\n        }\n      }\n    }\n    return result\n  }\n}\n\n/*  */\n\n\n\n/**\n * Runtime helper for resolving raw children VNodes into a slot object.\n */\nfunction resolveSlots (\n  children,\n  context\n) {\n  if (!children || !children.length) {\n    return {}\n  }\n  var slots = {};\n  for (var i = 0, l = children.length; i < l; i++) {\n    var child = children[i];\n    var data = child.data;\n    // remove slot attribute if the node is resolved as a Vue slot node\n    if (data && data.attrs && data.attrs.slot) {\n      delete data.attrs.slot;\n    }\n    // named slots should only be respected if the vnode was rendered in the\n    // same context.\n    if ((child.context === context || child.fnContext === context) &&\n      data && data.slot != null\n    ) {\n      var name = data.slot;\n      var slot = (slots[name] || (slots[name] = []));\n      if (child.tag === 'template') {\n        slot.push.apply(slot, child.children || []);\n      } else {\n        slot.push(child);\n      }\n    } else {\n      // fixed by xxxxxx 临时 hack 掉 uni-app 中的异步 name slot page\n      if(child.asyncMeta && child.asyncMeta.data && child.asyncMeta.data.slot === 'page'){\n        (slots['page'] || (slots['page'] = [])).push(child);\n      }else{\n        (slots.default || (slots.default = [])).push(child);\n      }\n    }\n  }\n  // ignore slots that contains only whitespace\n  for (var name$1 in slots) {\n    if (slots[name$1].every(isWhitespace)) {\n      delete slots[name$1];\n    }\n  }\n  return slots\n}\n\nfunction isWhitespace (node) {\n  return (node.isComment && !node.asyncFactory) || node.text === ' '\n}\n\n/*  */\n\nfunction normalizeScopedSlots (\n  slots,\n  normalSlots,\n  prevSlots\n) {\n  var res;\n  var hasNormalSlots = Object.keys(normalSlots).length > 0;\n  var isStable = slots ? !!slots.$stable : !hasNormalSlots;\n  var key = slots && slots.$key;\n  if (!slots) {\n    res = {};\n  } else if (slots._normalized) {\n    // fast path 1: child component re-render only, parent did not change\n    return slots._normalized\n  } else if (\n    isStable &&\n    prevSlots &&\n    prevSlots !== emptyObject &&\n    key === prevSlots.$key &&\n    !hasNormalSlots &&\n    !prevSlots.$hasNormal\n  ) {\n    // fast path 2: stable scoped slots w/ no normal slots to proxy,\n    // only need to normalize once\n    return prevSlots\n  } else {\n    res = {};\n    for (var key$1 in slots) {\n      if (slots[key$1] && key$1[0] !== '$') {\n        res[key$1] = normalizeScopedSlot(normalSlots, key$1, slots[key$1]);\n      }\n    }\n  }\n  // expose normal slots on scopedSlots\n  for (var key$2 in normalSlots) {\n    if (!(key$2 in res)) {\n      res[key$2] = proxyNormalSlot(normalSlots, key$2);\n    }\n  }\n  // avoriaz seems to mock a non-extensible $scopedSlots object\n  // and when that is passed down this would cause an error\n  if (slots && Object.isExtensible(slots)) {\n    (slots)._normalized = res;\n  }\n  def(res, '$stable', isStable);\n  def(res, '$key', key);\n  def(res, '$hasNormal', hasNormalSlots);\n  return res\n}\n\nfunction normalizeScopedSlot(normalSlots, key, fn) {\n  var normalized = function () {\n    var res = arguments.length ? fn.apply(null, arguments) : fn({});\n    res = res && typeof res === 'object' && !Array.isArray(res)\n      ? [res] // single vnode\n      : normalizeChildren(res);\n    return res && (\n      res.length === 0 ||\n      (res.length === 1 && res[0].isComment) // #9658\n    ) ? undefined\n      : res\n  };\n  // this is a slot using the new v-slot syntax without scope. although it is\n  // compiled as a scoped slot, render fn users would expect it to be present\n  // on this.$slots because the usage is semantically a normal slot.\n  if (fn.proxy) {\n    Object.defineProperty(normalSlots, key, {\n      get: normalized,\n      enumerable: true,\n      configurable: true\n    });\n  }\n  return normalized\n}\n\nfunction proxyNormalSlot(slots, key) {\n  return function () { return slots[key]; }\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering v-for lists.\n */\nfunction renderList (\n  val,\n  render\n) {\n  var ret, i, l, keys, key;\n  if (Array.isArray(val) || typeof val === 'string') {\n    ret = new Array(val.length);\n    for (i = 0, l = val.length; i < l; i++) {\n      ret[i] = render(val[i], i, i, i); // fixed by xxxxxx\n    }\n  } else if (typeof val === 'number') {\n    ret = new Array(val);\n    for (i = 0; i < val; i++) {\n      ret[i] = render(i + 1, i, i, i); // fixed by xxxxxx\n    }\n  } else if (isObject(val)) {\n    if (hasSymbol && val[Symbol.iterator]) {\n      ret = [];\n      var iterator = val[Symbol.iterator]();\n      var result = iterator.next();\n      while (!result.done) {\n        ret.push(render(result.value, ret.length, i, i++)); // fixed by xxxxxx\n        result = iterator.next();\n      }\n    } else {\n      keys = Object.keys(val);\n      ret = new Array(keys.length);\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[i] = render(val[key], key, i, i); // fixed by xxxxxx\n      }\n    }\n  }\n  if (!isDef(ret)) {\n    ret = [];\n  }\n  (ret)._isVList = true;\n  return ret\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering <slot>\n */\nfunction renderSlot (\n  name,\n  fallback,\n  props,\n  bindObject\n) {\n  var scopedSlotFn = this.$scopedSlots[name];\n  var nodes;\n  if (scopedSlotFn) { // scoped slot\n    props = props || {};\n    if (bindObject) {\n      if (process.env.NODE_ENV !== 'production' && !isObject(bindObject)) {\n        warn(\n          'slot v-bind without argument expects an Object',\n          this\n        );\n      }\n      props = extend(extend({}, bindObject), props);\n    }\n    // fixed by xxxxxx app-plus scopedSlot\n    nodes = scopedSlotFn(props, this, props._i) || fallback;\n  } else {\n    nodes = this.$slots[name] || fallback;\n  }\n\n  var target = props && props.slot;\n  if (target) {\n    return this.$createElement('template', { slot: target }, nodes)\n  } else {\n    return nodes\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for resolving filters\n */\nfunction resolveFilter (id) {\n  return resolveAsset(this.$options, 'filters', id, true) || identity\n}\n\n/*  */\n\nfunction isKeyNotMatch (expect, actual) {\n  if (Array.isArray(expect)) {\n    return expect.indexOf(actual) === -1\n  } else {\n    return expect !== actual\n  }\n}\n\n/**\n * Runtime helper for checking keyCodes from config.\n * exposed as Vue.prototype._k\n * passing in eventKeyName as last argument separately for backwards compat\n */\nfunction checkKeyCodes (\n  eventKeyCode,\n  key,\n  builtInKeyCode,\n  eventKeyName,\n  builtInKeyName\n) {\n  var mappedKeyCode = config.keyCodes[key] || builtInKeyCode;\n  if (builtInKeyName && eventKeyName && !config.keyCodes[key]) {\n    return isKeyNotMatch(builtInKeyName, eventKeyName)\n  } else if (mappedKeyCode) {\n    return isKeyNotMatch(mappedKeyCode, eventKeyCode)\n  } else if (eventKeyName) {\n    return hyphenate(eventKeyName) !== key\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for merging v-bind=\"object\" into a VNode's data.\n */\nfunction bindObjectProps (\n  data,\n  tag,\n  value,\n  asProp,\n  isSync\n) {\n  if (value) {\n    if (!isObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-bind without argument expects an Object or Array value',\n        this\n      );\n    } else {\n      if (Array.isArray(value)) {\n        value = toObject(value);\n      }\n      var hash;\n      var loop = function ( key ) {\n        if (\n          key === 'class' ||\n          key === 'style' ||\n          isReservedAttribute(key)\n        ) {\n          hash = data;\n        } else {\n          var type = data.attrs && data.attrs.type;\n          hash = asProp || config.mustUseProp(tag, type, key)\n            ? data.domProps || (data.domProps = {})\n            : data.attrs || (data.attrs = {});\n        }\n        var camelizedKey = camelize(key);\n        var hyphenatedKey = hyphenate(key);\n        if (!(camelizedKey in hash) && !(hyphenatedKey in hash)) {\n          hash[key] = value[key];\n\n          if (isSync) {\n            var on = data.on || (data.on = {});\n            on[(\"update:\" + key)] = function ($event) {\n              value[key] = $event;\n            };\n          }\n        }\n      };\n\n      for (var key in value) loop( key );\n    }\n  }\n  return data\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering static trees.\n */\nfunction renderStatic (\n  index,\n  isInFor\n) {\n  var cached = this._staticTrees || (this._staticTrees = []);\n  var tree = cached[index];\n  // if has already-rendered static tree and not inside v-for,\n  // we can reuse the same tree.\n  if (tree && !isInFor) {\n    return tree\n  }\n  // otherwise, render a fresh tree.\n  tree = cached[index] = this.$options.staticRenderFns[index].call(\n    this._renderProxy,\n    null,\n    this // for render fns generated for functional component templates\n  );\n  markStatic(tree, (\"__static__\" + index), false);\n  return tree\n}\n\n/**\n * Runtime helper for v-once.\n * Effectively it means marking the node as static with a unique key.\n */\nfunction markOnce (\n  tree,\n  index,\n  key\n) {\n  markStatic(tree, (\"__once__\" + index + (key ? (\"_\" + key) : \"\")), true);\n  return tree\n}\n\nfunction markStatic (\n  tree,\n  key,\n  isOnce\n) {\n  if (Array.isArray(tree)) {\n    for (var i = 0; i < tree.length; i++) {\n      if (tree[i] && typeof tree[i] !== 'string') {\n        markStaticNode(tree[i], (key + \"_\" + i), isOnce);\n      }\n    }\n  } else {\n    markStaticNode(tree, key, isOnce);\n  }\n}\n\nfunction markStaticNode (node, key, isOnce) {\n  node.isStatic = true;\n  node.key = key;\n  node.isOnce = isOnce;\n}\n\n/*  */\n\nfunction bindObjectListeners (data, value) {\n  if (value) {\n    if (!isPlainObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-on without argument expects an Object value',\n        this\n      );\n    } else {\n      var on = data.on = data.on ? extend({}, data.on) : {};\n      for (var key in value) {\n        var existing = on[key];\n        var ours = value[key];\n        on[key] = existing ? [].concat(existing, ours) : ours;\n      }\n    }\n  }\n  return data\n}\n\n/*  */\n\nfunction resolveScopedSlots (\n  fns, // see flow/vnode\n  res,\n  // the following are added in 2.6\n  hasDynamicKeys,\n  contentHashKey\n) {\n  res = res || { $stable: !hasDynamicKeys };\n  for (var i = 0; i < fns.length; i++) {\n    var slot = fns[i];\n    if (Array.isArray(slot)) {\n      resolveScopedSlots(slot, res, hasDynamicKeys);\n    } else if (slot) {\n      // marker for reverse proxying v-slot without scope on this.$slots\n      if (slot.proxy) {\n        slot.fn.proxy = true;\n      }\n      res[slot.key] = slot.fn;\n    }\n  }\n  if (contentHashKey) {\n    (res).$key = contentHashKey;\n  }\n  return res\n}\n\n/*  */\n\nfunction bindDynamicKeys (baseObj, values) {\n  for (var i = 0; i < values.length; i += 2) {\n    var key = values[i];\n    if (typeof key === 'string' && key) {\n      baseObj[values[i]] = values[i + 1];\n    } else if (process.env.NODE_ENV !== 'production' && key !== '' && key !== null) {\n      // null is a special value for explicitly removing a binding\n      warn(\n        (\"Invalid value for dynamic directive argument (expected string or null): \" + key),\n        this\n      );\n    }\n  }\n  return baseObj\n}\n\n// helper to dynamically append modifier runtime markers to event names.\n// ensure only append when value is already string, otherwise it will be cast\n// to string and cause the type check to miss.\nfunction prependModifier (value, symbol) {\n  return typeof value === 'string' ? symbol + value : value\n}\n\n/*  */\n\nfunction installRenderHelpers (target) {\n  target._o = markOnce;\n  target._n = toNumber;\n  target._s = toString;\n  target._l = renderList;\n  target._t = renderSlot;\n  target._q = looseEqual;\n  target._i = looseIndexOf;\n  target._m = renderStatic;\n  target._f = resolveFilter;\n  target._k = checkKeyCodes;\n  target._b = bindObjectProps;\n  target._v = createTextVNode;\n  target._e = createEmptyVNode;\n  target._u = resolveScopedSlots;\n  target._g = bindObjectListeners;\n  target._d = bindDynamicKeys;\n  target._p = prependModifier;\n}\n\n/*  */\n\nfunction FunctionalRenderContext (\n  data,\n  props,\n  children,\n  parent,\n  Ctor\n) {\n  var this$1 = this;\n\n  var options = Ctor.options;\n  // ensure the createElement function in functional components\n  // gets a unique context - this is necessary for correct named slot check\n  var contextVm;\n  if (hasOwn(parent, '_uid')) {\n    contextVm = Object.create(parent);\n    // $flow-disable-line\n    contextVm._original = parent;\n  } else {\n    // the context vm passed in is a functional context as well.\n    // in this case we want to make sure we are able to get a hold to the\n    // real context instance.\n    contextVm = parent;\n    // $flow-disable-line\n    parent = parent._original;\n  }\n  var isCompiled = isTrue(options._compiled);\n  var needNormalization = !isCompiled;\n\n  this.data = data;\n  this.props = props;\n  this.children = children;\n  this.parent = parent;\n  this.listeners = data.on || emptyObject;\n  this.injections = resolveInject(options.inject, parent);\n  this.slots = function () {\n    if (!this$1.$slots) {\n      normalizeScopedSlots(\n        data.scopedSlots,\n        this$1.$slots = resolveSlots(children, parent)\n      );\n    }\n    return this$1.$slots\n  };\n\n  Object.defineProperty(this, 'scopedSlots', ({\n    enumerable: true,\n    get: function get () {\n      return normalizeScopedSlots(data.scopedSlots, this.slots())\n    }\n  }));\n\n  // support for compiled functional template\n  if (isCompiled) {\n    // exposing $options for renderStatic()\n    this.$options = options;\n    // pre-resolve slots for renderSlot()\n    this.$slots = this.slots();\n    this.$scopedSlots = normalizeScopedSlots(data.scopedSlots, this.$slots);\n  }\n\n  if (options._scopeId) {\n    this._c = function (a, b, c, d) {\n      var vnode = createElement(contextVm, a, b, c, d, needNormalization);\n      if (vnode && !Array.isArray(vnode)) {\n        vnode.fnScopeId = options._scopeId;\n        vnode.fnContext = parent;\n      }\n      return vnode\n    };\n  } else {\n    this._c = function (a, b, c, d) { return createElement(contextVm, a, b, c, d, needNormalization); };\n  }\n}\n\ninstallRenderHelpers(FunctionalRenderContext.prototype);\n\nfunction createFunctionalComponent (\n  Ctor,\n  propsData,\n  data,\n  contextVm,\n  children\n) {\n  var options = Ctor.options;\n  var props = {};\n  var propOptions = options.props;\n  if (isDef(propOptions)) {\n    for (var key in propOptions) {\n      props[key] = validateProp(key, propOptions, propsData || emptyObject);\n    }\n  } else {\n    if (isDef(data.attrs)) { mergeProps(props, data.attrs); }\n    if (isDef(data.props)) { mergeProps(props, data.props); }\n  }\n\n  var renderContext = new FunctionalRenderContext(\n    data,\n    props,\n    children,\n    contextVm,\n    Ctor\n  );\n\n  var vnode = options.render.call(null, renderContext._c, renderContext);\n\n  if (vnode instanceof VNode) {\n    return cloneAndMarkFunctionalResult(vnode, data, renderContext.parent, options, renderContext)\n  } else if (Array.isArray(vnode)) {\n    var vnodes = normalizeChildren(vnode) || [];\n    var res = new Array(vnodes.length);\n    for (var i = 0; i < vnodes.length; i++) {\n      res[i] = cloneAndMarkFunctionalResult(vnodes[i], data, renderContext.parent, options, renderContext);\n    }\n    return res\n  }\n}\n\nfunction cloneAndMarkFunctionalResult (vnode, data, contextVm, options, renderContext) {\n  // #7817 clone node before setting fnContext, otherwise if the node is reused\n  // (e.g. it was from a cached normal slot) the fnContext causes named slots\n  // that should not be matched to match.\n  var clone = cloneVNode(vnode);\n  clone.fnContext = contextVm;\n  clone.fnOptions = options;\n  if (process.env.NODE_ENV !== 'production') {\n    (clone.devtoolsMeta = clone.devtoolsMeta || {}).renderContext = renderContext;\n  }\n  if (data.slot) {\n    (clone.data || (clone.data = {})).slot = data.slot;\n  }\n  return clone\n}\n\nfunction mergeProps (to, from) {\n  for (var key in from) {\n    to[camelize(key)] = from[key];\n  }\n}\n\n/*  */\n\n/*  */\n\n/*  */\n\n/*  */\n\n// inline hooks to be invoked on component VNodes during patch\nvar componentVNodeHooks = {\n  init: function init (vnode, hydrating) {\n    if (\n      vnode.componentInstance &&\n      !vnode.componentInstance._isDestroyed &&\n      vnode.data.keepAlive\n    ) {\n      // kept-alive components, treat as a patch\n      var mountedNode = vnode; // work around flow\n      componentVNodeHooks.prepatch(mountedNode, mountedNode);\n    } else {\n      var child = vnode.componentInstance = createComponentInstanceForVnode(\n        vnode,\n        activeInstance\n      );\n      child.$mount(hydrating ? vnode.elm : undefined, hydrating);\n    }\n  },\n\n  prepatch: function prepatch (oldVnode, vnode) {\n    var options = vnode.componentOptions;\n    var child = vnode.componentInstance = oldVnode.componentInstance;\n    updateChildComponent(\n      child,\n      options.propsData, // updated props\n      options.listeners, // updated listeners\n      vnode, // new parent vnode\n      options.children // new children\n    );\n  },\n\n  insert: function insert (vnode) {\n    var context = vnode.context;\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isMounted) {\n      callHook(componentInstance, 'onServiceCreated');\n      callHook(componentInstance, 'onServiceAttached');\n      componentInstance._isMounted = true;\n      callHook(componentInstance, 'mounted');\n    }\n    if (vnode.data.keepAlive) {\n      if (context._isMounted) {\n        // vue-router#1212\n        // During updates, a kept-alive component's child components may\n        // change, so directly walking the tree here may call activated hooks\n        // on incorrect children. Instead we push them into a queue which will\n        // be processed after the whole patch process ended.\n        queueActivatedComponent(componentInstance);\n      } else {\n        activateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  },\n\n  destroy: function destroy (vnode) {\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isDestroyed) {\n      if (!vnode.data.keepAlive) {\n        componentInstance.$destroy();\n      } else {\n        deactivateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  }\n};\n\nvar hooksToMerge = Object.keys(componentVNodeHooks);\n\nfunction createComponent (\n  Ctor,\n  data,\n  context,\n  children,\n  tag\n) {\n  if (isUndef(Ctor)) {\n    return\n  }\n\n  var baseCtor = context.$options._base;\n\n  // plain options object: turn it into a constructor\n  if (isObject(Ctor)) {\n    Ctor = baseCtor.extend(Ctor);\n  }\n\n  // if at this stage it's not a constructor or an async component factory,\n  // reject.\n  if (typeof Ctor !== 'function') {\n    if (process.env.NODE_ENV !== 'production') {\n      warn((\"Invalid Component definition: \" + (String(Ctor))), context);\n    }\n    return\n  }\n\n  // async component\n  var asyncFactory;\n  if (isUndef(Ctor.cid)) {\n    asyncFactory = Ctor;\n    Ctor = resolveAsyncComponent(asyncFactory, baseCtor);\n    if (Ctor === undefined) {\n      // return a placeholder node for async component, which is rendered\n      // as a comment node but preserves all the raw information for the node.\n      // the information will be used for async server-rendering and hydration.\n      return createAsyncPlaceholder(\n        asyncFactory,\n        data,\n        context,\n        children,\n        tag\n      )\n    }\n  }\n\n  data = data || {};\n\n  // resolve constructor options in case global mixins are applied after\n  // component constructor creation\n  resolveConstructorOptions(Ctor);\n\n  // transform component v-model data into props & events\n  if (isDef(data.model)) {\n    transformModel(Ctor.options, data);\n  }\n\n  // extract props\n  var propsData = extractPropsFromVNodeData(data, Ctor, tag, context); // fixed by xxxxxx\n\n  // functional component\n  if (isTrue(Ctor.options.functional)) {\n    return createFunctionalComponent(Ctor, propsData, data, context, children)\n  }\n\n  // extract listeners, since these needs to be treated as\n  // child component listeners instead of DOM listeners\n  var listeners = data.on;\n  // replace with listeners with .native modifier\n  // so it gets processed during parent component patch.\n  data.on = data.nativeOn;\n\n  if (isTrue(Ctor.options.abstract)) {\n    // abstract components do not keep anything\n    // other than props & listeners & slot\n\n    // work around flow\n    var slot = data.slot;\n    data = {};\n    if (slot) {\n      data.slot = slot;\n    }\n  }\n\n  // install component management hooks onto the placeholder node\n  installComponentHooks(data);\n\n  // return a placeholder vnode\n  var name = Ctor.options.name || tag;\n  var vnode = new VNode(\n    (\"vue-component-\" + (Ctor.cid) + (name ? (\"-\" + name) : '')),\n    data, undefined, undefined, undefined, context,\n    { Ctor: Ctor, propsData: propsData, listeners: listeners, tag: tag, children: children },\n    asyncFactory\n  );\n\n  return vnode\n}\n\nfunction createComponentInstanceForVnode (\n  vnode, // we know it's MountedComponentVNode but flow doesn't\n  parent // activeInstance in lifecycle state\n) {\n  var options = {\n    _isComponent: true,\n    _parentVnode: vnode,\n    parent: parent\n  };\n  // check inline-template render functions\n  var inlineTemplate = vnode.data.inlineTemplate;\n  if (isDef(inlineTemplate)) {\n    options.render = inlineTemplate.render;\n    options.staticRenderFns = inlineTemplate.staticRenderFns;\n  }\n  return new vnode.componentOptions.Ctor(options)\n}\n\nfunction installComponentHooks (data) {\n  var hooks = data.hook || (data.hook = {});\n  for (var i = 0; i < hooksToMerge.length; i++) {\n    var key = hooksToMerge[i];\n    var existing = hooks[key];\n    var toMerge = componentVNodeHooks[key];\n    if (existing !== toMerge && !(existing && existing._merged)) {\n      hooks[key] = existing ? mergeHook$1(toMerge, existing) : toMerge;\n    }\n  }\n}\n\nfunction mergeHook$1 (f1, f2) {\n  var merged = function (a, b) {\n    // flow complains about extra args which is why we use any\n    f1(a, b);\n    f2(a, b);\n  };\n  merged._merged = true;\n  return merged\n}\n\n// transform component v-model info (value and callback) into\n// prop and event handler respectively.\nfunction transformModel (options, data) {\n  var prop = (options.model && options.model.prop) || 'value';\n  var event = (options.model && options.model.event) || 'input'\n  ;(data.attrs || (data.attrs = {}))[prop] = data.model.value;\n  var on = data.on || (data.on = {});\n  var existing = on[event];\n  var callback = data.model.callback;\n  if (isDef(existing)) {\n    if (\n      Array.isArray(existing)\n        ? existing.indexOf(callback) === -1\n        : existing !== callback\n    ) {\n      on[event] = [callback].concat(existing);\n    }\n  } else {\n    on[event] = callback;\n  }\n}\n\n/*  */\n\nvar SIMPLE_NORMALIZE = 1;\nvar ALWAYS_NORMALIZE = 2;\n\n// wrapper function for providing a more flexible interface\n// without getting yelled at by flow\nfunction createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType,\n  alwaysNormalize\n) {\n  if (Array.isArray(data) || isPrimitive(data)) {\n    normalizationType = children;\n    children = data;\n    data = undefined;\n  }\n  if (isTrue(alwaysNormalize)) {\n    normalizationType = ALWAYS_NORMALIZE;\n  }\n  return _createElement(context, tag, data, children, normalizationType)\n}\n\nfunction _createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType\n) {\n  if (isDef(data) && isDef((data).__ob__)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      \"Avoid using observed data object as vnode data: \" + (JSON.stringify(data)) + \"\\n\" +\n      'Always create fresh vnode data objects in each render!',\n      context\n    );\n    return createEmptyVNode()\n  }\n  // object syntax in v-bind\n  if (isDef(data) && isDef(data.is)) {\n    tag = data.is;\n  }\n  if (!tag) {\n    // in case of component :is set to falsy value\n    return createEmptyVNode()\n  }\n  // warn against non-primitive key\n  if (process.env.NODE_ENV !== 'production' &&\n    isDef(data) && isDef(data.key) && !isPrimitive(data.key)\n  ) {\n    {\n      warn(\n        'Avoid using non-primitive value as key, ' +\n        'use string/number value instead.',\n        context\n      );\n    }\n  }\n  // support single function children as default scoped slot\n  if (Array.isArray(children) &&\n    typeof children[0] === 'function'\n  ) {\n    data = data || {};\n    data.scopedSlots = { default: children[0] };\n    children.length = 0;\n  }\n  if (normalizationType === ALWAYS_NORMALIZE) {\n    children = normalizeChildren(children);\n  } else if (normalizationType === SIMPLE_NORMALIZE) {\n    children = simpleNormalizeChildren(children);\n  }\n  var vnode, ns;\n  if (typeof tag === 'string') {\n    var Ctor;\n    ns = (context.$vnode && context.$vnode.ns) || config.getTagNamespace(tag);\n    if (config.isReservedTag(tag)) {\n      // platform built-in elements\n      if (process.env.NODE_ENV !== 'production' && isDef(data) && isDef(data.nativeOn)) {\n        warn(\n          (\"The .native modifier for v-on is only valid on components but it was used on <\" + tag + \">.\"),\n          context\n        );\n      }\n      vnode = new VNode(\n        config.parsePlatformTagName(tag), data, children,\n        undefined, undefined, context\n      );\n    } else if ((!data || !data.pre) && isDef(Ctor = resolveAsset(context.$options, 'components', tag))) {\n      // component\n      vnode = createComponent(Ctor, data, context, children, tag);\n    } else {\n      // unknown or unlisted namespaced elements\n      // check at runtime because it may get assigned a namespace when its\n      // parent normalizes children\n      vnode = new VNode(\n        tag, data, children,\n        undefined, undefined, context\n      );\n    }\n  } else {\n    // direct component options / constructor\n    vnode = createComponent(tag, data, context, children);\n  }\n  if (Array.isArray(vnode)) {\n    return vnode\n  } else if (isDef(vnode)) {\n    if (isDef(ns)) { applyNS(vnode, ns); }\n    if (isDef(data)) { registerDeepBindings(data); }\n    return vnode\n  } else {\n    return createEmptyVNode()\n  }\n}\n\nfunction applyNS (vnode, ns, force) {\n  vnode.ns = ns;\n  if (vnode.tag === 'foreignObject') {\n    // use default namespace inside foreignObject\n    ns = undefined;\n    force = true;\n  }\n  if (isDef(vnode.children)) {\n    for (var i = 0, l = vnode.children.length; i < l; i++) {\n      var child = vnode.children[i];\n      if (isDef(child.tag) && (\n        isUndef(child.ns) || (isTrue(force) && child.tag !== 'svg'))) {\n        applyNS(child, ns, force);\n      }\n    }\n  }\n}\n\n// ref #5318\n// necessary to ensure parent re-render when deep bindings like :style and\n// :class are used on slot nodes\nfunction registerDeepBindings (data) {\n  if (isObject(data.style)) {\n    traverse(data.style);\n  }\n  if (isObject(data.class)) {\n    traverse(data.class);\n  }\n}\n\n/*  */\n\nfunction initRender (vm) {\n  vm._vnode = null; // the root of the child tree\n  vm._staticTrees = null; // v-once cached trees\n  var options = vm.$options;\n  var parentVnode = vm.$vnode = options._parentVnode; // the placeholder node in parent tree\n  var renderContext = parentVnode && parentVnode.context;\n  vm.$slots = resolveSlots(options._renderChildren, renderContext);\n  vm.$scopedSlots = emptyObject;\n  // bind the createElement fn to this instance\n  // so that we get proper render context inside it.\n  // args order: tag, data, children, normalizationType, alwaysNormalize\n  // internal version is used by render functions compiled from templates\n  vm._c = function (a, b, c, d) { return createElement(vm, a, b, c, d, false); };\n  // normalization is always applied for the public version, used in\n  // user-written render functions.\n  vm.$createElement = function (a, b, c, d) { return createElement(vm, a, b, c, d, true); };\n\n  // $attrs & $listeners are exposed for easier HOC creation.\n  // they need to be reactive so that HOCs using them are always updated\n  var parentData = parentVnode && parentVnode.data;\n\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$attrs is readonly.\", vm);\n    }, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$listeners is readonly.\", vm);\n    }, true);\n  } else {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, null, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, null, true);\n  }\n}\n\nvar currentRenderingInstance = null;\n\nfunction renderMixin (Vue) {\n  // install runtime convenience helpers\n  installRenderHelpers(Vue.prototype);\n\n  Vue.prototype.$nextTick = function (fn) {\n    return nextTick(fn, this)\n  };\n\n  Vue.prototype._render = function () {\n    var vm = this;\n    var ref = vm.$options;\n    var render = ref.render;\n    var _parentVnode = ref._parentVnode;\n\n    if (_parentVnode) {\n      vm.$scopedSlots = normalizeScopedSlots(\n        _parentVnode.data.scopedSlots,\n        vm.$slots,\n        vm.$scopedSlots\n      );\n    }\n\n    // set parent vnode. this allows render functions to have access\n    // to the data on the placeholder node.\n    vm.$vnode = _parentVnode;\n    // render self\n    var vnode;\n    try {\n      // There's no need to maintain a stack because all render fns are called\n      // separately from one another. Nested component's render fns are called\n      // when parent component is patched.\n      currentRenderingInstance = vm;\n      vnode = render.call(vm._renderProxy, vm.$createElement);\n    } catch (e) {\n      handleError(e, vm, \"render\");\n      // return error render result,\n      // or previous vnode to prevent render error causing blank component\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production' && vm.$options.renderError) {\n        try {\n          vnode = vm.$options.renderError.call(vm._renderProxy, vm.$createElement, e);\n        } catch (e) {\n          handleError(e, vm, \"renderError\");\n          vnode = vm._vnode;\n        }\n      } else {\n        vnode = vm._vnode;\n      }\n    } finally {\n      currentRenderingInstance = null;\n    }\n    // if the returned array contains only a single node, allow it\n    if (Array.isArray(vnode) && vnode.length === 1) {\n      vnode = vnode[0];\n    }\n    // return empty vnode in case the render function errored out\n    if (!(vnode instanceof VNode)) {\n      if (process.env.NODE_ENV !== 'production' && Array.isArray(vnode)) {\n        warn(\n          'Multiple root nodes returned from render function. Render function ' +\n          'should return a single root node.',\n          vm\n        );\n      }\n      vnode = createEmptyVNode();\n    }\n    // set parent\n    vnode.parent = _parentVnode;\n    return vnode\n  };\n}\n\n/*  */\n\nfunction ensureCtor (comp, base) {\n  if (\n    comp.__esModule ||\n    (hasSymbol && comp[Symbol.toStringTag] === 'Module')\n  ) {\n    comp = comp.default;\n  }\n  return isObject(comp)\n    ? base.extend(comp)\n    : comp\n}\n\nfunction createAsyncPlaceholder (\n  factory,\n  data,\n  context,\n  children,\n  tag\n) {\n  var node = createEmptyVNode();\n  node.asyncFactory = factory;\n  node.asyncMeta = { data: data, context: context, children: children, tag: tag };\n  return node\n}\n\nfunction resolveAsyncComponent (\n  factory,\n  baseCtor\n) {\n  if (isTrue(factory.error) && isDef(factory.errorComp)) {\n    return factory.errorComp\n  }\n\n  if (isDef(factory.resolved)) {\n    return factory.resolved\n  }\n\n  var owner = currentRenderingInstance;\n  if (owner && isDef(factory.owners) && factory.owners.indexOf(owner) === -1) {\n    // already pending\n    factory.owners.push(owner);\n  }\n\n  if (isTrue(factory.loading) && isDef(factory.loadingComp)) {\n    return factory.loadingComp\n  }\n\n  if (owner && !isDef(factory.owners)) {\n    var owners = factory.owners = [owner];\n    var sync = true;\n    var timerLoading = null;\n    var timerTimeout = null\n\n    ;(owner).$on('hook:destroyed', function () { return remove(owners, owner); });\n\n    var forceRender = function (renderCompleted) {\n      for (var i = 0, l = owners.length; i < l; i++) {\n        (owners[i]).$forceUpdate();\n      }\n\n      if (renderCompleted) {\n        owners.length = 0;\n        if (timerLoading !== null) {\n          clearTimeout(timerLoading);\n          timerLoading = null;\n        }\n        if (timerTimeout !== null) {\n          clearTimeout(timerTimeout);\n          timerTimeout = null;\n        }\n      }\n    };\n\n    var resolve = once(function (res) {\n      // cache resolved\n      factory.resolved = ensureCtor(res, baseCtor);\n      // invoke callbacks only if this is not a synchronous resolve\n      // (async resolves are shimmed as synchronous during SSR)\n      if (!sync) {\n        forceRender(true);\n      } else {\n        owners.length = 0;\n      }\n    });\n\n    var reject = once(function (reason) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed to resolve async component: \" + (String(factory)) +\n        (reason ? (\"\\nReason: \" + reason) : '')\n      );\n      if (isDef(factory.errorComp)) {\n        factory.error = true;\n        forceRender(true);\n      }\n    });\n\n    var res = factory(resolve, reject);\n\n    if (isObject(res)) {\n      if (isPromise(res)) {\n        // () => Promise\n        if (isUndef(factory.resolved)) {\n          res.then(resolve, reject);\n        }\n      } else if (isPromise(res.component)) {\n        res.component.then(resolve, reject);\n\n        if (isDef(res.error)) {\n          factory.errorComp = ensureCtor(res.error, baseCtor);\n        }\n\n        if (isDef(res.loading)) {\n          factory.loadingComp = ensureCtor(res.loading, baseCtor);\n          if (res.delay === 0) {\n            factory.loading = true;\n          } else {\n            timerLoading = setTimeout(function () {\n              timerLoading = null;\n              if (isUndef(factory.resolved) && isUndef(factory.error)) {\n                factory.loading = true;\n                forceRender(false);\n              }\n            }, res.delay || 200);\n          }\n        }\n\n        if (isDef(res.timeout)) {\n          timerTimeout = setTimeout(function () {\n            timerTimeout = null;\n            if (isUndef(factory.resolved)) {\n              reject(\n                process.env.NODE_ENV !== 'production'\n                  ? (\"timeout (\" + (res.timeout) + \"ms)\")\n                  : null\n              );\n            }\n          }, res.timeout);\n        }\n      }\n    }\n\n    sync = false;\n    // return in case resolved synchronously\n    return factory.loading\n      ? factory.loadingComp\n      : factory.resolved\n  }\n}\n\n/*  */\n\nfunction isAsyncPlaceholder (node) {\n  return node.isComment && node.asyncFactory\n}\n\n/*  */\n\nfunction getFirstComponentChild (children) {\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      var c = children[i];\n      if (isDef(c) && (isDef(c.componentOptions) || isAsyncPlaceholder(c))) {\n        return c\n      }\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\nfunction initEvents (vm) {\n  vm._events = Object.create(null);\n  vm._hasHookEvent = false;\n  // init parent attached events\n  var listeners = vm.$options._parentListeners;\n  if (listeners) {\n    updateComponentListeners(vm, listeners);\n  }\n}\n\nvar target;\n\nfunction add (event, fn) {\n  target.$on(event, fn);\n}\n\nfunction remove$1 (event, fn) {\n  target.$off(event, fn);\n}\n\nfunction createOnceHandler (event, fn) {\n  var _target = target;\n  return function onceHandler () {\n    var res = fn.apply(null, arguments);\n    if (res !== null) {\n      _target.$off(event, onceHandler);\n    }\n  }\n}\n\nfunction updateComponentListeners (\n  vm,\n  listeners,\n  oldListeners\n) {\n  target = vm;\n  updateListeners(listeners, oldListeners || {}, add, remove$1, createOnceHandler, vm);\n  target = undefined;\n}\n\nfunction eventsMixin (Vue) {\n  var hookRE = /^hook:/;\n  Vue.prototype.$on = function (event, fn) {\n    var vm = this;\n    if (Array.isArray(event)) {\n      for (var i = 0, l = event.length; i < l; i++) {\n        vm.$on(event[i], fn);\n      }\n    } else {\n      (vm._events[event] || (vm._events[event] = [])).push(fn);\n      // optimize hook:event cost by using a boolean flag marked at registration\n      // instead of a hash lookup\n      if (hookRE.test(event)) {\n        vm._hasHookEvent = true;\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$once = function (event, fn) {\n    var vm = this;\n    function on () {\n      vm.$off(event, on);\n      fn.apply(vm, arguments);\n    }\n    on.fn = fn;\n    vm.$on(event, on);\n    return vm\n  };\n\n  Vue.prototype.$off = function (event, fn) {\n    var vm = this;\n    // all\n    if (!arguments.length) {\n      vm._events = Object.create(null);\n      return vm\n    }\n    // array of events\n    if (Array.isArray(event)) {\n      for (var i$1 = 0, l = event.length; i$1 < l; i$1++) {\n        vm.$off(event[i$1], fn);\n      }\n      return vm\n    }\n    // specific event\n    var cbs = vm._events[event];\n    if (!cbs) {\n      return vm\n    }\n    if (!fn) {\n      vm._events[event] = null;\n      return vm\n    }\n    // specific handler\n    var cb;\n    var i = cbs.length;\n    while (i--) {\n      cb = cbs[i];\n      if (cb === fn || cb.fn === fn) {\n        cbs.splice(i, 1);\n        break\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$emit = function (event) {\n    var vm = this;\n    if (process.env.NODE_ENV !== 'production') {\n      var lowerCaseEvent = event.toLowerCase();\n      if (lowerCaseEvent !== event && vm._events[lowerCaseEvent]) {\n        tip(\n          \"Event \\\"\" + lowerCaseEvent + \"\\\" is emitted in component \" +\n          (formatComponentName(vm)) + \" but the handler is registered for \\\"\" + event + \"\\\". \" +\n          \"Note that HTML attributes are case-insensitive and you cannot use \" +\n          \"v-on to listen to camelCase events when using in-DOM templates. \" +\n          \"You should probably use \\\"\" + (hyphenate(event)) + \"\\\" instead of \\\"\" + event + \"\\\".\"\n        );\n      }\n    }\n    var cbs = vm._events[event];\n    if (cbs) {\n      cbs = cbs.length > 1 ? toArray(cbs) : cbs;\n      var args = toArray(arguments, 1);\n      var info = \"event handler for \\\"\" + event + \"\\\"\";\n      for (var i = 0, l = cbs.length; i < l; i++) {\n        invokeWithErrorHandling(cbs[i], vm, args, vm, info);\n      }\n    }\n    return vm\n  };\n}\n\n/*  */\n\nvar activeInstance = null;\nvar isUpdatingChildComponent = false;\n\nfunction setActiveInstance(vm) {\n  var prevActiveInstance = activeInstance;\n  activeInstance = vm;\n  return function () {\n    activeInstance = prevActiveInstance;\n  }\n}\n\nfunction initLifecycle (vm) {\n  var options = vm.$options;\n\n  // locate first non-abstract parent\n  var parent = options.parent;\n  if (parent && !options.abstract) {\n    while (parent.$options.abstract && parent.$parent) {\n      parent = parent.$parent;\n    }\n    parent.$children.push(vm);\n  }\n\n  vm.$parent = parent;\n  vm.$root = parent ? parent.$root : vm;\n\n  vm.$children = [];\n  vm.$refs = {};\n\n  vm._watcher = null;\n  vm._inactive = null;\n  vm._directInactive = false;\n  vm._isMounted = false;\n  vm._isDestroyed = false;\n  vm._isBeingDestroyed = false;\n}\n\nfunction lifecycleMixin (Vue) {\n  Vue.prototype._update = function (vnode, hydrating) {\n    var vm = this;\n    var prevEl = vm.$el;\n    var prevVnode = vm._vnode;\n    var restoreActiveInstance = setActiveInstance(vm);\n    vm._vnode = vnode;\n    // Vue.prototype.__patch__ is injected in entry points\n    // based on the rendering backend used.\n    if (!prevVnode) {\n      // initial render\n      vm.$el = vm.__patch__(vm.$el, vnode, hydrating, false /* removeOnly */);\n    } else {\n      // updates\n      vm.$el = vm.__patch__(prevVnode, vnode);\n    }\n    restoreActiveInstance();\n    // update __vue__ reference\n    if (prevEl) {\n      prevEl.__vue__ = null;\n    }\n    if (vm.$el) {\n      vm.$el.__vue__ = vm;\n    }\n    // if parent is an HOC, update its $el as well\n    if (vm.$vnode && vm.$parent && vm.$vnode === vm.$parent._vnode) {\n      vm.$parent.$el = vm.$el;\n    }\n    // updated hook is called by the scheduler to ensure that children are\n    // updated in a parent's updated hook.\n  };\n\n  Vue.prototype.$forceUpdate = function () {\n    var vm = this;\n    if (vm._watcher) {\n      vm._watcher.update();\n    }\n  };\n\n  Vue.prototype.$destroy = function () {\n    var vm = this;\n    if (vm._isBeingDestroyed) {\n      return\n    }\n    callHook(vm, 'beforeDestroy');\n    vm._isBeingDestroyed = true;\n    // remove self from parent\n    var parent = vm.$parent;\n    if (parent && !parent._isBeingDestroyed && !vm.$options.abstract) {\n      remove(parent.$children, vm);\n    }\n    // teardown watchers\n    if (vm._watcher) {\n      vm._watcher.teardown();\n    }\n    var i = vm._watchers.length;\n    while (i--) {\n      vm._watchers[i].teardown();\n    }\n    // remove reference from data ob\n    // frozen object may not have observer.\n    if (vm._data.__ob__) {\n      vm._data.__ob__.vmCount--;\n    }\n    // call the last hook...\n    vm._isDestroyed = true;\n    // invoke destroy hooks on current rendered tree\n    vm.__patch__(vm._vnode, null);\n    // fire destroyed hook\n    callHook(vm, 'destroyed');\n    // turn off all instance listeners.\n    vm.$off();\n    // remove __vue__ reference\n    if (vm.$el) {\n      vm.$el.__vue__ = null;\n    }\n    // release circular reference (#6759)\n    if (vm.$vnode) {\n      vm.$vnode.parent = null;\n    }\n  };\n}\n\nfunction updateChildComponent (\n  vm,\n  propsData,\n  listeners,\n  parentVnode,\n  renderChildren\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = true;\n  }\n\n  // determine whether component has slot children\n  // we need to do this before overwriting $options._renderChildren.\n\n  // check if there are dynamic scopedSlots (hand-written or compiled but with\n  // dynamic slot names). Static scoped slots compiled from template has the\n  // \"$stable\" marker.\n  var newScopedSlots = parentVnode.data.scopedSlots;\n  var oldScopedSlots = vm.$scopedSlots;\n  var hasDynamicScopedSlot = !!(\n    (newScopedSlots && !newScopedSlots.$stable) ||\n    (oldScopedSlots !== emptyObject && !oldScopedSlots.$stable) ||\n    (newScopedSlots && vm.$scopedSlots.$key !== newScopedSlots.$key)\n  );\n\n  // Any static slot children from the parent may have changed during parent's\n  // update. Dynamic scoped slots may also have changed. In such cases, a forced\n  // update is necessary to ensure correctness.\n  var needsForceUpdate = !!(\n    renderChildren ||               // has new static slots\n    vm.$options._renderChildren ||  // has old static slots\n    hasDynamicScopedSlot\n  );\n\n  vm.$options._parentVnode = parentVnode;\n  vm.$vnode = parentVnode; // update vm's placeholder node without re-render\n\n  if (vm._vnode) { // update child tree's parent\n    vm._vnode.parent = parentVnode;\n  }\n  vm.$options._renderChildren = renderChildren;\n\n  // update $attrs and $listeners hash\n  // these are also reactive so they may trigger child update if the child\n  // used them during render\n  vm.$attrs = parentVnode.data.attrs || emptyObject;\n  vm.$listeners = listeners || emptyObject;\n\n  // update props\n  if (propsData && vm.$options.props) {\n    toggleObserving(false);\n    var props = vm._props;\n    var propKeys = vm.$options._propKeys || [];\n    for (var i = 0; i < propKeys.length; i++) {\n      var key = propKeys[i];\n      var propOptions = vm.$options.props; // wtf flow?\n      props[key] = validateProp(key, propOptions, propsData, vm);\n    }\n    toggleObserving(true);\n    // keep a copy of raw propsData\n    vm.$options.propsData = propsData;\n  }\n  \n  // fixed by xxxxxx update properties(mp runtime)\n  vm._$updateProperties && vm._$updateProperties(vm);\n  \n  // update listeners\n  listeners = listeners || emptyObject;\n  var oldListeners = vm.$options._parentListeners;\n  vm.$options._parentListeners = listeners;\n  updateComponentListeners(vm, listeners, oldListeners);\n\n  // resolve slots + force update if has children\n  if (needsForceUpdate) {\n    vm.$slots = resolveSlots(renderChildren, parentVnode.context);\n    vm.$forceUpdate();\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = false;\n  }\n}\n\nfunction isInInactiveTree (vm) {\n  while (vm && (vm = vm.$parent)) {\n    if (vm._inactive) { return true }\n  }\n  return false\n}\n\nfunction activateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = false;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  } else if (vm._directInactive) {\n    return\n  }\n  if (vm._inactive || vm._inactive === null) {\n    vm._inactive = false;\n    for (var i = 0; i < vm.$children.length; i++) {\n      activateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'activated');\n  }\n}\n\nfunction deactivateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = true;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  }\n  if (!vm._inactive) {\n    vm._inactive = true;\n    for (var i = 0; i < vm.$children.length; i++) {\n      deactivateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'deactivated');\n  }\n}\n\nfunction callHook (vm, hook) {\n  // #7573 disable dep collection when invoking lifecycle hooks\n  pushTarget();\n  var handlers = vm.$options[hook];\n  var info = hook + \" hook\";\n  if (handlers) {\n    for (var i = 0, j = handlers.length; i < j; i++) {\n      invokeWithErrorHandling(handlers[i], vm, null, vm, info);\n    }\n  }\n  if (vm._hasHookEvent) {\n    vm.$emit('hook:' + hook);\n  }\n  popTarget();\n}\n\n/*  */\n\nvar MAX_UPDATE_COUNT = 100;\n\nvar queue = [];\nvar activatedChildren = [];\nvar has = {};\nvar circular = {};\nvar waiting = false;\nvar flushing = false;\nvar index = 0;\n\n/**\n * Reset the scheduler's state.\n */\nfunction resetSchedulerState () {\n  index = queue.length = activatedChildren.length = 0;\n  has = {};\n  if (process.env.NODE_ENV !== 'production') {\n    circular = {};\n  }\n  waiting = flushing = false;\n}\n\n// Async edge case #6566 requires saving the timestamp when event listeners are\n// attached. However, calling performance.now() has a perf overhead especially\n// if the page has thousands of event listeners. Instead, we take a timestamp\n// every time the scheduler flushes and use that for all event listeners\n// attached during that flush.\nvar currentFlushTimestamp = 0;\n\n// Async edge case fix requires storing an event listener's attach timestamp.\nvar getNow = Date.now;\n\n// Determine what event timestamp the browser is using. Annoyingly, the\n// timestamp can either be hi-res (relative to page load) or low-res\n// (relative to UNIX epoch), so in order to compare time we have to use the\n// same timestamp type when saving the flush timestamp.\n// All IE versions use low-res event timestamps, and have problematic clock\n// implementations (#9632)\nif (inBrowser && !isIE) {\n  var performance = window.performance;\n  if (\n    performance &&\n    typeof performance.now === 'function' &&\n    getNow() > document.createEvent('Event').timeStamp\n  ) {\n    // if the event timestamp, although evaluated AFTER the Date.now(), is\n    // smaller than it, it means the event is using a hi-res timestamp,\n    // and we need to use the hi-res version for event listener timestamps as\n    // well.\n    getNow = function () { return performance.now(); };\n  }\n}\n\n/**\n * Flush both queues and run the watchers.\n */\nfunction flushSchedulerQueue () {\n  currentFlushTimestamp = getNow();\n  flushing = true;\n  var watcher, id;\n\n  // Sort queue before flush.\n  // This ensures that:\n  // 1. Components are updated from parent to child. (because parent is always\n  //    created before the child)\n  // 2. A component's user watchers are run before its render watcher (because\n  //    user watchers are created before the render watcher)\n  // 3. If a component is destroyed during a parent component's watcher run,\n  //    its watchers can be skipped.\n  queue.sort(function (a, b) { return a.id - b.id; });\n\n  // do not cache length because more watchers might be pushed\n  // as we run existing watchers\n  for (index = 0; index < queue.length; index++) {\n    watcher = queue[index];\n    if (watcher.before) {\n      watcher.before();\n    }\n    id = watcher.id;\n    has[id] = null;\n    watcher.run();\n    // in dev build, check and stop circular updates.\n    if (process.env.NODE_ENV !== 'production' && has[id] != null) {\n      circular[id] = (circular[id] || 0) + 1;\n      if (circular[id] > MAX_UPDATE_COUNT) {\n        warn(\n          'You may have an infinite update loop ' + (\n            watcher.user\n              ? (\"in watcher with expression \\\"\" + (watcher.expression) + \"\\\"\")\n              : \"in a component render function.\"\n          ),\n          watcher.vm\n        );\n        break\n      }\n    }\n  }\n\n  // keep copies of post queues before resetting state\n  var activatedQueue = activatedChildren.slice();\n  var updatedQueue = queue.slice();\n\n  resetSchedulerState();\n\n  // call component updated and activated hooks\n  callActivatedHooks(activatedQueue);\n  callUpdatedHooks(updatedQueue);\n\n  // devtool hook\n  /* istanbul ignore if */\n  if (devtools && config.devtools) {\n    devtools.emit('flush');\n  }\n}\n\nfunction callUpdatedHooks (queue) {\n  var i = queue.length;\n  while (i--) {\n    var watcher = queue[i];\n    var vm = watcher.vm;\n    if (vm._watcher === watcher && vm._isMounted && !vm._isDestroyed) {\n      callHook(vm, 'updated');\n    }\n  }\n}\n\n/**\n * Queue a kept-alive component that was activated during patch.\n * The queue will be processed after the entire tree has been patched.\n */\nfunction queueActivatedComponent (vm) {\n  // setting _inactive to false here so that a render function can\n  // rely on checking whether it's in an inactive tree (e.g. router-view)\n  vm._inactive = false;\n  activatedChildren.push(vm);\n}\n\nfunction callActivatedHooks (queue) {\n  for (var i = 0; i < queue.length; i++) {\n    queue[i]._inactive = true;\n    activateChildComponent(queue[i], true /* true */);\n  }\n}\n\n/**\n * Push a watcher into the watcher queue.\n * Jobs with duplicate IDs will be skipped unless it's\n * pushed when the queue is being flushed.\n */\nfunction queueWatcher (watcher) {\n  var id = watcher.id;\n  if (has[id] == null) {\n    has[id] = true;\n    if (!flushing) {\n      queue.push(watcher);\n    } else {\n      // if already flushing, splice the watcher based on its id\n      // if already past its id, it will be run next immediately.\n      var i = queue.length - 1;\n      while (i > index && queue[i].id > watcher.id) {\n        i--;\n      }\n      queue.splice(i + 1, 0, watcher);\n    }\n    // queue the flush\n    if (!waiting) {\n      waiting = true;\n\n      if (process.env.NODE_ENV !== 'production' && !config.async) {\n        flushSchedulerQueue();\n        return\n      }\n      nextTick(flushSchedulerQueue);\n    }\n  }\n}\n\n/*  */\n\n\n\nvar uid$2 = 0;\n\n/**\n * A watcher parses an expression, collects dependencies,\n * and fires callback when the expression value changes.\n * This is used for both the $watch() api and directives.\n */\nvar Watcher = function Watcher (\n  vm,\n  expOrFn,\n  cb,\n  options,\n  isRenderWatcher\n) {\n  this.vm = vm;\n  if (isRenderWatcher) {\n    vm._watcher = this;\n  }\n  vm._watchers.push(this);\n  // options\n  if (options) {\n    this.deep = !!options.deep;\n    this.user = !!options.user;\n    this.lazy = !!options.lazy;\n    this.sync = !!options.sync;\n    this.before = options.before;\n  } else {\n    this.deep = this.user = this.lazy = this.sync = false;\n  }\n  this.cb = cb;\n  this.id = ++uid$2; // uid for batching\n  this.active = true;\n  this.dirty = this.lazy; // for lazy watchers\n  this.deps = [];\n  this.newDeps = [];\n  this.depIds = new _Set();\n  this.newDepIds = new _Set();\n  this.expression = process.env.NODE_ENV !== 'production'\n    ? expOrFn.toString()\n    : '';\n  // parse expression for getter\n  if (typeof expOrFn === 'function') {\n    this.getter = expOrFn;\n  } else {\n    this.getter = parsePath(expOrFn);\n    if (!this.getter) {\n      this.getter = noop;\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed watching path: \\\"\" + expOrFn + \"\\\" \" +\n        'Watcher only accepts simple dot-delimited paths. ' +\n        'For full control, use a function instead.',\n        vm\n      );\n    }\n  }\n  this.value = this.lazy\n    ? undefined\n    : this.get();\n};\n\n/**\n * Evaluate the getter, and re-collect dependencies.\n */\nWatcher.prototype.get = function get () {\n  pushTarget(this);\n  var value;\n  var vm = this.vm;\n  try {\n    value = this.getter.call(vm, vm);\n  } catch (e) {\n    if (this.user) {\n      handleError(e, vm, (\"getter for watcher \\\"\" + (this.expression) + \"\\\"\"));\n    } else {\n      throw e\n    }\n  } finally {\n    // \"touch\" every property so they are all tracked as\n    // dependencies for deep watching\n    if (this.deep) {\n      traverse(value);\n    }\n    popTarget();\n    this.cleanupDeps();\n  }\n  return value\n};\n\n/**\n * Add a dependency to this directive.\n */\nWatcher.prototype.addDep = function addDep (dep) {\n  var id = dep.id;\n  if (!this.newDepIds.has(id)) {\n    this.newDepIds.add(id);\n    this.newDeps.push(dep);\n    if (!this.depIds.has(id)) {\n      dep.addSub(this);\n    }\n  }\n};\n\n/**\n * Clean up for dependency collection.\n */\nWatcher.prototype.cleanupDeps = function cleanupDeps () {\n  var i = this.deps.length;\n  while (i--) {\n    var dep = this.deps[i];\n    if (!this.newDepIds.has(dep.id)) {\n      dep.removeSub(this);\n    }\n  }\n  var tmp = this.depIds;\n  this.depIds = this.newDepIds;\n  this.newDepIds = tmp;\n  this.newDepIds.clear();\n  tmp = this.deps;\n  this.deps = this.newDeps;\n  this.newDeps = tmp;\n  this.newDeps.length = 0;\n};\n\n/**\n * Subscriber interface.\n * Will be called when a dependency changes.\n */\nWatcher.prototype.update = function update () {\n  /* istanbul ignore else */\n  if (this.lazy) {\n    this.dirty = true;\n  } else if (this.sync) {\n    this.run();\n  } else {\n    queueWatcher(this);\n  }\n};\n\n/**\n * Scheduler job interface.\n * Will be called by the scheduler.\n */\nWatcher.prototype.run = function run () {\n  if (this.active) {\n    var value = this.get();\n    if (\n      value !== this.value ||\n      // Deep watchers and watchers on Object/Arrays should fire even\n      // when the value is the same, because the value may\n      // have mutated.\n      isObject(value) ||\n      this.deep\n    ) {\n      // set new value\n      var oldValue = this.value;\n      this.value = value;\n      if (this.user) {\n        try {\n          this.cb.call(this.vm, value, oldValue);\n        } catch (e) {\n          handleError(e, this.vm, (\"callback for watcher \\\"\" + (this.expression) + \"\\\"\"));\n        }\n      } else {\n        this.cb.call(this.vm, value, oldValue);\n      }\n    }\n  }\n};\n\n/**\n * Evaluate the value of the watcher.\n * This only gets called for lazy watchers.\n */\nWatcher.prototype.evaluate = function evaluate () {\n  this.value = this.get();\n  this.dirty = false;\n};\n\n/**\n * Depend on all deps collected by this watcher.\n */\nWatcher.prototype.depend = function depend () {\n  var i = this.deps.length;\n  while (i--) {\n    this.deps[i].depend();\n  }\n};\n\n/**\n * Remove self from all dependencies' subscriber list.\n */\nWatcher.prototype.teardown = function teardown () {\n  if (this.active) {\n    // remove self from vm's watcher list\n    // this is a somewhat expensive operation so we skip it\n    // if the vm is being destroyed.\n    if (!this.vm._isBeingDestroyed) {\n      remove(this.vm._watchers, this);\n    }\n    var i = this.deps.length;\n    while (i--) {\n      this.deps[i].removeSub(this);\n    }\n    this.active = false;\n  }\n};\n\n/*  */\n\nvar sharedPropertyDefinition = {\n  enumerable: true,\n  configurable: true,\n  get: noop,\n  set: noop\n};\n\nfunction proxy (target, sourceKey, key) {\n  sharedPropertyDefinition.get = function proxyGetter () {\n    return this[sourceKey][key]\n  };\n  sharedPropertyDefinition.set = function proxySetter (val) {\n    this[sourceKey][key] = val;\n  };\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction initState (vm) {\n  vm._watchers = [];\n  var opts = vm.$options;\n  if (opts.props) { initProps(vm, opts.props); }\n  if (opts.methods) { initMethods(vm, opts.methods); }\n  if (opts.data) {\n    initData(vm);\n  } else {\n    observe(vm._data = {}, true /* asRootData */);\n  }\n  if (opts.computed) { initComputed(vm, opts.computed); }\n  if (opts.watch && opts.watch !== nativeWatch) {\n    initWatch(vm, opts.watch);\n  }\n}\n\nfunction initProps (vm, propsOptions) {\n  var propsData = vm.$options.propsData || {};\n  var props = vm._props = {};\n  // cache prop keys so that future props updates can iterate using Array\n  // instead of dynamic object key enumeration.\n  var keys = vm.$options._propKeys = [];\n  var isRoot = !vm.$parent;\n  // root instance props should be converted\n  if (!isRoot) {\n    toggleObserving(false);\n  }\n  var loop = function ( key ) {\n    keys.push(key);\n    var value = validateProp(key, propsOptions, propsData, vm);\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      var hyphenatedKey = hyphenate(key);\n      if (isReservedAttribute(hyphenatedKey) ||\n          config.isReservedAttr(hyphenatedKey)) {\n        warn(\n          (\"\\\"\" + hyphenatedKey + \"\\\" is a reserved attribute and cannot be used as component prop.\"),\n          vm\n        );\n      }\n      defineReactive$$1(props, key, value, function () {\n        if (!isRoot && !isUpdatingChildComponent) {\n          {\n            if(vm.mpHost === 'mp-baidu' || vm.mpHost === 'mp-kuaishou' || vm.mpHost === 'mp-xhs'){//百度、快手、小红书 observer 在 setData callback 之后触发，直接忽略该 warn\n                return\n            }\n            //fixed by xxxxxx __next_tick_pending,uni://form-field 时不告警\n            if(\n                key === 'value' && \n                Array.isArray(vm.$options.behaviors) &&\n                vm.$options.behaviors.indexOf('uni://form-field') !== -1\n              ){\n              return\n            }\n            if(vm._getFormData){\n              return\n            }\n            var $parent = vm.$parent;\n            while($parent){\n              if($parent.__next_tick_pending){\n                return  \n              }\n              $parent = $parent.$parent;\n            }\n          }\n          warn(\n            \"Avoid mutating a prop directly since the value will be \" +\n            \"overwritten whenever the parent component re-renders. \" +\n            \"Instead, use a data or computed property based on the prop's \" +\n            \"value. Prop being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        }\n      });\n    } else {\n      defineReactive$$1(props, key, value);\n    }\n    // static props are already proxied on the component's prototype\n    // during Vue.extend(). We only need to proxy props defined at\n    // instantiation here.\n    if (!(key in vm)) {\n      proxy(vm, \"_props\", key);\n    }\n  };\n\n  for (var key in propsOptions) loop( key );\n  toggleObserving(true);\n}\n\nfunction initData (vm) {\n  var data = vm.$options.data;\n  data = vm._data = typeof data === 'function'\n    ? getData(data, vm)\n    : data || {};\n  if (!isPlainObject(data)) {\n    data = {};\n    process.env.NODE_ENV !== 'production' && warn(\n      'data functions should return an object:\\n' +\n      'https://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function',\n      vm\n    );\n  }\n  // proxy data on instance\n  var keys = Object.keys(data);\n  var props = vm.$options.props;\n  var methods = vm.$options.methods;\n  var i = keys.length;\n  while (i--) {\n    var key = keys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (methods && hasOwn(methods, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a data property.\"),\n          vm\n        );\n      }\n    }\n    if (props && hasOwn(props, key)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"The data property \\\"\" + key + \"\\\" is already declared as a prop. \" +\n        \"Use prop default value instead.\",\n        vm\n      );\n    } else if (!isReserved(key)) {\n      proxy(vm, \"_data\", key);\n    }\n  }\n  // observe data\n  observe(data, true /* asRootData */);\n}\n\nfunction getData (data, vm) {\n  // #7573 disable dep collection when invoking data getters\n  pushTarget();\n  try {\n    return data.call(vm, vm)\n  } catch (e) {\n    handleError(e, vm, \"data()\");\n    return {}\n  } finally {\n    popTarget();\n  }\n}\n\nvar computedWatcherOptions = { lazy: true };\n\nfunction initComputed (vm, computed) {\n  // $flow-disable-line\n  var watchers = vm._computedWatchers = Object.create(null);\n  // computed properties are just getters during SSR\n  var isSSR = isServerRendering();\n\n  for (var key in computed) {\n    var userDef = computed[key];\n    var getter = typeof userDef === 'function' ? userDef : userDef.get;\n    if (process.env.NODE_ENV !== 'production' && getter == null) {\n      warn(\n        (\"Getter is missing for computed property \\\"\" + key + \"\\\".\"),\n        vm\n      );\n    }\n\n    if (!isSSR) {\n      // create internal watcher for the computed property.\n      watchers[key] = new Watcher(\n        vm,\n        getter || noop,\n        noop,\n        computedWatcherOptions\n      );\n    }\n\n    // component-defined computed properties are already defined on the\n    // component prototype. We only need to define computed properties defined\n    // at instantiation here.\n    if (!(key in vm)) {\n      defineComputed(vm, key, userDef);\n    } else if (process.env.NODE_ENV !== 'production') {\n      if (key in vm.$data) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined in data.\"), vm);\n      } else if (vm.$options.props && key in vm.$options.props) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined as a prop.\"), vm);\n      }\n    }\n  }\n}\n\nfunction defineComputed (\n  target,\n  key,\n  userDef\n) {\n  var shouldCache = !isServerRendering();\n  if (typeof userDef === 'function') {\n    sharedPropertyDefinition.get = shouldCache\n      ? createComputedGetter(key)\n      : createGetterInvoker(userDef);\n    sharedPropertyDefinition.set = noop;\n  } else {\n    sharedPropertyDefinition.get = userDef.get\n      ? shouldCache && userDef.cache !== false\n        ? createComputedGetter(key)\n        : createGetterInvoker(userDef.get)\n      : noop;\n    sharedPropertyDefinition.set = userDef.set || noop;\n  }\n  if (process.env.NODE_ENV !== 'production' &&\n      sharedPropertyDefinition.set === noop) {\n    sharedPropertyDefinition.set = function () {\n      warn(\n        (\"Computed property \\\"\" + key + \"\\\" was assigned to but it has no setter.\"),\n        this\n      );\n    };\n  }\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction createComputedGetter (key) {\n  return function computedGetter () {\n    var watcher = this._computedWatchers && this._computedWatchers[key];\n    if (watcher) {\n      if (watcher.dirty) {\n        watcher.evaluate();\n      }\n      if (Dep.SharedObject.target) {// fixed by xxxxxx\n        watcher.depend();\n      }\n      return watcher.value\n    }\n  }\n}\n\nfunction createGetterInvoker(fn) {\n  return function computedGetter () {\n    return fn.call(this, this)\n  }\n}\n\nfunction initMethods (vm, methods) {\n  var props = vm.$options.props;\n  for (var key in methods) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof methods[key] !== 'function') {\n        warn(\n          \"Method \\\"\" + key + \"\\\" has type \\\"\" + (typeof methods[key]) + \"\\\" in the component definition. \" +\n          \"Did you reference the function correctly?\",\n          vm\n        );\n      }\n      if (props && hasOwn(props, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a prop.\"),\n          vm\n        );\n      }\n      if ((key in vm) && isReserved(key)) {\n        warn(\n          \"Method \\\"\" + key + \"\\\" conflicts with an existing Vue instance method. \" +\n          \"Avoid defining component methods that start with _ or $.\"\n        );\n      }\n    }\n    vm[key] = typeof methods[key] !== 'function' ? noop : bind(methods[key], vm);\n  }\n}\n\nfunction initWatch (vm, watch) {\n  for (var key in watch) {\n    var handler = watch[key];\n    if (Array.isArray(handler)) {\n      for (var i = 0; i < handler.length; i++) {\n        createWatcher(vm, key, handler[i]);\n      }\n    } else {\n      createWatcher(vm, key, handler);\n    }\n  }\n}\n\nfunction createWatcher (\n  vm,\n  expOrFn,\n  handler,\n  options\n) {\n  if (isPlainObject(handler)) {\n    options = handler;\n    handler = handler.handler;\n  }\n  if (typeof handler === 'string') {\n    handler = vm[handler];\n  }\n  return vm.$watch(expOrFn, handler, options)\n}\n\nfunction stateMixin (Vue) {\n  // flow somehow has problems with directly declared definition object\n  // when using Object.defineProperty, so we have to procedurally build up\n  // the object here.\n  var dataDef = {};\n  dataDef.get = function () { return this._data };\n  var propsDef = {};\n  propsDef.get = function () { return this._props };\n  if (process.env.NODE_ENV !== 'production') {\n    dataDef.set = function () {\n      warn(\n        'Avoid replacing instance root $data. ' +\n        'Use nested data properties instead.',\n        this\n      );\n    };\n    propsDef.set = function () {\n      warn(\"$props is readonly.\", this);\n    };\n  }\n  Object.defineProperty(Vue.prototype, '$data', dataDef);\n  Object.defineProperty(Vue.prototype, '$props', propsDef);\n\n  Vue.prototype.$set = set;\n  Vue.prototype.$delete = del;\n\n  Vue.prototype.$watch = function (\n    expOrFn,\n    cb,\n    options\n  ) {\n    var vm = this;\n    if (isPlainObject(cb)) {\n      return createWatcher(vm, expOrFn, cb, options)\n    }\n    options = options || {};\n    options.user = true;\n    var watcher = new Watcher(vm, expOrFn, cb, options);\n    if (options.immediate) {\n      try {\n        cb.call(vm, watcher.value);\n      } catch (error) {\n        handleError(error, vm, (\"callback for immediate watcher \\\"\" + (watcher.expression) + \"\\\"\"));\n      }\n    }\n    return function unwatchFn () {\n      watcher.teardown();\n    }\n  };\n}\n\n/*  */\n\nvar uid$3 = 0;\n\nfunction initMixin (Vue) {\n  Vue.prototype._init = function (options) {\n    var vm = this;\n    // a uid\n    vm._uid = uid$3++;\n\n    var startTag, endTag;\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      startTag = \"vue-perf-start:\" + (vm._uid);\n      endTag = \"vue-perf-end:\" + (vm._uid);\n      mark(startTag);\n    }\n\n    // a flag to avoid this being observed\n    vm._isVue = true;\n    // merge options\n    if (options && options._isComponent) {\n      // optimize internal component instantiation\n      // since dynamic options merging is pretty slow, and none of the\n      // internal component options needs special treatment.\n      initInternalComponent(vm, options);\n    } else {\n      vm.$options = mergeOptions(\n        resolveConstructorOptions(vm.constructor),\n        options || {},\n        vm\n      );\n    }\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      initProxy(vm);\n    } else {\n      vm._renderProxy = vm;\n    }\n    // expose real self\n    vm._self = vm;\n    initLifecycle(vm);\n    initEvents(vm);\n    initRender(vm);\n    callHook(vm, 'beforeCreate');\n    !vm._$fallback && initInjections(vm); // resolve injections before data/props  \n    initState(vm);\n    !vm._$fallback && initProvide(vm); // resolve provide after data/props\n    !vm._$fallback && callHook(vm, 'created');      \n\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      vm._name = formatComponentName(vm, false);\n      mark(endTag);\n      measure((\"vue \" + (vm._name) + \" init\"), startTag, endTag);\n    }\n\n    if (vm.$options.el) {\n      vm.$mount(vm.$options.el);\n    }\n  };\n}\n\nfunction initInternalComponent (vm, options) {\n  var opts = vm.$options = Object.create(vm.constructor.options);\n  // doing this because it's faster than dynamic enumeration.\n  var parentVnode = options._parentVnode;\n  opts.parent = options.parent;\n  opts._parentVnode = parentVnode;\n\n  var vnodeComponentOptions = parentVnode.componentOptions;\n  opts.propsData = vnodeComponentOptions.propsData;\n  opts._parentListeners = vnodeComponentOptions.listeners;\n  opts._renderChildren = vnodeComponentOptions.children;\n  opts._componentTag = vnodeComponentOptions.tag;\n\n  if (options.render) {\n    opts.render = options.render;\n    opts.staticRenderFns = options.staticRenderFns;\n  }\n}\n\nfunction resolveConstructorOptions (Ctor) {\n  var options = Ctor.options;\n  if (Ctor.super) {\n    var superOptions = resolveConstructorOptions(Ctor.super);\n    var cachedSuperOptions = Ctor.superOptions;\n    if (superOptions !== cachedSuperOptions) {\n      // super option changed,\n      // need to resolve new options.\n      Ctor.superOptions = superOptions;\n      // check if there are any late-modified/attached options (#4976)\n      var modifiedOptions = resolveModifiedOptions(Ctor);\n      // update base extend options\n      if (modifiedOptions) {\n        extend(Ctor.extendOptions, modifiedOptions);\n      }\n      options = Ctor.options = mergeOptions(superOptions, Ctor.extendOptions);\n      if (options.name) {\n        options.components[options.name] = Ctor;\n      }\n    }\n  }\n  return options\n}\n\nfunction resolveModifiedOptions (Ctor) {\n  var modified;\n  var latest = Ctor.options;\n  var sealed = Ctor.sealedOptions;\n  for (var key in latest) {\n    if (latest[key] !== sealed[key]) {\n      if (!modified) { modified = {}; }\n      modified[key] = latest[key];\n    }\n  }\n  return modified\n}\n\nfunction Vue (options) {\n  if (process.env.NODE_ENV !== 'production' &&\n    !(this instanceof Vue)\n  ) {\n    warn('Vue is a constructor and should be called with the `new` keyword');\n  }\n  this._init(options);\n}\n\ninitMixin(Vue);\nstateMixin(Vue);\neventsMixin(Vue);\nlifecycleMixin(Vue);\nrenderMixin(Vue);\n\n/*  */\n\nfunction initUse (Vue) {\n  Vue.use = function (plugin) {\n    var installedPlugins = (this._installedPlugins || (this._installedPlugins = []));\n    if (installedPlugins.indexOf(plugin) > -1) {\n      return this\n    }\n\n    // additional parameters\n    var args = toArray(arguments, 1);\n    args.unshift(this);\n    if (typeof plugin.install === 'function') {\n      plugin.install.apply(plugin, args);\n    } else if (typeof plugin === 'function') {\n      plugin.apply(null, args);\n    }\n    installedPlugins.push(plugin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initMixin$1 (Vue) {\n  Vue.mixin = function (mixin) {\n    this.options = mergeOptions(this.options, mixin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initExtend (Vue) {\n  /**\n   * Each instance constructor, including Vue, has a unique\n   * cid. This enables us to create wrapped \"child\n   * constructors\" for prototypal inheritance and cache them.\n   */\n  Vue.cid = 0;\n  var cid = 1;\n\n  /**\n   * Class inheritance\n   */\n  Vue.extend = function (extendOptions) {\n    extendOptions = extendOptions || {};\n    var Super = this;\n    var SuperId = Super.cid;\n    var cachedCtors = extendOptions._Ctor || (extendOptions._Ctor = {});\n    if (cachedCtors[SuperId]) {\n      return cachedCtors[SuperId]\n    }\n\n    var name = extendOptions.name || Super.options.name;\n    if (process.env.NODE_ENV !== 'production' && name) {\n      validateComponentName(name);\n    }\n\n    var Sub = function VueComponent (options) {\n      this._init(options);\n    };\n    Sub.prototype = Object.create(Super.prototype);\n    Sub.prototype.constructor = Sub;\n    Sub.cid = cid++;\n    Sub.options = mergeOptions(\n      Super.options,\n      extendOptions\n    );\n    Sub['super'] = Super;\n\n    // For props and computed properties, we define the proxy getters on\n    // the Vue instances at extension time, on the extended prototype. This\n    // avoids Object.defineProperty calls for each instance created.\n    if (Sub.options.props) {\n      initProps$1(Sub);\n    }\n    if (Sub.options.computed) {\n      initComputed$1(Sub);\n    }\n\n    // allow further extension/mixin/plugin usage\n    Sub.extend = Super.extend;\n    Sub.mixin = Super.mixin;\n    Sub.use = Super.use;\n\n    // create asset registers, so extended classes\n    // can have their private assets too.\n    ASSET_TYPES.forEach(function (type) {\n      Sub[type] = Super[type];\n    });\n    // enable recursive self-lookup\n    if (name) {\n      Sub.options.components[name] = Sub;\n    }\n\n    // keep a reference to the super options at extension time.\n    // later at instantiation we can check if Super's options have\n    // been updated.\n    Sub.superOptions = Super.options;\n    Sub.extendOptions = extendOptions;\n    Sub.sealedOptions = extend({}, Sub.options);\n\n    // cache constructor\n    cachedCtors[SuperId] = Sub;\n    return Sub\n  };\n}\n\nfunction initProps$1 (Comp) {\n  var props = Comp.options.props;\n  for (var key in props) {\n    proxy(Comp.prototype, \"_props\", key);\n  }\n}\n\nfunction initComputed$1 (Comp) {\n  var computed = Comp.options.computed;\n  for (var key in computed) {\n    defineComputed(Comp.prototype, key, computed[key]);\n  }\n}\n\n/*  */\n\nfunction initAssetRegisters (Vue) {\n  /**\n   * Create asset registration methods.\n   */\n  ASSET_TYPES.forEach(function (type) {\n    Vue[type] = function (\n      id,\n      definition\n    ) {\n      if (!definition) {\n        return this.options[type + 's'][id]\n      } else {\n        /* istanbul ignore if */\n        if (process.env.NODE_ENV !== 'production' && type === 'component') {\n          validateComponentName(id);\n        }\n        if (type === 'component' && isPlainObject(definition)) {\n          definition.name = definition.name || id;\n          definition = this.options._base.extend(definition);\n        }\n        if (type === 'directive' && typeof definition === 'function') {\n          definition = { bind: definition, update: definition };\n        }\n        this.options[type + 's'][id] = definition;\n        return definition\n      }\n    };\n  });\n}\n\n/*  */\n\n\n\nfunction getComponentName (opts) {\n  return opts && (opts.Ctor.options.name || opts.tag)\n}\n\nfunction matches (pattern, name) {\n  if (Array.isArray(pattern)) {\n    return pattern.indexOf(name) > -1\n  } else if (typeof pattern === 'string') {\n    return pattern.split(',').indexOf(name) > -1\n  } else if (isRegExp(pattern)) {\n    return pattern.test(name)\n  }\n  /* istanbul ignore next */\n  return false\n}\n\nfunction pruneCache (keepAliveInstance, filter) {\n  var cache = keepAliveInstance.cache;\n  var keys = keepAliveInstance.keys;\n  var _vnode = keepAliveInstance._vnode;\n  for (var key in cache) {\n    var cachedNode = cache[key];\n    if (cachedNode) {\n      var name = getComponentName(cachedNode.componentOptions);\n      if (name && !filter(name)) {\n        pruneCacheEntry(cache, key, keys, _vnode);\n      }\n    }\n  }\n}\n\nfunction pruneCacheEntry (\n  cache,\n  key,\n  keys,\n  current\n) {\n  var cached$$1 = cache[key];\n  if (cached$$1 && (!current || cached$$1.tag !== current.tag)) {\n    cached$$1.componentInstance.$destroy();\n  }\n  cache[key] = null;\n  remove(keys, key);\n}\n\nvar patternTypes = [String, RegExp, Array];\n\nvar KeepAlive = {\n  name: 'keep-alive',\n  abstract: true,\n\n  props: {\n    include: patternTypes,\n    exclude: patternTypes,\n    max: [String, Number]\n  },\n\n  created: function created () {\n    this.cache = Object.create(null);\n    this.keys = [];\n  },\n\n  destroyed: function destroyed () {\n    for (var key in this.cache) {\n      pruneCacheEntry(this.cache, key, this.keys);\n    }\n  },\n\n  mounted: function mounted () {\n    var this$1 = this;\n\n    this.$watch('include', function (val) {\n      pruneCache(this$1, function (name) { return matches(val, name); });\n    });\n    this.$watch('exclude', function (val) {\n      pruneCache(this$1, function (name) { return !matches(val, name); });\n    });\n  },\n\n  render: function render () {\n    var slot = this.$slots.default;\n    var vnode = getFirstComponentChild(slot);\n    var componentOptions = vnode && vnode.componentOptions;\n    if (componentOptions) {\n      // check pattern\n      var name = getComponentName(componentOptions);\n      var ref = this;\n      var include = ref.include;\n      var exclude = ref.exclude;\n      if (\n        // not included\n        (include && (!name || !matches(include, name))) ||\n        // excluded\n        (exclude && name && matches(exclude, name))\n      ) {\n        return vnode\n      }\n\n      var ref$1 = this;\n      var cache = ref$1.cache;\n      var keys = ref$1.keys;\n      var key = vnode.key == null\n        // same constructor may get registered as different local components\n        // so cid alone is not enough (#3269)\n        ? componentOptions.Ctor.cid + (componentOptions.tag ? (\"::\" + (componentOptions.tag)) : '')\n        : vnode.key;\n      if (cache[key]) {\n        vnode.componentInstance = cache[key].componentInstance;\n        // make current key freshest\n        remove(keys, key);\n        keys.push(key);\n      } else {\n        cache[key] = vnode;\n        keys.push(key);\n        // prune oldest entry\n        if (this.max && keys.length > parseInt(this.max)) {\n          pruneCacheEntry(cache, keys[0], keys, this._vnode);\n        }\n      }\n\n      vnode.data.keepAlive = true;\n    }\n    return vnode || (slot && slot[0])\n  }\n};\n\nvar builtInComponents = {\n  KeepAlive: KeepAlive\n};\n\n/*  */\n\nfunction initGlobalAPI (Vue) {\n  // config\n  var configDef = {};\n  configDef.get = function () { return config; };\n  if (process.env.NODE_ENV !== 'production') {\n    configDef.set = function () {\n      warn(\n        'Do not replace the Vue.config object, set individual fields instead.'\n      );\n    };\n  }\n  Object.defineProperty(Vue, 'config', configDef);\n\n  // exposed util methods.\n  // NOTE: these are not considered part of the public API - avoid relying on\n  // them unless you are aware of the risk.\n  Vue.util = {\n    warn: warn,\n    extend: extend,\n    mergeOptions: mergeOptions,\n    defineReactive: defineReactive$$1\n  };\n\n  Vue.set = set;\n  Vue.delete = del;\n  Vue.nextTick = nextTick;\n\n  // 2.6 explicit observable API\n  Vue.observable = function (obj) {\n    observe(obj);\n    return obj\n  };\n\n  Vue.options = Object.create(null);\n  ASSET_TYPES.forEach(function (type) {\n    Vue.options[type + 's'] = Object.create(null);\n  });\n\n  // this is used to identify the \"base\" constructor to extend all plain-object\n  // components with in Weex's multi-instance scenarios.\n  Vue.options._base = Vue;\n\n  extend(Vue.options.components, builtInComponents);\n\n  initUse(Vue);\n  initMixin$1(Vue);\n  initExtend(Vue);\n  initAssetRegisters(Vue);\n}\n\ninitGlobalAPI(Vue);\n\nObject.defineProperty(Vue.prototype, '$isServer', {\n  get: isServerRendering\n});\n\nObject.defineProperty(Vue.prototype, '$ssrContext', {\n  get: function get () {\n    /* istanbul ignore next */\n    return this.$vnode && this.$vnode.ssrContext\n  }\n});\n\n// expose FunctionalRenderContext for ssr runtime helper installation\nObject.defineProperty(Vue, 'FunctionalRenderContext', {\n  value: FunctionalRenderContext\n});\n\nVue.version = '2.6.11';\n\n/**\n * https://raw.githubusercontent.com/Tencent/westore/master/packages/westore/utils/diff.js\n */\nvar ARRAYTYPE = '[object Array]';\nvar OBJECTTYPE = '[object Object]';\nvar NULLTYPE = '[object Null]';\nvar UNDEFINEDTYPE = '[object Undefined]';\n// const FUNCTIONTYPE = '[object Function]'\n\nfunction diff(current, pre) {\n    var result = {};\n    syncKeys(current, pre);\n    _diff(current, pre, '', result);\n    return result\n}\n\nfunction syncKeys(current, pre) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE && rootPreType == OBJECTTYPE) {\n        if(Object.keys(current).length >= Object.keys(pre).length){\n            for (var key in pre) {\n                var currentValue = current[key];\n                if (currentValue === undefined) {\n                    current[key] = null;\n                } else {\n                    syncKeys(currentValue, pre[key]);\n                }\n            }\n        }\n    } else if (rootCurrentType == ARRAYTYPE && rootPreType == ARRAYTYPE) {\n        if (current.length >= pre.length) {\n            pre.forEach(function (item, index) {\n                syncKeys(current[index], item);\n            });\n        }\n    }\n}\n\nfunction nullOrUndefined(currentType, preType) {\n    if(\n        (currentType === NULLTYPE || currentType === UNDEFINEDTYPE) && \n        (preType === NULLTYPE || preType === UNDEFINEDTYPE)\n    ) {\n        return false\n    }\n    return true\n}\n\nfunction _diff(current, pre, path, result) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE) {\n        if (rootPreType != OBJECTTYPE || Object.keys(current).length < Object.keys(pre).length) {\n            setResult(result, path, current);\n        } else {\n            var loop = function ( key ) {\n                var currentValue = current[key];\n                var preValue = pre[key];\n                var currentType = type(currentValue);\n                var preType = type(preValue);\n                if (currentType != ARRAYTYPE && currentType != OBJECTTYPE) {\n                    if (currentValue !== pre[key] && nullOrUndefined(currentType, preType)) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    }\n                } else if (currentType == ARRAYTYPE) {\n                    if (preType != ARRAYTYPE) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        if (currentValue.length < preValue.length) {\n                            setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                        } else {\n                            currentValue.forEach(function (item, index) {\n                                _diff(item, preValue[index], (path == '' ? '' : path + \".\") + key + '[' + index + ']', result);\n                            });\n                        }\n                    }\n                } else if (currentType == OBJECTTYPE) {\n                    if (preType != OBJECTTYPE || Object.keys(currentValue).length < Object.keys(preValue).length) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        for (var subKey in currentValue) {\n                            _diff(currentValue[subKey], preValue[subKey], (path == '' ? '' : path + \".\") + key + '.' + subKey, result);\n                        }\n                    }\n                }\n            };\n\n            for (var key in current) loop( key );\n        }\n    } else if (rootCurrentType == ARRAYTYPE) {\n        if (rootPreType != ARRAYTYPE) {\n            setResult(result, path, current);\n        } else {\n            if (current.length < pre.length) {\n                setResult(result, path, current);\n            } else {\n                current.forEach(function (item, index) {\n                    _diff(item, pre[index], path + '[' + index + ']', result);\n                });\n            }\n        }\n    } else {\n        setResult(result, path, current);\n    }\n}\n\nfunction setResult(result, k, v) {\n    // if (type(v) != FUNCTIONTYPE) {\n        result[k] = v;\n    // }\n}\n\nfunction type(obj) {\n    return Object.prototype.toString.call(obj)\n}\n\n/*  */\r\n\r\nfunction flushCallbacks$1(vm) {\r\n    if (vm.__next_tick_callbacks && vm.__next_tick_callbacks.length) {\r\n        if (process.env.VUE_APP_DEBUG) {\r\n            var mpInstance = vm.$scope;\r\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\r\n                ']:flushCallbacks[' + vm.__next_tick_callbacks.length + ']');\r\n        }\r\n        var copies = vm.__next_tick_callbacks.slice(0);\r\n        vm.__next_tick_callbacks.length = 0;\r\n        for (var i = 0; i < copies.length; i++) {\r\n            copies[i]();\r\n        }\r\n    }\r\n}\r\n\r\nfunction hasRenderWatcher(vm) {\r\n    return queue.find(function (watcher) { return vm._watcher === watcher; })\r\n}\r\n\r\nfunction nextTick$1(vm, cb) {\r\n    //1.nextTick 之前 已 setData 且 setData 还未回调完成\r\n    //2.nextTick 之前存在 render watcher\r\n    if (!vm.__next_tick_pending && !hasRenderWatcher(vm)) {\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\n                ']:nextVueTick');\n        }\r\n        return nextTick(cb, vm)\r\n    }else{\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance$1 = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance$1.is || mpInstance$1.route) + '][' + vm._uid +\n                ']:nextMPTick');\n        }\n    }\r\n    var _resolve;\r\n    if (!vm.__next_tick_callbacks) {\r\n        vm.__next_tick_callbacks = [];\r\n    }\r\n    vm.__next_tick_callbacks.push(function () {\r\n        if (cb) {\r\n            try {\r\n                cb.call(vm);\r\n            } catch (e) {\r\n                handleError(e, vm, 'nextTick');\r\n            }\r\n        } else if (_resolve) {\r\n            _resolve(vm);\r\n        }\r\n    });\r\n    // $flow-disable-line\r\n    if (!cb && typeof Promise !== 'undefined') {\r\n        return new Promise(function (resolve) {\r\n            _resolve = resolve;\r\n        })\r\n    }\r\n}\n\n/*  */\r\n\r\nfunction clearInstance(key, value) {\r\n  // 简易去除 Vue 和小程序组件实例\r\n  if (value) {\r\n    if (value._isVue || value.__v_isMPComponent) {\r\n      return {}\r\n    }\r\n  }\r\n  return value\r\n}\r\n\r\nfunction cloneWithData(vm) {\r\n  // 确保当前 vm 所有数据被同步\r\n  var ret = Object.create(null);\r\n  var dataKeys = [].concat(\r\n    Object.keys(vm._data || {}),\r\n    Object.keys(vm._computedWatchers || {}));\r\n\r\n  dataKeys.reduce(function(ret, key) {\r\n    ret[key] = vm[key];\r\n    return ret\r\n  }, ret);\r\n\r\n  // vue-composition-api\r\n  var compositionApiState = vm.__composition_api_state__ || vm.__secret_vfa_state__;\r\n  var rawBindings = compositionApiState && compositionApiState.rawBindings;\r\n  if (rawBindings) {\r\n    Object.keys(rawBindings).forEach(function (key) {\r\n      ret[key] = vm[key];\r\n    });\r\n  }\r\n\r\n  //TODO 需要把无用数据处理掉，比如 list=>l0 则 list 需要移除，否则多传输一份数据\r\n  Object.assign(ret, vm.$mp.data || {});\r\n  if (\r\n    Array.isArray(vm.$options.behaviors) &&\r\n    vm.$options.behaviors.indexOf('uni://form-field') !== -1\r\n  ) { //form-field\r\n    ret['name'] = vm.name;\r\n    ret['value'] = vm.value;\r\n  }\r\n\r\n  return JSON.parse(JSON.stringify(ret, clearInstance))\r\n}\r\n\r\nvar patch = function(oldVnode, vnode) {\n  var this$1 = this;\n\r\n  if (vnode === null) { //destroy\r\n    return\r\n  }\r\n  if (this.mpType === 'page' || this.mpType === 'component') {\r\n    var mpInstance = this.$scope;\r\n    var data = Object.create(null);\r\n    try {\r\n      data = cloneWithData(this);\r\n    } catch (err) {\r\n      console.error(err);\r\n    }\r\n    data.__webviewId__ = mpInstance.data.__webviewId__;\r\n    var mpData = Object.create(null);\r\n    Object.keys(data).forEach(function (key) { //仅同步 data 中有的数据\r\n      mpData[key] = mpInstance.data[key];\r\n    });\r\n    var diffData = this.$shouldDiffData === false ? data : diff(data, mpData);\r\n    if (Object.keys(diffData).length) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + this._uid +\r\n          ']差量更新',\r\n          JSON.stringify(diffData));\r\n      }\r\n      this.__next_tick_pending = true;\r\n      mpInstance.setData(diffData, function () {\r\n        this$1.__next_tick_pending = false;\r\n        flushCallbacks$1(this$1);\r\n      });\r\n    } else {\r\n      flushCallbacks$1(this);\r\n    }\r\n  }\r\n};\n\n/*  */\n\nfunction createEmptyRender() {\n\n}\n\nfunction mountComponent$1(\n  vm,\n  el,\n  hydrating\n) {\n  if (!vm.mpType) {//main.js 中的 new Vue\n    return vm\n  }\n  if (vm.mpType === 'app') {\n    vm.$options.render = createEmptyRender;\n  }\n  if (!vm.$options.render) {\n    vm.$options.render = createEmptyRender;\n    if (process.env.NODE_ENV !== 'production') {\n      /* istanbul ignore if */\n      if ((vm.$options.template && vm.$options.template.charAt(0) !== '#') ||\n        vm.$options.el || el) {\n        warn(\n          'You are using the runtime-only build of Vue where the template ' +\n          'compiler is not available. Either pre-compile the templates into ' +\n          'render functions, or use the compiler-included build.',\n          vm\n        );\n      } else {\n        warn(\n          'Failed to mount component: template or render function not defined.',\n          vm\n        );\n      }\n    }\n  }\n  \n  !vm._$fallback && callHook(vm, 'beforeMount');\n\n  var updateComponent = function () {\n    vm._update(vm._render(), hydrating);\n  };\n\n  // we set this to vm._watcher inside the watcher's constructor\n  // since the watcher's initial patch may call $forceUpdate (e.g. inside child\n  // component's mounted hook), which relies on vm._watcher being already defined\n  new Watcher(vm, updateComponent, noop, {\n    before: function before() {\n      if (vm._isMounted && !vm._isDestroyed) {\n        callHook(vm, 'beforeUpdate');\n      }\n    }\n  }, true /* isRenderWatcher */);\n  hydrating = false;\n  return vm\n}\n\n/*  */\n\nfunction renderClass (\n  staticClass,\n  dynamicClass\n) {\n  if (isDef(staticClass) || isDef(dynamicClass)) {\n    return concat(staticClass, stringifyClass(dynamicClass))\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction concat (a, b) {\n  return a ? b ? (a + ' ' + b) : a : (b || '')\n}\n\nfunction stringifyClass (value) {\n  if (Array.isArray(value)) {\n    return stringifyArray(value)\n  }\n  if (isObject(value)) {\n    return stringifyObject(value)\n  }\n  if (typeof value === 'string') {\n    return value\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction stringifyArray (value) {\n  var res = '';\n  var stringified;\n  for (var i = 0, l = value.length; i < l; i++) {\n    if (isDef(stringified = stringifyClass(value[i])) && stringified !== '') {\n      if (res) { res += ' '; }\n      res += stringified;\n    }\n  }\n  return res\n}\n\nfunction stringifyObject (value) {\n  var res = '';\n  for (var key in value) {\n    if (value[key]) {\n      if (res) { res += ' '; }\n      res += key;\n    }\n  }\n  return res\n}\n\n/*  */\n\nvar parseStyleText = cached(function (cssText) {\n  var res = {};\n  var listDelimiter = /;(?![^(]*\\))/g;\n  var propertyDelimiter = /:(.+)/;\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      var tmp = item.split(propertyDelimiter);\n      tmp.length > 1 && (res[tmp[0].trim()] = tmp[1].trim());\n    }\n  });\n  return res\n});\n\n// normalize possible array / string values into Object\nfunction normalizeStyleBinding (bindingStyle) {\n  if (Array.isArray(bindingStyle)) {\n    return toObject(bindingStyle)\n  }\n  if (typeof bindingStyle === 'string') {\n    return parseStyleText(bindingStyle)\n  }\n  return bindingStyle\n}\n\n/*  */\r\n\r\nvar MP_METHODS = ['createSelectorQuery', 'createIntersectionObserver', 'selectAllComponents', 'selectComponent'];\r\n\r\nfunction getTarget(obj, path) {\r\n  var parts = path.split('.');\r\n  var key = parts[0];\r\n  if (key.indexOf('__$n') === 0) { //number index\r\n    key = parseInt(key.replace('__$n', ''));\r\n  }\r\n  if (parts.length === 1) {\r\n    return obj[key]\r\n  }\r\n  return getTarget(obj[key], parts.slice(1).join('.'))\r\n}\r\n\r\nfunction internalMixin(Vue) {\r\n\r\n  Vue.config.errorHandler = function(err, vm, info) {\r\n    Vue.util.warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\r\n    console.error(err);\r\n    /* eslint-disable no-undef */\r\n    var app = typeof getApp === 'function' && getApp();\r\n    if (app && app.onError) {\r\n      app.onError(err);\r\n    }\r\n  };\r\n\r\n  var oldEmit = Vue.prototype.$emit;\r\n\r\n  Vue.prototype.$emit = function(event) {\r\n    if (this.$scope && event) {\r\n      var triggerEvent = this.$scope['_triggerEvent'] || this.$scope['triggerEvent'];\r\n      if (triggerEvent) {\r\n        try {\r\n          triggerEvent.call(this.$scope, event, {\r\n            __args__: toArray(arguments, 1)\r\n          });\r\n        } catch (error) {\r\n\r\n        }\r\n      }\r\n    }\r\n    return oldEmit.apply(this, arguments)\r\n  };\r\n\r\n  Vue.prototype.$nextTick = function(fn) {\r\n    return nextTick$1(this, fn)\r\n  };\r\n\r\n  MP_METHODS.forEach(function (method) {\r\n    Vue.prototype[method] = function(args) {\r\n      if (this.$scope && this.$scope[method]) {\r\n        return this.$scope[method](args)\r\n      }\r\n      // mp-alipay\r\n      if (typeof my === 'undefined') {\r\n        return\r\n      }\r\n      if (method === 'createSelectorQuery') {\r\n        /* eslint-disable no-undef */\r\n        return my.createSelectorQuery(args)\r\n      } else if (method === 'createIntersectionObserver') {\r\n        /* eslint-disable no-undef */\r\n        return my.createIntersectionObserver(args)\r\n      }\r\n      // TODO mp-alipay 暂不支持 selectAllComponents,selectComponent\r\n    };\r\n  });\r\n\r\n  Vue.prototype.__init_provide = initProvide;\r\n\r\n  Vue.prototype.__init_injections = initInjections;\r\n\r\n  Vue.prototype.__call_hook = function(hook, args) {\r\n    var vm = this;\r\n    // #7573 disable dep collection when invoking lifecycle hooks\r\n    pushTarget();\r\n    var handlers = vm.$options[hook];\r\n    var info = hook + \" hook\";\r\n    var ret;\r\n    if (handlers) {\r\n      for (var i = 0, j = handlers.length; i < j; i++) {\r\n        ret = invokeWithErrorHandling(handlers[i], vm, args ? [args] : null, vm, info);\r\n      }\r\n    }\r\n    if (vm._hasHookEvent) {\r\n      vm.$emit('hook:' + hook, args);\r\n    }\r\n    popTarget();\r\n    return ret\r\n  };\r\n\r\n  Vue.prototype.__set_model = function(target, key, value, modifiers) {\r\n    if (Array.isArray(modifiers)) {\r\n      if (modifiers.indexOf('trim') !== -1) {\r\n        value = value.trim();\r\n      }\r\n      if (modifiers.indexOf('number') !== -1) {\r\n        value = this._n(value);\r\n      }\r\n    }\r\n    if (!target) {\r\n      target = this;\r\n    }\r\n    // 解决动态属性添加\r\n    Vue.set(target, key, value);\r\n  };\r\n\r\n  Vue.prototype.__set_sync = function(target, key, value) {\r\n    if (!target) {\r\n      target = this;\r\n    }\r\n    // 解决动态属性添加\r\n    Vue.set(target, key, value);\r\n  };\r\n\r\n  Vue.prototype.__get_orig = function(item) {\r\n    if (isPlainObject(item)) {\r\n      return item['$orig'] || item\r\n    }\r\n    return item\r\n  };\r\n\r\n  Vue.prototype.__get_value = function(dataPath, target) {\r\n    return getTarget(target || this, dataPath)\r\n  };\r\n\r\n\r\n  Vue.prototype.__get_class = function(dynamicClass, staticClass) {\r\n    return renderClass(staticClass, dynamicClass)\r\n  };\r\n\r\n  Vue.prototype.__get_style = function(dynamicStyle, staticStyle) {\r\n    if (!dynamicStyle && !staticStyle) {\r\n      return ''\r\n    }\r\n    var dynamicStyleObj = normalizeStyleBinding(dynamicStyle);\r\n    var styleObj = staticStyle ? extend(staticStyle, dynamicStyleObj) : dynamicStyleObj;\r\n    return Object.keys(styleObj).map(function (name) { return ((hyphenate(name)) + \":\" + (styleObj[name])); }).join(';')\r\n  };\r\n\r\n  Vue.prototype.__map = function(val, iteratee) {\r\n    //TODO 暂不考虑 string\r\n    var ret, i, l, keys, key;\r\n    if (Array.isArray(val)) {\r\n      ret = new Array(val.length);\r\n      for (i = 0, l = val.length; i < l; i++) {\r\n        ret[i] = iteratee(val[i], i);\r\n      }\r\n      return ret\r\n    } else if (isObject(val)) {\r\n      keys = Object.keys(val);\r\n      ret = Object.create(null);\r\n      for (i = 0, l = keys.length; i < l; i++) {\r\n        key = keys[i];\r\n        ret[key] = iteratee(val[key], key, i);\r\n      }\r\n      return ret\r\n    } else if (typeof val === 'number') {\r\n      ret = new Array(val);\r\n      for (i = 0, l = val; i < l; i++) {\r\n        // 第一个参数暂时仍和小程序一致\r\n        ret[i] = iteratee(i, i);\r\n      }\r\n      return ret\r\n    }\r\n    return []\r\n  };\r\n\r\n}\n\n/*  */\r\n\r\nvar LIFECYCLE_HOOKS$1 = [\r\n    //App\r\n    'onLaunch',\r\n    'onShow',\r\n    'onHide',\r\n    'onUniNViewMessage',\r\n    'onPageNotFound',\r\n    'onThemeChange',\r\n    'onError',\r\n    'onUnhandledRejection',\r\n    //Page\r\n    'onInit',\r\n    'onLoad',\r\n    // 'onShow',\r\n    'onReady',\r\n    // 'onHide',\r\n    'onUnload',\r\n    'onPullDownRefresh',\r\n    'onReachBottom',\r\n    'onTabItemTap',\r\n    'onAddToFavorites',\r\n    'onShareTimeline',\r\n    'onShareAppMessage',\r\n    'onResize',\r\n    'onPageScroll',\r\n    'onNavigationBarButtonTap',\r\n    'onBackPress',\r\n    'onNavigationBarSearchInputChanged',\r\n    'onNavigationBarSearchInputConfirmed',\r\n    'onNavigationBarSearchInputClicked',\r\n    'onUploadDouyinVideo',\r\n    'onNFCReadMessage',\r\n    //Component\r\n    // 'onReady', // 兼容旧版本，应该移除该事件\r\n    'onPageShow',\r\n    'onPageHide',\r\n    'onPageResize'\r\n];\r\nfunction lifecycleMixin$1(Vue) {\r\n\r\n    //fixed vue-class-component\r\n    var oldExtend = Vue.extend;\r\n    Vue.extend = function(extendOptions) {\r\n        extendOptions = extendOptions || {};\r\n\r\n        var methods = extendOptions.methods;\r\n        if (methods) {\r\n            Object.keys(methods).forEach(function (methodName) {\r\n                if (LIFECYCLE_HOOKS$1.indexOf(methodName)!==-1) {\r\n                    extendOptions[methodName] = methods[methodName];\r\n                    delete methods[methodName];\r\n                }\r\n            });\r\n        }\r\n\r\n        return oldExtend.call(this, extendOptions)\r\n    };\r\n\r\n    var strategies = Vue.config.optionMergeStrategies;\r\n    var mergeHook = strategies.created;\r\n    LIFECYCLE_HOOKS$1.forEach(function (hook) {\r\n        strategies[hook] = mergeHook;\r\n    });\r\n\r\n    Vue.prototype.__lifecycle_hooks__ = LIFECYCLE_HOOKS$1;\r\n}\n\n/*  */\r\n\n// install platform patch function\r\nVue.prototype.__patch__ = patch;\r\n\r\n// public mount method\r\nVue.prototype.$mount = function(\r\n    el ,\r\n    hydrating \r\n) {\r\n    return mountComponent$1(this, el, hydrating)\r\n};\r\n\r\nlifecycleMixin$1(Vue);\r\ninternalMixin(Vue);\n\n/*  */\n\nexport default Vue;\n", "// 用户认证和权限管理工具\r\n\r\n/**\r\n * 获取用户token\r\n */\r\nexport function getToken() {\r\n\treturn uni.getStorageSync('user_token')\r\n}\r\n\r\n/**\r\n * 设置用户token\r\n */\r\nexport function setToken(token) {\r\n\tuni.setStorageSync('user_token', token)\r\n\tconsole.log('Token已保存')\r\n}\r\n\r\n/**\r\n * 移除用户token\r\n */\r\nexport function removeToken() {\r\n\tuni.removeStorageSync('user_token')\r\n\tuni.removeStorageSync('user_info')\r\n\tconsole.log('Token和相关信息已清除')\r\n}\r\n\r\n/**\r\n * 获取用户信息\r\n */\r\nexport function getUserInfo() {\r\n\treturn uni.getStorageSync('user_info')\r\n}\r\n\r\n/**\r\n * 设置用户信息\r\n */\r\nexport function setUserInfo(userInfo) {\r\n\treturn uni.setStorageSync('user_info', userInfo)\r\n}\r\n\r\n/**\r\n * 检查用户是否已登录\r\n */\r\nexport function isLoggedIn() {\r\n\tconst token = getToken()\r\n\tconst userInfo = getUserInfo()\r\n\r\n\treturn !!(token && userInfo)\r\n}\r\n\r\n\r\n\r\n/**\r\n * 检查登录状态，如果未登录则跳转到登录页\r\n * @param {boolean} showToast 是否显示提示\r\n * @returns {boolean} 是否已登录\r\n */\r\nexport function checkLogin(showToast = true) {\r\n\tif (!isLoggedIn()) {\r\n\t\tconst message = '请先登录'\r\n\r\n\t\tif (showToast) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: message,\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 1500\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\t// 延迟跳转，确保toast显示\r\n\t\tsetTimeout(() => {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/login/login'\r\n\t\t\t})\r\n\t\t}, showToast ? 1500 : 0)\r\n\r\n\t\treturn false\r\n\t}\r\n\treturn true\r\n}\r\n\r\n/**\r\n * 用户登出\r\n */\r\nexport function logout() {\r\n\treturn new Promise((resolve) => {\r\n\t\tuni.showModal({\r\n\t\t\ttitle: '确认退出',\r\n\t\t\tcontent: '确定要退出登录吗？',\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t// 清除本地存储\r\n\t\t\t\t\tremoveToken()\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '已退出登录',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\t// 跳转到登录页\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 1500)\r\n\r\n\t\t\t\t\tresolve(true)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tresolve(false)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t})\r\n\t})\r\n}\r\n\r\n/**\r\n * 页面登录检查混入\r\n * 在页面的 onLoad 或 onShow 中调用\r\n */\r\nexport const loginCheckMixin = {\r\n\tmethods: {\r\n\t\t// 检查登录状态\r\n\t\tcheckLoginStatus() {\r\n\t\t\treturn checkLogin()\r\n\t\t},\r\n\r\n\t\t// 退出登录\r\n\t\thandleLogout() {\r\n\t\t\treturn logout()\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * 需要登录的页面列表\r\n */\r\nexport const protectedPages = [\r\n\t'/pages/index/index',\r\n\t'/pages/activation/activation',\r\n\t'/pages/signin/signin'\r\n]\r\n\r\n/**\r\n * 检查当前页面是否需要登录\r\n */\r\nexport function isProtectedPage(url) {\r\n\t// 获取页面路径（去除参数）\r\n\tconst pagePath = url.split('?')[0]\r\n\treturn protectedPages.some(page => pagePath.includes(page))\r\n}\r\n\r\n// 全局变量，用于临时禁用路由守卫\r\nlet guardDisabled = false\r\n\r\n/**\r\n * 临时禁用路由守卫\r\n */\r\nexport function disableGuard() {\r\n\tguardDisabled = true\r\n\t// 2秒后自动恢复\r\n\tsetTimeout(() => {\r\n\t\tguardDisabled = false\r\n\t}, 2000)\r\n}\r\n\r\n/**\r\n * 全局页面跳转拦截\r\n */\r\nexport function setupNavigationGuard() {\r\n\t// 拦截 navigateTo\r\n\tconst originalNavigateTo = uni.navigateTo\r\n\tuni.navigateTo = function(options) {\r\n\t\tif (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {\r\n\t\t\tconsole.log('navigateTo被拦截:', options.url, '登录状态:', isLoggedIn())\r\n\t\t\tcheckLogin()\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn originalNavigateTo.call(this, options)\r\n\t}\r\n\r\n\t// 拦截 switchTab\r\n\tconst originalSwitchTab = uni.switchTab\r\n\tuni.switchTab = function(options) {\r\n\t\tif (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {\r\n\t\t\tconsole.log('switchTab被拦截:', options.url, '登录状态:', isLoggedIn())\r\n\t\t\tcheckLogin()\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn originalSwitchTab.call(this, options)\r\n\t}\r\n\r\n\t// 拦截 reLaunch\r\n\tconst originalReLaunch = uni.reLaunch\r\n\tuni.reLaunch = function(options) {\r\n\t\tif (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {\r\n\t\t\tconsole.log('reLaunch被拦截:', options.url, '登录状态:', isLoggedIn())\r\n\t\t\tcheckLogin()\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn originalReLaunch.call(this, options)\r\n\t}\r\n\r\n\t// 拦截 redirectTo\r\n\tconst originalRedirectTo = uni.redirectTo\r\n\tuni.redirectTo = function(options) {\r\n\t\tif (isProtectedPage(options.url) && !isLoggedIn()) {\r\n\t\t\tcheckLogin()\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn originalRedirectTo.call(this, options)\r\n\t}\r\n}\r\n\r\n/**\r\n * 自动登录检查（应用启动时调用）\r\n */\r\nexport async function autoLoginCheck() {\r\n\tconst token = getToken()\r\n\tconst userInfo = getUserInfo()\r\n\r\n\tif (!token || !userInfo) {\r\n\t\t// 没有登录信息，跳转到登录页\r\n\t\tuni.reLaunch({\r\n\t\t\turl: '/pages/login/login'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n\r\n\t// 可以在这里验证token是否有效\r\n\t// 如果有后端API，可以调用验证接口\r\n\ttry {\r\n\t\t// 这里可以调用验证token的API\r\n\t\t// const res = await validateToken(token)\r\n\t\t// if (res.code !== 200) {\r\n\t\t//     removeToken()\r\n\t\t//     uni.reLaunch({\r\n\t\t//         url: '/pages/login/login'\r\n\t\t//     })\r\n\t\t//     return false\r\n\t\t// }\r\n\r\n\t\treturn true\r\n\t} catch (error) {\r\n\t\tconsole.error('Token验证失败:', error)\r\n\t\t// 验证失败，清除登录信息\r\n\t\tremoveToken()\r\n\t\tuni.reLaunch({\r\n\t\t\turl: '/pages/login/login'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n}", "// TODO(Babel 8): Remove this file.\n\nvar runtime = require('@babel/runtime/helpers/regeneratorRuntime')()\nmodule.exports = runtime\n", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _regeneratorRuntime() {\n  \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */\n  module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return e;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  var t,\n    e = {},\n    r = Object.prototype,\n    n = r.hasOwnProperty,\n    o = Object.defineProperty || function (t, e, r) {\n      t[e] = r.value;\n    },\n    i = \"function\" == typeof Symbol ? Symbol : {},\n    a = i.iterator || \"@@iterator\",\n    c = i.asyncIterator || \"@@asyncIterator\",\n    u = i.toStringTag || \"@@toStringTag\";\n  function define(t, e, r) {\n    return Object.defineProperty(t, e, {\n      value: r,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }), t[e];\n  }\n  try {\n    define({}, \"\");\n  } catch (t) {\n    define = function define(t, e, r) {\n      return t[e] = r;\n    };\n  }\n  function wrap(t, e, r, n) {\n    var i = e && e.prototype instanceof Generator ? e : Generator,\n      a = Object.create(i.prototype),\n      c = new Context(n || []);\n    return o(a, \"_invoke\", {\n      value: makeInvokeMethod(t, r, c)\n    }), a;\n  }\n  function tryCatch(t, e, r) {\n    try {\n      return {\n        type: \"normal\",\n        arg: t.call(e, r)\n      };\n    } catch (t) {\n      return {\n        type: \"throw\",\n        arg: t\n      };\n    }\n  }\n  e.wrap = wrap;\n  var h = \"suspendedStart\",\n    l = \"suspendedYield\",\n    f = \"executing\",\n    s = \"completed\",\n    y = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var p = {};\n  define(p, a, function () {\n    return this;\n  });\n  var d = Object.getPrototypeOf,\n    v = d && d(d(values([])));\n  v && v !== r && n.call(v, a) && (p = v);\n  var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p);\n  function defineIteratorMethods(t) {\n    [\"next\", \"throw\", \"return\"].forEach(function (e) {\n      define(t, e, function (t) {\n        return this._invoke(e, t);\n      });\n    });\n  }\n  function AsyncIterator(t, e) {\n    function invoke(r, o, i, a) {\n      var c = tryCatch(t[r], t, o);\n      if (\"throw\" !== c.type) {\n        var u = c.arg,\n          h = u.value;\n        return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) {\n          invoke(\"next\", t, i, a);\n        }, function (t) {\n          invoke(\"throw\", t, i, a);\n        }) : e.resolve(h).then(function (t) {\n          u.value = t, i(u);\n        }, function (t) {\n          return invoke(\"throw\", t, i, a);\n        });\n      }\n      a(c.arg);\n    }\n    var r;\n    o(this, \"_invoke\", {\n      value: function value(t, n) {\n        function callInvokeWithMethodAndArg() {\n          return new e(function (e, r) {\n            invoke(t, n, e, r);\n          });\n        }\n        return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(e, r, n) {\n    var o = h;\n    return function (i, a) {\n      if (o === f) throw Error(\"Generator is already running\");\n      if (o === s) {\n        if (\"throw\" === i) throw a;\n        return {\n          value: t,\n          done: !0\n        };\n      }\n      for (n.method = i, n.arg = a;;) {\n        var c = n.delegate;\n        if (c) {\n          var u = maybeInvokeDelegate(c, n);\n          if (u) {\n            if (u === y) continue;\n            return u;\n          }\n        }\n        if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) {\n          if (o === h) throw o = s, n.arg;\n          n.dispatchException(n.arg);\n        } else \"return\" === n.method && n.abrupt(\"return\", n.arg);\n        o = f;\n        var p = tryCatch(e, r, n);\n        if (\"normal\" === p.type) {\n          if (o = n.done ? s : l, p.arg === y) continue;\n          return {\n            value: p.arg,\n            done: n.done\n          };\n        }\n        \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(e, r) {\n    var n = r.method,\n      o = e.iterator[n];\n    if (o === t) return r.delegate = null, \"throw\" === n && e.iterator[\"return\"] && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y;\n    var i = tryCatch(o, e.iterator, r.arg);\n    if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y;\n    var a = i.arg;\n    return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y);\n  }\n  function pushTryEntry(t) {\n    var e = {\n      tryLoc: t[0]\n    };\n    1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e);\n  }\n  function resetTryEntry(t) {\n    var e = t.completion || {};\n    e.type = \"normal\", delete e.arg, t.completion = e;\n  }\n  function Context(t) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], t.forEach(pushTryEntry, this), this.reset(!0);\n  }\n  function values(e) {\n    if (e || \"\" === e) {\n      var r = e[a];\n      if (r) return r.call(e);\n      if (\"function\" == typeof e.next) return e;\n      if (!isNaN(e.length)) {\n        var o = -1,\n          i = function next() {\n            for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next;\n            return next.value = t, next.done = !0, next;\n          };\n        return i.next = i;\n      }\n    }\n    throw new TypeError(_typeof(e) + \" is not iterable\");\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: !0\n  }), o(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: !0\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) {\n    var e = \"function\" == typeof t && t.constructor;\n    return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name));\n  }, e.mark = function (t) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t;\n  }, e.awrap = function (t) {\n    return {\n      __await: t\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () {\n    return this;\n  }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) {\n    void 0 === i && (i = Promise);\n    var a = new AsyncIterator(wrap(t, r, n, o), i);\n    return e.isGeneratorFunction(r) ? a : a.next().then(function (t) {\n      return t.done ? t.value : a.next();\n    });\n  }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () {\n    return this;\n  }), define(g, \"toString\", function () {\n    return \"[object Generator]\";\n  }), e.keys = function (t) {\n    var e = Object(t),\n      r = [];\n    for (var n in e) r.push(n);\n    return r.reverse(), function next() {\n      for (; r.length;) {\n        var t = r.pop();\n        if (t in e) return next.value = t, next.done = !1, next;\n      }\n      return next.done = !0, next;\n    };\n  }, e.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function reset(e) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t);\n    },\n    stop: function stop() {\n      this.done = !0;\n      var t = this.tryEntries[0].completion;\n      if (\"throw\" === t.type) throw t.arg;\n      return this.rval;\n    },\n    dispatchException: function dispatchException(e) {\n      if (this.done) throw e;\n      var r = this;\n      function handle(n, o) {\n        return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o;\n      }\n      for (var o = this.tryEntries.length - 1; o >= 0; --o) {\n        var i = this.tryEntries[o],\n          a = i.completion;\n        if (\"root\" === i.tryLoc) return handle(\"end\");\n        if (i.tryLoc <= this.prev) {\n          var c = n.call(i, \"catchLoc\"),\n            u = n.call(i, \"finallyLoc\");\n          if (c && u) {\n            if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n            if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n          } else if (c) {\n            if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n          } else {\n            if (!u) throw Error(\"try statement without catch or finally\");\n            if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function abrupt(t, e) {\n      for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n        var o = this.tryEntries[r];\n        if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) {\n          var i = o;\n          break;\n        }\n      }\n      i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);\n      var a = i ? i.completion : {};\n      return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a);\n    },\n    complete: function complete(t, e) {\n      if (\"throw\" === t.type) throw t.arg;\n      return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y;\n    },\n    finish: function finish(t) {\n      for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n        var r = this.tryEntries[e];\n        if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y;\n      }\n    },\n    \"catch\": function _catch(t) {\n      for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n        var r = this.tryEntries[e];\n        if (r.tryLoc === t) {\n          var n = r.completion;\n          if (\"throw\" === n.type) {\n            var o = n.arg;\n            resetTryEntry(r);\n          }\n          return o;\n        }\n      }\n      throw Error(\"illegal catch attempt\");\n    },\n    delegateYield: function delegateYield(e, r, n) {\n      return this.delegate = {\n        iterator: values(e),\n        resultName: r,\n        nextLoc: n\n      }, \"next\" === this.method && (this.arg = t), y;\n    }\n  }, e;\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(undefined);\n    });\n  };\n}\nmodule.exports = _asyncToGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode, /* vue-cli only */\n  components, // fixed by xxxxxx auto components\n  renderjs // fixed by xxxxxx renderjs\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // fixed by xxxxxx auto components\n  if (components) {\n    if (!options.components) {\n      options.components = {}\n    }\n    var hasOwn = Object.prototype.hasOwnProperty\n    for (var name in components) {\n      if (hasOwn.call(components, name) && !hasOwn.call(options.components, name)) {\n        options.components[name] = components[name]\n      }\n    }\n  }\n  // fixed by xxxxxx renderjs\n  if (renderjs) {\n    if(typeof renderjs.beforeCreate === 'function'){\n\t\t\trenderjs.beforeCreate = [renderjs.beforeCreate]\n\t\t}\n    (renderjs.beforeCreate || (renderjs.beforeCreate = [])).unshift(function() {\n      this[renderjs.__module] = this\n    });\n    (options.mixins || (options.mixins = [])).push(renderjs)\n  }\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "uni.addInterceptor({\r\n  returnValue (res) {\r\n    if (!(!!res && (typeof res === \"object\" || typeof res === \"function\") && typeof res.then === \"function\")) {\r\n      return res;\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      res.then((res) => {\r\n        if (!res) return resolve(res) \r\n        return res[0] ? reject(res[0]) : resolve(res[1])\r\n      });\r\n    });\r\n  },\r\n});", "import http from '@/utils/request'\r\n\r\n// 签到相关API\r\nexport const signInApi = {\r\n\t// 获取签到配置\r\n\tgetSetting() {\r\n\t\treturn http.get('/sign_in/setting')\r\n\t},\r\n\r\n\t// 检查今天是否签到\r\n\tcheckSignInToday(tokenName) {\r\n\t\treturn http.get('/sign_in/check_sign_in', { tokenName })\r\n\t},\r\n\r\n\t// 获取签到历史\r\n\tgetHistory(tokenName, pageNum = 1, pageSize = 10) {\r\n\t\treturn http.get('/sign_in/history', {\r\n\t\t\ttokenName,\r\n\t\t\tpageNum,\r\n\t\t\tpageSize\r\n\t\t})\r\n\t},\r\n\r\n\t// 执行签到\r\n\tdoSignIn(tokenName) {\r\n\t\treturn http.post('/sign_in/do_sign_in', {\r\n\t\t\ttokenName: tokenName\r\n\t\t}, {\r\n\t\t\theader: {\r\n\t\t\t\t'Content-Type': 'application/x-www-form-urlencoded'\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 激活码相关API\r\nexport const tokenApi = {\r\n\t// 获取激活码额度信息\r\n\tgetQuota(tokenName) {\r\n\t\treturn http.get('/fq_token/get_quota', { tokenName })\r\n\t},\r\n\r\n\t// 获取激活码详细信息\r\n\tgetTokenInfo(tokenName) {\r\n\t\treturn http.get('/fq_token/selectByPage', {\r\n\t\t\ttokenName,\r\n\t\t\tpageNum: 1,\r\n\t\t\tpageSize: 1\r\n\t\t})\r\n\t},\r\n\r\n\t// 刷新额度\r\n\trefreshQuota(tokenName) {\r\n\t\treturn http.get('/fq_token/get_quota', { tokenName })\r\n\t},\r\n\r\n\t// 新增激活码\r\n\taddToken(tokenName, tokenTime) {\r\n\t\treturn http.put('/fq_token/insert', {\r\n\t\t\ttokenName,\r\n\t\t\ttokenTime\r\n\t\t})\r\n\t},\r\n\r\n\t// 获取用户当前token状态\r\n\tgetUserToken() {\r\n\t\treturn http.get('/fq_token/get_user_token')\r\n\t}\r\n}\r\n\r\n// 引入 Base64 工具\r\nimport { base64Encode as customBase64Encode, base64Decode as customBase64Decode } from '@/utils/base64'\r\n\r\n// 工具函数\r\nexport const utils = {\r\n\t// Base64 编码函数 (使用自定义实现)\r\n\tbase64Encode(str) {\r\n\t\ttry {\r\n\t\t\t// 优先使用原生函数\r\n\t\t\tif (typeof uni.base64Encode === 'function') {\r\n\t\t\t\treturn uni.base64Encode(str)\r\n\t\t\t}\r\n\r\n\t\t\t// 使用自定义 Base64 实现\r\n\t\t\treturn customBase64Encode(String(str))\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Base64编码失败:', error)\r\n\t\t\treturn String(str)\r\n\t\t}\r\n\t},\r\n\r\n\t// Base64 解码函数 (使用自定义实现)\r\n\tbase64Decode(str) {\r\n\t\ttry {\r\n\t\t\t// 优先使用原生函数\r\n\t\t\tif (typeof uni.base64Decode === 'function') {\r\n\t\t\t\treturn uni.base64Decode(str)\r\n\t\t\t}\r\n\r\n\t\t\t// 使用自定义 Base64 实现\r\n\t\t\treturn customBase64Decode(String(str))\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Base64解码失败:', error)\r\n\t\t\treturn String(str)\r\n\t\t}\r\n\t},\r\n\r\n\t// 简单编码函数 (十六进制编码，作为备用方案)\r\n\tsimpleEncode(str) {\r\n\t\ttry {\r\n\t\t\tstr = String(str)\r\n\t\t\tlet result = ''\r\n\t\t\tfor (let i = 0; i < str.length; i++) {\r\n\t\t\t\tconst hex = str.charCodeAt(i).toString(16)\r\n\t\t\t\tresult += hex.padStart(2, '0')\r\n\t\t\t}\r\n\t\t\treturn result\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('简单编码失败:', error)\r\n\t\t\treturn String(str)\r\n\t\t}\r\n\t},\r\n\r\n\t// 简单解码函数 (十六进制解码，作为备用方案)\r\n\tsimpleDecode(hexStr) {\r\n\t\ttry {\r\n\t\t\thexStr = String(hexStr)\r\n\t\t\tlet result = ''\r\n\t\t\tfor (let i = 0; i < hexStr.length; i += 2) {\r\n\t\t\t\tconst hex = hexStr.substr(i, 2)\r\n\t\t\t\tconst charCode = parseInt(hex, 16)\r\n\t\t\t\tif (!isNaN(charCode) && charCode > 0) {\r\n\t\t\t\t\tresult += String.fromCharCode(charCode)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn result\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('简单解码失败:', error)\r\n\t\t\treturn String(hexStr)\r\n\t\t}\r\n\t},\r\n\r\n\r\n\t// 加密函数 (使用 Base64 编码)\r\n\tencrypt(data, key = 'default_key', iv = 'default_iv') {\r\n\t\ttry {\r\n\t\t\t// 处理对象类型\r\n\t\t\tif (typeof data === \"object\") {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tdata = JSON.stringify(data)\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log(\"JSON序列化失败:\", error)\r\n\t\t\t\t\treturn data\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 转换为字符串\r\n\t\t\tconst dataStr = String(data)\r\n\r\n\t\t\t// 使用异或加密\r\n\t\t\tconst keyStr = key + iv\r\n\t\t\tlet encrypted = ''\r\n\r\n\t\t\tfor (let i = 0; i < dataStr.length; i++) {\r\n\t\t\t\tconst dataChar = dataStr.charCodeAt(i)\r\n\t\t\t\tconst keyChar = keyStr.charCodeAt(i % keyStr.length)\r\n\t\t\t\t// 异或运算加密\r\n\t\t\t\tconst encryptedChar = dataChar ^ keyChar\r\n\t\t\t\tencrypted += String.fromCharCode(encryptedChar)\r\n\t\t\t}\r\n\r\n\t\t\t// 使用 Base64 编码\r\n\t\t\treturn this.base64Encode(encrypted)\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('加密失败:', error)\r\n\t\t\t// 降级到简单编码\r\n\t\t\ttry {\r\n\t\t\t\treturn this.simpleEncode(String(data))\r\n\t\t\t} catch (fallbackError) {\r\n\t\t\t\tconsole.error('降级编码也失败:', fallbackError)\r\n\t\t\t\treturn String(data)\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\r\n\t// 解密函数 (使用 Base64 解码)\r\n\tdecrypt(encryptedData, key = 'default_key', iv = 'default_iv') {\r\n\t\ttry {\r\n\t\t\tif (!encryptedData) return ''\r\n\r\n\t\t\t// 使用 Base64 解码\r\n\t\t\tlet decoded\r\n\t\t\ttry {\r\n\t\t\t\tdecoded = this.base64Decode(encryptedData)\r\n\t\t\t} catch (base64Error) {\r\n\t\t\t\tconsole.error('Base64解码失败，尝试简单解码:', base64Error)\r\n\t\t\t\t// 降级到简单解码\r\n\t\t\t\tdecoded = this.simpleDecode(encryptedData)\r\n\t\t\t}\r\n\r\n\t\t\tif (!decoded) return encryptedData\r\n\r\n\t\t\t// 使用相同的密钥解密\r\n\t\t\tconst keyStr = key + iv\r\n\t\t\tlet decrypted = ''\r\n\r\n\t\t\tfor (let i = 0; i < decoded.length; i++) {\r\n\t\t\t\tconst encryptedChar = decoded.charCodeAt(i)\r\n\t\t\t\tconst keyChar = keyStr.charCodeAt(i % keyStr.length)\r\n\t\t\t\t// 异或运算解密\r\n\t\t\t\tconst decryptedChar = encryptedChar ^ keyChar\r\n\t\t\t\tdecrypted += String.fromCharCode(decryptedChar)\r\n\t\t\t}\r\n\r\n\t\t\treturn decrypted\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('解密失败:', error)\r\n\t\t\treturn String(encryptedData)\r\n\t\t}\r\n\t},\r\n\r\n\r\n\r\n\t// 获取当前时间字符串\r\n\tgetCurrentTimeString() {\r\n\t\tconst now = new Date()\r\n\t\tconst year = now.getFullYear()\r\n\t\tconst month = String(now.getMonth() + 1).padStart(2, '0')\r\n\t\tconst day = String(now.getDate()).padStart(2, '0')\r\n\t\tconst hours = String(now.getHours()).padStart(2, '0')\r\n\t\tconst minutes = String(now.getMinutes()).padStart(2, '0')\r\n\t\tconst seconds = String(now.getSeconds()).padStart(2, '0')\r\n\r\n\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n\t},\r\n\r\n\t// 复制文本到剪贴板\r\n\tcopyText(text) {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tuni.setClipboardData({\r\n\t\t\t\tdata: text,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '复制成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\tresolve()\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\treject(err)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\t},\r\n\r\n\t// 格式化日期\r\n\tformatDate(date, format = 'YYYY-MM-DD') {\r\n\t\tconst d = new Date(date)\r\n\t\tconst year = d.getFullYear()\r\n\t\tconst month = String(d.getMonth() + 1).padStart(2, '0')\r\n\t\tconst day = String(d.getDate()).padStart(2, '0')\r\n\t\tconst hours = String(d.getHours()).padStart(2, '0')\r\n\t\tconst minutes = String(d.getMinutes()).padStart(2, '0')\r\n\t\tconst seconds = String(d.getSeconds()).padStart(2, '0')\r\n\r\n\t\treturn format\r\n\t\t\t.replace('YYYY', year)\r\n\t\t\t.replace('MM', month)\r\n\t\t\t.replace('DD', day)\r\n\t\t\t.replace('HH', hours)\r\n\t\t\t.replace('mm', minutes)\r\n\t\t\t.replace('ss', seconds)\r\n\t}\r\n}\r\n", "// 网络请求封装\r\nimport config from '@/config/index'\r\nimport { removeToken } from '@/utils/auth'\r\n\r\nconst BASE_URL = config.baseURL\r\n\r\n// 请求拦截器\r\nconst request = (options = {}) => {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// 显示加载提示\r\n\t\tif (options.showLoading !== false) {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中...',\r\n\t\t\t\tmask: true\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\t// 获取token (与auth.js保持一致)\r\n\t\tconst token = uni.getStorageSync('user_token') || ''\r\n\r\n\t\t// 检查 API 地址是否已配置\r\n\t\tif (BASE_URL.includes('your-api-domain.com')) {\r\n\t\t\t// 隐藏加载提示\r\n\t\t\tif (options.showLoading !== false) {\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t}\r\n\r\n\t\t\tconst errorMsg = '请先配置正确的 API 地址'\r\n\t\t\tif (options.showError !== false) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '配置提示',\r\n\t\t\t\t\tcontent: '请在 config/index.js 文件中配置正确的 API 基础地址后再使用',\r\n\t\t\t\t\tshowCancel: false\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\treject(new Error(errorMsg))\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// 设置默认配置\r\n\t\tconst requestConfig = {\r\n\t\t\turl: BASE_URL + options.url,\r\n\t\t\tmethod: options.method || 'GET',\r\n\t\t\tdata: options.data || {},\r\n\t\t\theader: {\r\n\t\t\t\t'Content-Type': 'application/json',\r\n\t\t\t\t'Authorization': token ? `Bearer ${token}` : '',\r\n\t\t\t\t...options.header\r\n\t\t\t},\r\n\t\t\ttimeout: options.timeout || 10000\r\n\t\t}\r\n\r\n\t\t// 处理form-data格式\r\n\t\tif (options.header && options.header['Content-Type'] === 'application/x-www-form-urlencoded') {\r\n\t\t\t// 将data转换为form-data格式\r\n\t\t\tif (requestConfig.data && typeof requestConfig.data === 'object') {\r\n\t\t\t\tconst formData = Object.keys(requestConfig.data)\r\n\t\t\t\t\t.filter(key => requestConfig.data[key] !== undefined && requestConfig.data[key] !== null)\r\n\t\t\t\t\t.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(requestConfig.data[key])}`)\r\n\t\t\t\t\t.join('&')\r\n\t\t\t\trequestConfig.data = formData\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 处理URL参数（支持GET和POST请求）\r\n\t\tif (options.params) {\r\n\t\t\t// 手动构建查询字符串，兼容微信小程序环境\r\n\t\t\tconst params = Object.keys(options.params)\r\n\t\t\t\t.filter(key => options.params[key] !== undefined && options.params[key] !== null)\r\n\t\t\t\t.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options.params[key])}`)\r\n\t\t\t\t.join('&')\r\n\r\n\t\t\tif (params) {\r\n\t\t\t\trequestConfig.url += (requestConfig.url.includes('?') ? '&' : '?') + params\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 发起请求\r\n\t\tuni.request({\r\n\t\t\t...requestConfig,\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\t// 隐藏加载提示\r\n\t\t\t\tif (options.showLoading !== false) {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 处理响应数据\r\n\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t// 根据业务逻辑处理响应\r\n\t\t\t\t\tif (res.data.code === 200) {\r\n\t\t\t\t\t\tresolve(res.data)\r\n\t\t\t\t\t} else if (res.data.code === 401) {\r\n\t\t\t\t\t\t// 401未授权，清除token并跳转到登录页\r\n\t\t\t\t\t\tconsole.log('收到401响应，清除token并跳转到登录页')\r\n\t\t\t\t\t\tremoveToken()\r\n\r\n\t\t\t\t\t\tconst errorMsg = res.data.msg || '登录已过期，请重新登录'\r\n\t\t\t\t\t\tif (options.showError !== false) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 延迟跳转到登录页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1500)\r\n\r\n\t\t\t\t\t\treject(new Error(errorMsg))\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他业务错误\r\n\t\t\t\t\t\tconst errorMsg = res.data.msg || res.data.message || '请求失败'\r\n\t\t\t\t\t\tif (options.showError !== false) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treject(new Error(errorMsg))\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (res.statusCode === 401) {\r\n\t\t\t\t\t// HTTP 401未授权\r\n\t\t\t\t\tconsole.log('收到HTTP 401响应，清除token并跳转到登录页')\r\n\t\t\t\t\tremoveToken()\r\n\r\n\t\t\t\t\tconst errorMsg = '登录已过期，请重新登录'\r\n\t\t\t\t\tif (options.showError !== false) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 延迟跳转到登录页\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 1500)\r\n\r\n\t\t\t\t\treject(new Error(errorMsg))\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 其他HTTP错误\r\n\t\t\t\t\tconst errorMsg = `请求失败 (${res.statusCode})`\r\n\t\t\t\t\tif (options.showError !== false) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\treject(new Error(errorMsg))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\t// 隐藏加载提示\r\n\t\t\t\tif (options.showLoading !== false) {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 网络错误\r\n\t\t\t\tconst errorMsg = err.errMsg || '网络连接失败'\r\n\t\t\t\tif (options.showError !== false) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\treject(err)\r\n\t\t\t}\r\n\t\t})\r\n\t})\r\n}\r\n\r\n// 封装常用请求方法\r\nconst http = {\r\n\tget: (url, params = {}, options = {}) => {\r\n\t\treturn request({\r\n\t\t\turl,\r\n\t\t\tmethod: 'GET',\r\n\t\t\tparams,\r\n\t\t\t...options\r\n\t\t})\r\n\t},\r\n\r\n\tpost: (url, data = {}, options = {}) => {\r\n\t\treturn request({\r\n\t\t\turl,\r\n\t\t\tmethod: 'POST',\r\n\t\t\tdata,\r\n\t\t\t...options\r\n\t\t})\r\n\t},\r\n\r\n\tput: (url, data = {}, options = {}) => {\r\n\t\treturn request({\r\n\t\t\turl,\r\n\t\t\tmethod: 'PUT',\r\n\t\t\tdata,\r\n\t\t\t...options\r\n\t\t})\r\n\t},\r\n\r\n\tdelete: (url, params = {}, options = {}) => {\r\n\t\treturn request({\r\n\t\t\turl,\r\n\t\t\tmethod: 'DELETE',\r\n\t\t\tparams,\r\n\t\t\t...options\r\n\t\t})\r\n\t}\r\n}\r\n\r\nexport default http\r\n", "// 应用配置文件\r\nconst config = {\r\n\t// 开发环境配置\r\n\tdevelopment: {\r\n\t\t// API 基础地址 - 暂时使用占位符以启用mock API\r\n\t\tbaseURL: 'http://localhost:8080',\r\n\t\t// 是否启用调试模式\r\n\t\tdebug: true,\r\n\t\t// 请求超时时间\r\n\t\ttimeout: 10000\r\n\t},\r\n\r\n\t// 生产环境配置\r\n\tproduction: {\r\n\t\t// API 基础地址 - 暂时使用占位符以启用mock API\r\n\t\tbaseURL: 'http://localhost:8080',\r\n\t\t// 是否启用调试模式\r\n\t\tdebug: false,\r\n\t\t// 请求超时时间\r\n\t\ttimeout: 10000\r\n\t}\r\n}\r\n\r\n// 根据环境获取配置\r\nfunction getConfig() {\r\n\t// 在 uniapp 中判断环境\r\n\tconst env = process.env.NODE_ENV || 'development'\r\n\treturn config[env] || config.development\r\n}\r\n\r\nexport default getConfig()", "// UTF-8 编码辅助函数\r\nfunction stringToUtf8Bytes(str) {\r\n\tconst bytes = [];\r\n\tfor (let i = 0; i < str.length; i++) {\r\n\t\tlet code = str.charCodeAt(i);\r\n\t\tif (code < 0x80) {\r\n\t\t\tbytes.push(code);\r\n\t\t} else if (code < 0x800) {\r\n\t\t\tbytes.push(0xc0 | (code >> 6));\r\n\t\t\tbytes.push(0x80 | (code & 0x3f));\r\n\t\t} else if ((code & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\r\n\t\t\t// 处理代理对（高代理 + 低代理）\r\n\t\t\tconst hi = code;\r\n\t\t\tconst lo = str.charCodeAt(++i);\r\n\t\t\tcode = 0x10000 + (((hi & 0x3ff) << 10) | (lo & 0x3ff));\r\n\t\t\tbytes.push(0xf0 | (code >> 18));\r\n\t\t\tbytes.push(0x80 | ((code >> 12) & 0x3f));\r\n\t\t\tbytes.push(0x80 | ((code >> 6) & 0x3f));\r\n\t\t\tbytes.push(0x80 | (code & 0x3f));\r\n\t\t} else {\r\n\t\t\tbytes.push(0xe0 | (code >> 12));\r\n\t\t\tbytes.push(0x80 | ((code >> 6) & 0x3f));\r\n\t\t\tbytes.push(0x80 | (code & 0x3f));\r\n\t\t}\r\n\t}\r\n\treturn bytes;\r\n}\r\n\r\n// UTF-8 解码辅助函数\r\nfunction utf8BytesToString(bytes) {\r\n\tlet result = '';\r\n\tlet i = 0;\r\n\twhile (i < bytes.length) {\r\n\t\tlet byte1 = bytes[i++];\r\n\r\n\t\t// 检查字节是否有效\r\n\t\tif (byte1 === undefined) break;\r\n\r\n\t\tif (byte1 < 0x80) {\r\n\t\t\t// 单字节字符 (ASCII)\r\n\t\t\tresult += String.fromCharCode(byte1);\r\n\t\t} else if ((byte1 >> 5) === 0x06) {\r\n\t\t\t// 双字节字符\r\n\t\t\tlet byte2 = bytes[i++];\r\n\t\t\tif (byte2 === undefined) break;\r\n\t\t\tresult += String.fromCharCode(((byte1 & 0x1f) << 6) | (byte2 & 0x3f));\r\n\t\t} else if ((byte1 >> 4) === 0x0e) {\r\n\t\t\t// 三字节字符\r\n\t\t\tlet byte2 = bytes[i++];\r\n\t\t\tlet byte3 = bytes[i++];\r\n\t\t\tif (byte2 === undefined || byte3 === undefined) break;\r\n\t\t\tresult += String.fromCharCode(((byte1 & 0x0f) << 12) | ((byte2 & 0x3f) << 6) | (byte3 & 0x3f));\r\n\t\t} else if ((byte1 >> 3) === 0x1e) {\r\n\t\t\t// 四字节字符 (代理对)\r\n\t\t\tlet byte2 = bytes[i++];\r\n\t\t\tlet byte3 = bytes[i++];\r\n\t\t\tlet byte4 = bytes[i++];\r\n\t\t\tif (byte2 === undefined || byte3 === undefined || byte4 === undefined) break;\r\n\r\n\t\t\tlet codePoint = ((byte1 & 0x07) << 18) | ((byte2 & 0x3f) << 12) | ((byte3 & 0x3f) << 6) | (byte4 & 0x3f);\r\n\t\t\tcodePoint -= 0x10000;\r\n\t\t\tresult += String.fromCharCode(0xd800 + (codePoint >> 10));\r\n\t\t\tresult += String.fromCharCode(0xdc00 + (codePoint & 0x3ff));\r\n\t\t} else {\r\n\t\t\t// 无效的UTF-8字节，跳过\r\n\t\t\tconsole.warn('无效的UTF-8字节:', byte1.toString(16));\r\n\t\t}\r\n\t}\r\n\treturn result;\r\n}\r\n\r\n// 实现Base64加密\r\nfunction base64Encode(str) {\r\n\tlet base64 = new Base64();\r\n\treturn base64.encode(str);\r\n}\r\n\r\n// 实现Base64解密\r\nfunction base64Decode(str) {\r\n\tlet base64 = new Base64();\r\n\treturn base64.decode(str);\r\n}\r\n\r\n// 定义Base64对象\r\nfunction Base64() {\r\n\r\n\t// Base64字符集\r\n\tconst base64Chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\r\n\r\n\t// 编码函数 - 支持 Unicode\r\n\tthis.encode = function (str) {\r\n\t\t// 先将字符串转换为 UTF-8 字节数组\r\n\t\tconst utf8Bytes = stringToUtf8Bytes(str);\r\n\r\n\t\tlet result = '';\r\n\t\tfor (let i = 0; i < utf8Bytes.length; i += 3) {\r\n\t\t\tlet a = utf8Bytes[i];\r\n\t\t\tlet b = i + 1 < utf8Bytes.length ? utf8Bytes[i + 1] : 0;\r\n\t\t\tlet c = i + 2 < utf8Bytes.length ? utf8Bytes[i + 2] : 0;\r\n\r\n\t\t\tlet a1 = a >> 2,\r\n\t\t\t\ta2 = ((a & 3) << 4) | (b >> 4),\r\n\t\t\t\ta3 = ((b & 15) << 2) | (c >> 6),\r\n\t\t\t\ta4 = c & 63;\r\n\r\n\t\t\tresult += base64Chars[a1] + base64Chars[a2] +\r\n\t\t\t\t(i + 1 < utf8Bytes.length ? base64Chars[a3] : '=') +\r\n\t\t\t\t(i + 2 < utf8Bytes.length ? base64Chars[a4] : '=');\r\n\t\t}\r\n\t\treturn result;\r\n\t}\r\n\r\n\t// 解码函数 - 支持 Unicode\r\n\tthis.decode = function (str) {\r\n\t\t// 保存原始字符串用于填充检查\r\n\t\tconst originalStr = str;\r\n\r\n\t\t// 移除非Base64字符，但保留填充字符用于后续处理\r\n\t\tstr = str.replace(/[^A-Za-z0-9+/=]/g, '');\r\n\r\n\t\tconst bytes = [];\r\n\t\tlet i = 0;\r\n\r\n\t\twhile (i < str.length) {\r\n\t\t\tlet a = base64Chars.indexOf(str.charAt(i++));\r\n\t\t\tlet b = base64Chars.indexOf(str.charAt(i++));\r\n\t\t\tlet c = base64Chars.indexOf(str.charAt(i++));\r\n\t\t\tlet d = base64Chars.indexOf(str.charAt(i++));\r\n\r\n\t\t\t// 处理无效字符\r\n\t\t\tif (a === -1 || b === -1) break;\r\n\r\n\t\t\t// 处理填充字符\r\n\t\t\tlet hasPadding1 = str.charAt(i - 2) === '=';\r\n\t\t\tlet hasPadding2 = str.charAt(i - 1) === '=';\r\n\r\n\t\t\tif (c === -1) c = 0;\r\n\t\t\tif (d === -1) d = 0;\r\n\r\n\t\t\tlet a1 = (a << 2) | (b >> 4);\r\n\t\t\tlet a2 = ((b & 15) << 4) | (c >> 2);\r\n\t\t\tlet a3 = ((c & 3) << 6) | d;\r\n\r\n\t\t\tbytes.push(a1);\r\n\r\n\t\t\t// 根据填充字符决定是否添加后续字节\r\n\t\t\tif (!hasPadding1) {\r\n\t\t\t\tbytes.push(a2);\r\n\t\t\t}\r\n\t\t\tif (!hasPadding2 && !hasPadding1) {\r\n\t\t\t\tbytes.push(a3);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 将字节数组转换回 UTF-8 字符串\r\n\t\treturn utf8BytesToString(bytes);\r\n\t}\r\n}\r\n\r\n// 向外暴露方法 (ES6 模块语法)\r\nexport {\r\n\tbase64Encode,\r\n\tbase64Decode\r\n}", "import http from '@/utils/request'\r\n\r\n// 登录方法\r\nexport function loginApi(username, password, code, uuid) {\r\n\tconst data = {\r\n\t\tusername,\r\n\t\tpassword,\r\n\t\tcode,\r\n\t\tuuid\r\n\t}\r\n\r\n\treturn http.post('/login', data, {\r\n\t\theader: {\r\n\t\t\tisToken: false,\r\n\t\t\trepeatSubmit: false\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 注册方法\r\nexport function register(data) {\r\n\treturn http.post('/register', data, {\r\n\t\theader: {\r\n\t\t\tisToken: false\r\n\t\t},\r\n\t\tshowError: false // 禁用自动错误提示，由页面手动处理\r\n\t})\r\n}\r\n\r\n// 获取用户详细信息\r\nexport function getUserInfo() {\r\n\treturn http.get('/getInfo')\r\n}\r\n\r\n// 退出方法\r\nexport function logout() {\r\n\treturn http.post('/logout')\r\n}\r\n\r\n// 获取验证码\r\nexport function getCodeImg() {\r\n\treturn http.get('/captchaImage', {}, {\r\n\t\theader: {\r\n\t\t\tisToken: false\r\n\t\t},\r\n\t\ttimeout: 20000\r\n\t})\r\n}\r\n\r\n// 得到登录页面配置\r\nexport function loginInfo() {\r\n\treturn http.get('/login/info', {}, {\r\n\t\theader: {\r\n\t\t\tisToken: false\r\n\t\t},\r\n\t\ttimeout: 20000\r\n\t})\r\n}\r\n\r\n// 发送短信验证码\r\nexport function sendSmsCode(phone) {\r\n\treturn http.get('/login/captcha', {\r\n\t\tphone\r\n\t}, {\r\n\t\theader: {\r\n\t\t\tisToken: false\r\n\t\t},\r\n\t\ttimeout: 20000\r\n\t})\r\n}\r\n\r\n// 手机登录发送短信验证码\r\nexport function sendPhoneCode(phone) {\r\n\treturn http.get('/login/loginCaptcha', {\r\n\t\tphone\r\n\t}, {\r\n\t\theader: {\r\n\t\t\tisToken: false\r\n\t\t},\r\n\t\ttimeout: 20000\r\n\t})\r\n}\r\n\r\n//手机登录验证码验证\r\nexport function phoneLogin(phone, code) {\r\n\treturn http.post('/login/smsLogin', {\r\n\t\tphoneNumber: phone,\r\n\t\tsmsCode: code\r\n\t}, {\r\n\t\theader: {\r\n\t\t\tisToken: false\r\n\t\t},\r\n\t\ttimeout: 20000\r\n\t})\r\n}"], "sourceRoot": ""}