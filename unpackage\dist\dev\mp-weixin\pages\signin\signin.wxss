







































































































































































































































































































































































































































































































/* 页面容器 */
.container.data-v-23701386 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
}
/* 页面头部 */
.header.data-v-23701386 {
	text-align: center;
	padding: 40rpx 0;
	color: #ffffff;
}
.header-title.data-v-23701386 {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.header-subtitle.data-v-23701386 {
	font-size: 28rpx;
	opacity: 0.9;
}
/* 卡片样式 */
.card.data-v-23701386 {
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 30rpx;
	overflow: hidden;
	transition: all 0.3s ease;
}
.card.data-v-23701386:hover {
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}
.fade-in.data-v-23701386 {
	-webkit-animation: fadeInUp-data-v-23701386 0.6s ease-out;
	        animation: fadeInUp-data-v-23701386 0.6s ease-out;
}
@-webkit-keyframes fadeInUp-data-v-23701386 {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-23701386 {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
.card-header.data-v-23701386 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
}
.card-title.data-v-23701386 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.card-content.data-v-23701386 {
	padding: 30rpx;
}
/* 标签样式 */
.tag.data-v-23701386 {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: bold;
}
.tag-success.data-v-23701386 {
	background-color: #f0f9eb;
	color: #67C23A;
	border: 1rpx solid #c2e7b0;
}
.tag-info.data-v-23701386 {
	background-color: #f4f4f5;
	color: #909399;
	border: 1rpx solid #d3d4d6;
}
/* 按钮样式 */
.btn.data-v-23701386 {
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	border: none;
	font-size: 28rpx;
	font-weight: bold;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.btn.data-v-23701386:hover {
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}
.btn-primary.data-v-23701386 {
	background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
	color: #ffffff;
}
.btn-secondary.data-v-23701386 {
	background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
	color: #ffffff;
}
.btn-disabled.data-v-23701386 {
	background-color: #c0c4cc;
	color: #ffffff;
	cursor: not-allowed;
	opacity: 0.6;
}
.btn-disabled.data-v-23701386:hover {
	-webkit-transform: none;
	        transform: none;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
/* 签到信息 */
.signin-info.data-v-23701386 {
	margin-bottom: 30rpx;
}
.signin-stats.data-v-23701386 {
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
}
.stat-item.data-v-23701386 {
	text-align: center;
	flex: 1;
}
.stat-number.data-v-23701386 {
	font-size: 48rpx;
	font-weight: bold;
	color: #409EFF;
	margin-bottom: 10rpx;
}
.stat-label.data-v-23701386 {
	font-size: 24rpx;
	color: #666666;
}
.signin-tip.data-v-23701386 {
	text-align: center;
	font-size: 26rpx;
	color: #E6A23C;
	background-color: #fdf6ec;
	padding: 15rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #E6A23C;
}
/* 日历样式 */
.calendar-container.data-v-23701386 {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
}
.calendar-header.data-v-23701386 {
	text-align: center;
	margin-bottom: 20rpx;
}
.calendar-title.data-v-23701386 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.calendar-weekdays.data-v-23701386 {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 10rpx;
	margin-bottom: 15rpx;
	padding-bottom: 15rpx;
	border-bottom: 1rpx solid #e0e0e0;
}
.weekday.data-v-23701386 {
	text-align: center;
	font-size: 24rpx;
	font-weight: bold;
	color: #666666;
	padding: 10rpx 0;
}
.calendar-dates.data-v-23701386 {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 10rpx;
}
.calendar-date.data-v-23701386 {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	position: relative;
	margin: 0 auto;
	font-size: 26rpx;
	transition: all 0.3s ease;
}
.calendar-date.empty.data-v-23701386 {
	visibility: hidden;
}
.calendar-date.signed.data-v-23701386 {
	background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
	color: #ffffff;
	box-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.3);
}
.calendar-date.today.data-v-23701386 {
	border: 3rpx solid #409EFF;
	font-weight: bold;
	color: #409EFF;
}
.calendar-date.disabled.data-v-23701386 {
	color: #c0c4cc;
}
.signed-mark.data-v-23701386 {
	position: absolute;
	font-size: 20rpx;
	font-weight: bold;
}
/* 签到按钮 */
.signin-btn.data-v-23701386 {
	width: 100%;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	box-shadow: 0 6rpx 20rpx rgba(64, 158, 255, 0.3);
}
/* 签到历史 */
.signin-history.data-v-23701386 {
	margin-top: 20rpx;
}
.history-header.data-v-23701386 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.history-title.data-v-23701386 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
}
.history-more.data-v-23701386 {
	font-size: 24rpx;
	color: #409EFF;
	padding: 10rpx;
}
.history-list.data-v-23701386 {
	max-height: 400rpx;
	overflow-y: auto;
}
.history-item.data-v-23701386 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	margin-bottom: 15rpx;
	border-left: 4rpx solid #409EFF;
}
.history-item.extra-reward.data-v-23701386 {
	border-left-color: #67C23A;
	background: linear-gradient(90deg, #f0f9eb 0%, #f8f9fa 100%);
}
.history-content.data-v-23701386 {
	display: flex;
	align-items: center;
	gap: 15rpx;
}
.history-reward.data-v-23701386 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
}
.history-time.data-v-23701386 {
	font-size: 24rpx;
	color: #999999;
}
.no-data.data-v-23701386 {
	text-align: center;
	color: #999999;
	padding: 60rpx 0;
	font-size: 28rpx;
}
/* 无用户提示 */
.no-user-tip.data-v-23701386 {
	text-align: center;
	padding: 40rpx 20rpx;
}
.tip-icon.data-v-23701386 {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}
.tip-text.data-v-23701386 {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 30rpx;
	line-height: 1.5;
}
/* 导航按钮 */
.nav-buttons.data-v-23701386 {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}
.nav-btn.data-v-23701386 {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 30rpx;
}
/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.signin-stats.data-v-23701386 {
		flex-direction: column;
		gap: 20rpx;
}
}
@media screen and (max-width: 600rpx) {
.calendar-dates.data-v-23701386 {
		gap: 5rpx;
}
.calendar-date.data-v-23701386 {
		width: 50rpx;
		height: 50rpx;
		font-size: 24rpx;
}
}

