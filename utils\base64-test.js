// UTF-8 编码辅助函数
function stringToUtf8Bytes(str) {
	const bytes = [];
	for (let i = 0; i < str.length; i++) {
		let code = str.charCodeAt(i);
		if (code < 0x80) {
			bytes.push(code);
		} else if (code < 0x800) {
			bytes.push(0xc0 | (code >> 6));
			bytes.push(0x80 | (code & 0x3f));
		} else if ((code & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {
			// 处理代理对（高代理 + 低代理）
			const hi = code;
			const lo = str.charCodeAt(++i);
			code = 0x10000 + (((hi & 0x3ff) << 10) | (lo & 0x3ff));
			bytes.push(0xf0 | (code >> 18));
			bytes.push(0x80 | ((code >> 12) & 0x3f));
			bytes.push(0x80 | ((code >> 6) & 0x3f));
			bytes.push(0x80 | (code & 0x3f));
		} else {
			bytes.push(0xe0 | (code >> 12));
			bytes.push(0x80 | ((code >> 6) & 0x3f));
			bytes.push(0x80 | (code & 0x3f));
		}
	}
	return bytes;
}

// UTF-8 解码辅助函数
function utf8BytesToString(bytes) {
	let result = '';
	let i = 0;
	while (i < bytes.length) {
		let byte1 = bytes[i++];
		
		// 检查字节是否有效
		if (byte1 === undefined) break;
		
		if (byte1 < 0x80) {
			// 单字节字符 (ASCII)
			result += String.fromCharCode(byte1);
		} else if ((byte1 >> 5) === 0x06) {
			// 双字节字符
			let byte2 = bytes[i++];
			if (byte2 === undefined) break;
			result += String.fromCharCode(((byte1 & 0x1f) << 6) | (byte2 & 0x3f));
		} else if ((byte1 >> 4) === 0x0e) {
			// 三字节字符
			let byte2 = bytes[i++];
			let byte3 = bytes[i++];
			if (byte2 === undefined || byte3 === undefined) break;
			result += String.fromCharCode(((byte1 & 0x0f) << 12) | ((byte2 & 0x3f) << 6) | (byte3 & 0x3f));
		} else if ((byte1 >> 3) === 0x1e) {
			// 四字节字符 (代理对)
			let byte2 = bytes[i++];
			let byte3 = bytes[i++];
			let byte4 = bytes[i++];
			if (byte2 === undefined || byte3 === undefined || byte4 === undefined) break;
			
			let codePoint = ((byte1 & 0x07) << 18) | ((byte2 & 0x3f) << 12) | ((byte3 & 0x3f) << 6) | (byte4 & 0x3f);
			codePoint -= 0x10000;
			result += String.fromCharCode(0xd800 + (codePoint >> 10));
			result += String.fromCharCode(0xdc00 + (codePoint & 0x3ff));
		} else {
			// 无效的UTF-8字节，跳过
			console.warn('无效的UTF-8字节:', byte1.toString(16));
		}
	}
	return result;
}

// 实现Base64加密
function base64Encode(str) {
	let base64 = new Base64();
	return base64.encode(str);
}

// 实现Base64解密
function base64Decode(str) {
	let base64 = new Base64();
	return base64.decode(str);
}

// 定义Base64对象
function Base64() {

	// Base64字符集
	const base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

	// 编码函数 - 支持 Unicode
	this.encode = function(str) {
		// 先将字符串转换为 UTF-8 字节数组
		const utf8Bytes = stringToUtf8Bytes(str);

		let result = '';
		for (let i = 0; i < utf8Bytes.length; i += 3) {
			let a = utf8Bytes[i];
			let b = i + 1 < utf8Bytes.length ? utf8Bytes[i + 1] : 0;
			let c = i + 2 < utf8Bytes.length ? utf8Bytes[i + 2] : 0;

			let a1 = a >> 2,
				a2 = ((a & 3) << 4) | (b >> 4),
				a3 = ((b & 15) << 2) | (c >> 6),
				a4 = c & 63;

			result += base64Chars[a1] + base64Chars[a2] +
				(i + 1 < utf8Bytes.length ? base64Chars[a3] : '=') +
				(i + 2 < utf8Bytes.length ? base64Chars[a4] : '=');
		}
		return result;
	}

	// 解码函数 - 支持 Unicode
	this.decode = function (str) {
		// 保存原始字符串用于填充检查
		const originalStr = str;

		// 移除非Base64字符，但保留填充字符用于后续处理
		str = str.replace(/[^A-Za-z0-9+/=]/g, '');

		const bytes = [];
		let i = 0;

		while (i < str.length) {
			let a = base64Chars.indexOf(str.charAt(i++));
			let b = base64Chars.indexOf(str.charAt(i++));
			let c = base64Chars.indexOf(str.charAt(i++));
			let d = base64Chars.indexOf(str.charAt(i++));

			// 处理无效字符
			if (a === -1 || b === -1) break;

			// 处理填充字符
			let hasPadding1 = str.charAt(i - 2) === '=';
			let hasPadding2 = str.charAt(i - 1) === '=';

			if (c === -1) c = 0;
			if (d === -1) d = 0;

			let a1 = (a << 2) | (b >> 4);
			let a2 = ((b & 15) << 4) | (c >> 2);
			let a3 = ((c & 3) << 6) | d;

			bytes.push(a1);

			// 根据填充字符决定是否添加后续字节
			if (!hasPadding1) {
				bytes.push(a2);
			}
			if (!hasPadding2 && !hasPadding1) {
				bytes.push(a3);
			}
		}

		// 将字节数组转换回 UTF-8 字符串
		return utf8BytesToString(bytes);
	}
}

// CommonJS 导出
module.exports = {
	base64Encode,
	base64Decode
};
