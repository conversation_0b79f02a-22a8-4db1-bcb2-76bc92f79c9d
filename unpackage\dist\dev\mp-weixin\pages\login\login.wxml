<view class="login-container data-v-b237504c"><view class="bg-decoration data-v-b237504c"><view class="circle circle-1 data-v-b237504c"></view><view class="circle circle-2 data-v-b237504c"></view><view class="circle circle-3 data-v-b237504c"></view></view><view class="login-card fade-in data-v-b237504c"><view class="login-header data-v-b237504c"><view class="logo data-v-b237504c">🔐</view><view class="title data-v-b237504c">欢迎使用本系统</view><view class="subtitle data-v-b237504c">请选择登录方式</view></view><view class="tab-container data-v-b237504c"><view data-event-opts="{{[['tap',[['switchTab',['password']]]]]}}" class="{{['tab-item','data-v-b237504c',(activeTab==='password')?'active':'']}}" bindtap="__e">密码登录</view><view data-event-opts="{{[['tap',[['switchTab',['phone']]]]]}}" class="{{['tab-item','data-v-b237504c',(activeTab==='phone')?'active':'']}}" bindtap="__e">手机登录</view></view><block wx:if="{{activeTab==='password'}}"><view class="form-container data-v-b237504c"><view class="input-group data-v-b237504c"><view class="input-icon data-v-b237504c">👤</view><input class="input-field data-v-b237504c" placeholder="请输入用户名/手机号" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['passwordForm']]]]]}}" value="{{passwordForm.username}}" bindinput="__e"/></view><view class="input-group data-v-b237504c"><view class="input-icon data-v-b237504c">🔒</view><input class="input-field data-v-b237504c" placeholder="请输入密码" password="{{!showPassword}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['passwordForm']]]]]}}" value="{{passwordForm.password}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="input-suffix data-v-b237504c" bindtap="__e">{{''+(showPassword?'🙈':'👁️')+''}}</view></view><block wx:if="{{captchaEnabled}}"><view class="input-group captcha-group data-v-b237504c"><view class="input-icon data-v-b237504c">🔢</view><input class="input-field captcha-input data-v-b237504c" placeholder="请输入验证码" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['passwordForm']]]]]}}" value="{{passwordForm.code}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['refreshCaptcha',['$event']]]]]}}" class="captcha-image data-v-b237504c" bindtap="__e"><block wx:if="{{codeUrl}}"><image class="captcha-img data-v-b237504c" src="{{codeUrl}}" mode="aspectFit"></image></block><block wx:else><view class="captcha-placeholder data-v-b237504c">点击获取</view></block></view></view></block><view class="checkbox-group data-v-b237504c"><view data-event-opts="{{[['tap',[['toggleRemember',['$event']]]]]}}" class="{{['checkbox','data-v-b237504c',(passwordForm.rememberMe)?'checked':'']}}" bindtap="__e"><view class="checkbox-icon data-v-b237504c">{{passwordForm.rememberMe?'✓':''}}</view></view><text class="checkbox-label data-v-b237504c">记住密码</text></view><button class="{{['login-btn','data-v-b237504c',(passwordLoading)?'loading':'']}}" disabled="{{passwordLoading}}" data-event-opts="{{[['tap',[['handlePasswordLogin',['$event']]]]]}}" bindtap="__e"><block wx:if="{{passwordLoading}}"><view class="loading-icon data-v-b237504c">⏳</view></block>{{''+(passwordLoading?'登录中...':'登录')+''}}</button></view></block><block wx:if="{{activeTab==='phone'}}"><view class="form-container data-v-b237504c"><view class="input-group data-v-b237504c"><view class="input-icon data-v-b237504c">📱</view><input class="input-field data-v-b237504c" placeholder="请输入手机号" type="number" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['phoneForm']]]]]}}" value="{{phoneForm.phone}}" bindinput="__e"/></view><view class="input-group sms-group data-v-b237504c"><view class="input-icon data-v-b237504c">💬</view><input class="input-field sms-input data-v-b237504c" placeholder="请输入短信验证码" data-event-opts="{{[['input',[['__set_model',['$0','phoneCode','$event',[]],['phoneForm']]]]]}}" value="{{phoneForm.phoneCode}}" bindinput="__e"/><button class="sms-btn data-v-b237504c" disabled="{{smsDisabled}}" data-event-opts="{{[['tap',[['sendSmsCode',['$event']]]]]}}" bindtap="__e">{{''+smsButtonText+''}}</button></view><button class="{{['login-btn','data-v-b237504c',(phoneLoading)?'loading':'']}}" disabled="{{phoneLoading}}" data-event-opts="{{[['tap',[['handlePhoneLogin',['$event']]]]]}}" bindtap="__e"><block wx:if="{{phoneLoading}}"><view class="loading-icon data-v-b237504c">⏳</view></block>{{''+(phoneLoading?'登录中...':'登录')+''}}</button></view></block><view class="login-footer data-v-b237504c"><view data-event-opts="{{[['tap',[['goToRegister',['$event']]]]]}}" class="footer-link data-v-b237504c" bindtap="__e">还没有账号？立即注册</view></view></view></view>