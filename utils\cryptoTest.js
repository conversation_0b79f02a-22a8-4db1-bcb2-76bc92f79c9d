// 加密解密功能测试
import { utils } from '@/api/index'

// 测试加密解密功能
export function testCrypto() {
	console.log('=== 开始测试加密解密功能 ===')
	
	try {
		// 测试1: 基本字符串加密解密
		const testString = 'test_password_123'
		console.log('原始字符串:', testString)
		
		const encrypted = utils.encrypt(testString)
		console.log('加密后:', encrypted)
		
		const decrypted = utils.decrypt(encrypted)
		console.log('解密后:', decrypted)
		
		const isMatch = testString === decrypted
		console.log('加密解密是否匹配:', isMatch)
		
		// 测试2: 空字符串
		const emptyEncrypted = utils.encrypt('')
		const emptyDecrypted = utils.decrypt(emptyEncrypted)
		console.log('空字符串测试:', '' === emptyDecrypted)
		
		// 测试3: 中文字符串
		const chineseString = '中文密码测试'
		const chineseEncrypted = utils.encrypt(chineseString)
		const chineseDecrypted = utils.decrypt(chineseEncrypted)
		console.log('中文字符串测试:', chineseString === chineseDecrypted)
		
		// 测试4: 特殊字符
		const specialString = '!@#$%^&*()_+-=[]{}|;:,.<>?'
		const specialEncrypted = utils.encrypt(specialString)
		const specialDecrypted = utils.decrypt(specialEncrypted)
		console.log('特殊字符测试:', specialString === specialDecrypted)
		
		// 测试5: Base64编码解码
		const base64Test = 'Hello World!'
		const base64Encoded = utils.base64Encode(base64Test)
		const base64Decoded = utils.base64Decode(base64Encoded)
		console.log('Base64测试:', base64Test === base64Decoded)
		
		console.log('=== 加密解密功能测试完成 ===')
		
		return {
			basicTest: isMatch,
			emptyTest: '' === emptyDecrypted,
			chineseTest: chineseString === chineseDecrypted,
			specialTest: specialString === specialDecrypted,
			base64Test: base64Test === base64Decoded
		}
		
	} catch (error) {
		console.error('加密解密测试失败:', error)
		return {
			error: error.message
		}
	}
}

// 测试微信小程序环境兼容性
export function testWechatCompatibility() {
	console.log('=== 开始测试微信小程序兼容性 ===')
	
	try {
		// 检查 uni.base64Encode 是否可用
		const hasUniBase64 = typeof uni.base64Encode === 'function'
		console.log('uni.base64Encode 可用:', hasUniBase64)
		
		// 测试自定义 Base64 实现
		const testData = 'compatibility_test'
		const customEncoded = utils.base64Encode(testData)
		const customDecoded = utils.base64Decode(customEncoded)
		const customTest = testData === customDecoded
		console.log('自定义Base64实现测试:', customTest)
		
		// 测试加密在微信小程序环境中的表现
		const encryptTest = utils.encrypt('wechat_test')
		const decryptTest = utils.decrypt(encryptTest)
		const wechatTest = 'wechat_test' === decryptTest
		console.log('微信小程序加密测试:', wechatTest)
		
		console.log('=== 微信小程序兼容性测试完成 ===')
		
		return {
			hasUniBase64,
			customBase64Test: customTest,
			wechatEncryptTest: wechatTest
		}
		
	} catch (error) {
		console.error('微信小程序兼容性测试失败:', error)
		return {
			error: error.message
		}
	}
}

// 在开发环境中自动运行测试
if (process.env.NODE_ENV === 'development') {
	// 延迟执行，确保 utils 已经加载
	setTimeout(() => {
		try {
			const cryptoResults = testCrypto()
			const compatResults = testWechatCompatibility()
			
			console.log('加密解密测试结果:', cryptoResults)
			console.log('兼容性测试结果:', compatResults)
		} catch (error) {
			console.error('测试执行失败:', error)
		}
	}, 1000)
}
