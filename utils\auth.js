// 用户认证和权限管理工具

/**
 * 获取用户token
 */
export function getToken() {
	return uni.getStorageSync('user_token')
}

/**
 * 设置用户token
 */
export function setToken(token) {
	uni.setStorageSync('user_token', token)
	console.log('Token已保存')
}

/**
 * 移除用户token
 */
export function removeToken() {
	uni.removeStorageSync('user_token')
	uni.removeStorageSync('user_info')
	console.log('Token和相关信息已清除')
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
	return uni.getStorageSync('user_info')
}

/**
 * 设置用户信息
 */
export function setUserInfo(userInfo) {
	return uni.setStorageSync('user_info', userInfo)
}

/**
 * 检查用户是否已登录
 */
export function isLoggedIn() {
	const token = getToken()
	const userInfo = getUserInfo()

	return !!(token && userInfo)
}



/**
 * 检查登录状态，如果未登录则跳转到登录页
 * @param {boolean} showToast 是否显示提示
 * @returns {boolean} 是否已登录
 */
export function checkLogin(showToast = true) {
	if (!isLoggedIn()) {
		const message = '请先登录'

		if (showToast) {
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 1500
			})
		}

		// 延迟跳转，确保toast显示
		setTimeout(() => {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		}, showToast ? 1500 : 0)

		return false
	}
	return true
}

/**
 * 用户登出
 */
export function logout() {
	return new Promise((resolve) => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					// 清除本地存储
					removeToken()

					uni.showToast({
						title: '已退出登录',
						icon: 'success',
						duration: 1500
					})

					// 跳转到登录页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/login/login'
						})
					}, 1500)

					resolve(true)
				} else {
					resolve(false)
				}
			}
		})
	})
}

/**
 * 页面登录检查混入
 * 在页面的 onLoad 或 onShow 中调用
 */
export const loginCheckMixin = {
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			return checkLogin()
		},

		// 退出登录
		handleLogout() {
			return logout()
		}
	}
}

/**
 * 需要登录的页面列表
 */
export const protectedPages = [
	'/pages/index/index',
	'/pages/activation/activation',
	'/pages/signin/signin'
]

/**
 * 检查当前页面是否需要登录
 */
export function isProtectedPage(url) {
	// 获取页面路径（去除参数）
	const pagePath = url.split('?')[0]
	return protectedPages.some(page => pagePath.includes(page))
}

// 全局变量，用于临时禁用路由守卫
let guardDisabled = false

/**
 * 临时禁用路由守卫
 */
export function disableGuard() {
	guardDisabled = true
	// 2秒后自动恢复
	setTimeout(() => {
		guardDisabled = false
	}, 2000)
}

/**
 * 全局页面跳转拦截
 */
export function setupNavigationGuard() {
	// 拦截 navigateTo
	const originalNavigateTo = uni.navigateTo
	uni.navigateTo = function(options) {
		if (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {
			console.log('navigateTo被拦截:', options.url, '登录状态:', isLoggedIn())
			checkLogin()
			return
		}
		return originalNavigateTo.call(this, options)
	}

	// 拦截 switchTab
	const originalSwitchTab = uni.switchTab
	uni.switchTab = function(options) {
		if (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {
			console.log('switchTab被拦截:', options.url, '登录状态:', isLoggedIn())
			checkLogin()
			return
		}
		return originalSwitchTab.call(this, options)
	}

	// 拦截 reLaunch
	const originalReLaunch = uni.reLaunch
	uni.reLaunch = function(options) {
		if (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {
			console.log('reLaunch被拦截:', options.url, '登录状态:', isLoggedIn())
			checkLogin()
			return
		}
		return originalReLaunch.call(this, options)
	}

	// 拦截 redirectTo
	const originalRedirectTo = uni.redirectTo
	uni.redirectTo = function(options) {
		if (isProtectedPage(options.url) && !isLoggedIn()) {
			checkLogin()
			return
		}
		return originalRedirectTo.call(this, options)
	}
}

/**
 * 自动登录检查（应用启动时调用）
 */
export async function autoLoginCheck() {
	const token = getToken()
	const userInfo = getUserInfo()

	if (!token || !userInfo) {
		// 没有登录信息，跳转到登录页
		uni.reLaunch({
			url: '/pages/login/login'
		})
		return false
	}

	// 可以在这里验证token是否有效
	// 如果有后端API，可以调用验证接口
	try {
		// 这里可以调用验证token的API
		// const res = await validateToken(token)
		// if (res.code !== 200) {
		//     removeToken()
		//     uni.reLaunch({
		//         url: '/pages/login/login'
		//     })
		//     return false
		// }

		return true
	} catch (error) {
		console.error('Token验证失败:', error)
		// 验证失败，清除登录信息
		removeToken()
		uni.reLaunch({
			url: '/pages/login/login'
		})
		return false
	}
}