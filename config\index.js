// 应用配置文件
const config = {
	// 开发环境配置
	development: {
		// API 基础地址 - 暂时使用占位符以启用mock API
		baseURL: 'http://localhost:8080',
		// 是否启用调试模式
		debug: true,
		// 请求超时时间
		timeout: 10000
	},

	// 生产环境配置
	production: {
		// API 基础地址 - 暂时使用占位符以启用mock API
		baseURL: 'http://localhost:8080',
		// 是否启用调试模式
		debug: false,
		// 请求超时时间
		timeout: 10000
	}
}

// 根据环境获取配置
function getConfig() {
	// 在 uniapp 中判断环境
	const env = process.env.NODE_ENV || 'development'
	return config[env] || config.development
}

export default getConfig()