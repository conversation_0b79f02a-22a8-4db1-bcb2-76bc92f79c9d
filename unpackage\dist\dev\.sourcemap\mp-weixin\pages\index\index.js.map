{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?71c0", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?d3a5", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?2333", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?4fc9", "uni-app:///pages/index/index.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?17a7", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?fe61"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "currentUser", "userQuota", "baseLimit", "activityLimit", "tempLimit", "totalLimit", "currentUseNum", "remainingLimit", "<PERSON><PERSON><PERSON><PERSON>", "hasSigned", "showUserMenuModal", "computed", "remainingTotalQuota", "userDisplayName", "onLoad", "setTimeout", "onShow", "methods", "checkLoginAndInit", "initPage", "user", "phone", "len", "tokenApi", "tokenRes", "tokenName", "res", "tokenTime", "tokenStatus", "tokenCompCode", "signInApi", "signRes", "console", "goToActivation", "uni", "url", "goToSignIn", "goToLogin", "showUserMenu", "closeUserMenu", "goToUserProfile", "title", "icon", "goToSettings", "goToAbout", "handleLogout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAurB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4F3sB;AAKA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EAEAC;IAAA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IAAA;IACA;IACAD;MACA;IACA;EACA;EAEAE;IACA;IACAC;MACA;MACA;MACA;MAEA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;gBACAC;gBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAF;cAAA;gBAAAG;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAD;kBACAE;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACA3B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAsB;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAMAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAN;QACAO;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAT;QACAO;QACAC;MACA;IACA;IAEA;IACAE;MACA;MACAV;QACAO;QACAC;MACA;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtSA;AAAA;AAAA;AAAA;AAAogC,CAAgB,89BAAG,EAAC,C;;;;;;;;;;;ACAxhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.userDisplayName.charAt(0)\n  var g1 = _vm.showUserMenuModal ? _vm.userDisplayName.charAt(0) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<view class=\"header-info\">\r\n\t\t\t\t\t<view class=\"header-title\">Useful工具箱</view>\r\n\t\t\t\t\t<view class=\"header-subtitle\">激活码管理与每日签到</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"header-user\" @click=\"showUserMenu\">\r\n\t\t\t\t\t<view class=\"user-avatar\">{{ userDisplayName.charAt(0) }}</view>\r\n\t\t\t\t\t<view class=\"user-name\">{{ userDisplayName }}</view>\r\n\t\t\t\t\t<view class=\"user-arrow\">▼</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 功能导航卡片 -->\r\n\t\t<view class=\"nav-cards\">\r\n\t\t\t<!-- 激活码管理卡片 -->\r\n\t\t\t<view class=\"nav-card activation-card fade-in\" @click=\"goToActivation\">\r\n\t\t\t\t<view class=\"nav-card-icon\">🔑</view>\r\n\t\t\t\t<view class=\"nav-card-title\">激活码管理</view>\r\n\t\t\t\t<view class=\"nav-card-desc\">获取激活码、验证激活码、查询额度</view>\r\n\t\t\t\t<view class=\"nav-card-arrow\">→</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 每日签到卡片 -->\r\n\t\t\t<view class=\"nav-card signin-card fade-in\" style=\"animation-delay: 0.1s;\" @click=\"goToSignIn\">\r\n\t\t\t\t<view class=\"nav-card-icon\">📅</view>\r\n\t\t\t\t<view class=\"nav-card-title\">每日签到</view>\r\n\t\t\t\t<view class=\"nav-card-desc\">每日签到获取额度、查看签到历史</view>\r\n\t\t\t\t<view class=\"nav-card-arrow\">→</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 快速状态显示 -->\r\n\t\t<view v-if=\"userInfo\" class=\"quick-status fade-in\" style=\"animation-delay: 0.2s;\">\r\n\t\t\t<view class=\"status-header\">\r\n\t\t\t\t<view class=\"status-title\">快速状态</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"status-content\">\r\n\t\t\t\t<view class=\"status-item\">\r\n\t\t\t\t\t<view class=\"status-label\">激活码状态:</view>\r\n\t\t\t\t\t<view class=\"status-value success\">已验证</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"status-item\">\r\n\t\t\t\t\t<view class=\"status-label\">今日签到:</view>\r\n\t\t\t\t\t<view class=\"status-value\" :class=\"hasSigned ? 'success' : 'warning'\">\r\n\t\t\t\t\t\t{{ hasSigned ? '已签到' : '未签到' }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"status-item\">\r\n\t\t\t\t\t<view class=\"status-label\">剩余额度:</view>\r\n\t\t\t\t\t<view class=\"status-value\">{{ remainingTotalQuota || 0 }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 未验证激活码提示 -->\r\n\t\t<view v-else class=\"no-user-tip fade-in\" style=\"animation-delay: 0.2s;\">\r\n\t\t\t<view class=\"tip-icon\">⚠️</view>\r\n\t\t\t<view class=\"tip-text\">您还未验证激活码，请先前往激活码管理页面验证激活码</view>\r\n\t\t\t<view class=\"tip-buttons\">\r\n\t\t\t\t<button class=\"btn btn-primary\" @click=\"goToActivation\">立即验证</button>\r\n\t\t\t\t<button class=\"btn btn-secondary\" @click=\"goToLogin\">用户登录</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 用户菜单弹窗 -->\r\n\t\t<view v-if=\"showUserMenuModal\" class=\"modal-overlay\" @click=\"closeUserMenu\">\r\n\t\t\t<view class=\"user-menu-content\" @click.stop=\"\">\r\n\t\t\t\t<view class=\"user-menu-header\">\r\n\t\t\t\t\t<view class=\"menu-avatar\">{{ userDisplayName.charAt(0) }}</view>\r\n\t\t\t\t\t<view class=\"menu-user-info\">\r\n\t\t\t\t\t\t<view class=\"menu-username\">{{ userDisplayName }}</view>\r\n\t\t\t\t\t\t<view class=\"menu-user-id\">手机: {{ currentUser.phonenumber || 'N/A' }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"user-menu-list\">\r\n\t\t\t\t\t<view class=\"menu-item logout-item\" @click=\"handleLogout\">\r\n\t\t\t\t\t\t<view class=\"menu-icon\">🚪</view>\r\n\t\t\t\t\t\t<view class=\"menu-text\">退出登录</view>\r\n\t\t\t\t\t\t<view class=\"menu-arrow\">→</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tsignInApi,\r\n\t\ttokenApi,\r\n\t\tutils\r\n\t} from '@/api/index'\r\n\timport {\r\n\t\tgetUserInfo\r\n\t} from '@/api/user'\r\n\timport {\r\n\t\tcheckLogin,\r\n\t\tlogout,\r\n\t\tgetToken,\r\n\t\tisLoggedIn\r\n\t} from '@/utils/auth'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 用户信息\r\n\t\t\t\tuserInfo: null,\r\n\t\t\t\tcurrentUser: null,\r\n\t\t\t\tuserQuota: {\r\n\t\t\t\t\tbaseLimit: 0,\r\n\t\t\t\t\tactivityLimit: 0,\r\n\t\t\t\t\ttempLimit: 0,\r\n\t\t\t\t\ttotalLimit: 0,\r\n\t\t\t\t\tcurrentUseNum: 0,\r\n\t\t\t\t\tremainingLimit: 0,\r\n\t\t\t\t\tpermanentQuota: 0\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// 签到状态\r\n\t\t\t\thasSigned: false,\r\n\r\n\t\t\t\t// 弹窗状态\r\n\t\t\t\tshowUserMenuModal: false\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\tremainingTotalQuota() {\r\n\t\t\t\tconst totalQuota = (this.userQuota.totalLimit || 0) + (this.userQuota.permanentQuota || 0)\r\n\t\t\t\tconst used = this.userQuota.currentUseNum || 0\r\n\t\t\t\treturn Math.max(0, totalQuota - used)\r\n\t\t\t},\r\n\r\n\t\t\t// 用户显示名称\r\n\t\t\tuserDisplayName() {\r\n\t\t\t\tif (this.currentUser) {\r\n\t\t\t\t\treturn this.currentUser.nickName || this.currentUser.phone || '用户'\r\n\t\t\t\t}\r\n\t\t\t\treturn '未登录'\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\t// 延迟检查登录状态，确保token已经保存\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.checkLoginAndInit()\r\n\t\t\t}, 100)\r\n\t\t},\r\n\r\n\t\tonShow() {\r\n\t\t\t// 延迟检查登录状态\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.checkLoginAndInit()\r\n\t\t\t}, 100)\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 检查登录状态并初始化页面\r\n\t\t\tcheckLoginAndInit() {\r\n\t\t\t\tconst token = getToken()\r\n\t\t\t\tconst userInfo = getUserInfo()\r\n\t\t\t\tconst loginStatus = isLoggedIn()\r\n\r\n\t\t\t\tif (!loginStatus) {\r\n\t\t\t\t\tif (!checkLogin(false)) {\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.initPage()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 初始化页面\r\n\t\t\tasync initPage() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取当前登录用户信息\r\n\t\t\t\t\tlet user = await getUserInfo()\r\n\t\t\t\t\tthis.currentUser = user.user\r\n\t\t\t\t\tlet phone = String(this.currentUser.phonenumber)\r\n\t\t\t\t\tlet len = phone.length;\r\n\t\t\t\t\tthis.currentUser.phonenumber = phone.substring(0, 3) + \"***\" + phone.substring(len - 4, len);\r\n\r\n\t\t\t\t\t// 获取用户token状态\r\n\t\t\t\t\tconst tokenRes = await tokenApi.getUserToken()\r\n\t\t\t\t\tif (tokenRes.code === 200 && tokenRes.data) {\r\n\t\t\t\t\t\tconst tokenName = tokenRes.data.tokenName\r\n\t\t\t\t\t\tif (tokenName) {\r\n\t\t\t\t\t\t\t// 验证激活码获取用户信息\r\n\t\t\t\t\t\t\tconst res = await tokenApi.getQuota(tokenName)\r\n\t\t\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t\t\t\ttokenName: tokenName,\r\n\t\t\t\t\t\t\t\t\ttokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),\r\n\t\t\t\t\t\t\t\t\ttokenStatus: 0,\r\n\t\t\t\t\t\t\t\t\ttokenCompCode: '尚未绑定'\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// 保存额度信息\r\n\t\t\t\t\t\t\t\tthis.userQuota = res.data || {\r\n\t\t\t\t\t\t\t\t\tbaseLimit: 0,\r\n\t\t\t\t\t\t\t\t\tactivityLimit: 0,\r\n\t\t\t\t\t\t\t\t\ttempLimit: 0,\r\n\t\t\t\t\t\t\t\t\ttotalLimit: 0,\r\n\t\t\t\t\t\t\t\t\tcurrentUseNum: 0,\r\n\t\t\t\t\t\t\t\t\tremainingLimit: 0,\r\n\t\t\t\t\t\t\t\t\tpermanentQuota: 0\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// 检查今日是否已签到\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tconst signRes = await signInApi.checkSignInToday(tokenName)\r\n\t\t\t\t\t\t\t\t\tif (signRes.code === 200) {\r\n\t\t\t\t\t\t\t\t\t\tthis.hasSigned = signRes.data\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\tconsole.error('检查签到状态失败:', error)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取用户信息失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 前往激活码管理页面\r\n\t\t\tgoToActivation() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activation/activation'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 前往签到页面\r\n\t\t\tgoToSignIn() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/signin/signin'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 前往登录页面\r\n\t\t\tgoToLogin() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 显示用户菜单\r\n\t\t\tshowUserMenu() {\r\n\t\t\t\tthis.showUserMenuModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭用户菜单\r\n\t\t\tcloseUserMenu() {\r\n\t\t\t\tthis.showUserMenuModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// 前往个人信息页面\r\n\t\t\tgoToUserProfile() {\r\n\t\t\t\tthis.closeUserMenu()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '功能开发中',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 前往设置页面\r\n\t\t\tgoToSettings() {\r\n\t\t\t\tthis.closeUserMenu()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '功能开发中',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 前往关于页面\r\n\t\t\tgoToAbout() {\r\n\t\t\t\tthis.closeUserMenu()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '功能开发中',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 处理退出登录\r\n\t\t\tasync handleLogout() {\r\n\t\t\t\tthis.closeUserMenu()\r\n\t\t\t\tawait logout()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t/* 页面容器 */\r\n\t.container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t/* 页面头部 */\r\n\t.header {\r\n\t\tpadding: 40rpx 0;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.header-content {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\r\n\t.header-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 56rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.header-subtitle {\r\n\t\tfont-size: 32rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t/* 用户信息 */\r\n\t.header-user {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder-radius: 50rpx;\r\n\t\tpadding: 15rpx 25rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.header-user:hover {\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\ttransform: translateY(-2rpx);\r\n\t}\r\n\r\n\t.user-avatar {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-right: 15rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.user-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-right: 10rpx;\r\n\t\tmax-width: 120rpx;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.user-arrow {\r\n\t\tfont-size: 20rpx;\r\n\t\topacity: 0.8;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t.header-user:hover .user-arrow {\r\n\t\ttransform: rotate(180deg);\r\n\t}\r\n\r\n\t/* 导航卡片容器 */\r\n\t.nav-cards {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 30rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t/* 导航卡片 */\r\n\t.nav-card {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\t\ttransition: all 0.3s ease;\r\n\t\tcursor: pointer;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.nav-card:hover {\r\n\t\ttransform: translateY(-6rpx);\r\n\t\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.nav-card::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 6rpx;\r\n\t\tbackground: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n\t}\r\n\r\n\t.activation-card::before {\r\n\t\tbackground: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);\r\n\t}\r\n\r\n\t.signin-card::before {\r\n\t\tbackground: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);\r\n\t}\r\n\r\n\t.nav-card-icon {\r\n\t\tfont-size: 60rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.nav-card-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.nav-card-desc {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tline-height: 1.5;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.nav-card-arrow {\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #409EFF;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 快速状态 */\r\n\t.quick-status {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\t\tmargin-bottom: 30rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.status-header {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tbackground: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);\r\n\t}\r\n\r\n\t.status-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.status-content {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.status-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 15rpx 0;\r\n\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t}\r\n\r\n\t.status-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.status-label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.status-value {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.status-value.success {\r\n\t\tcolor: #67C23A;\r\n\t}\r\n\r\n\t.status-value.warning {\r\n\t\tcolor: #E6A23C;\r\n\t}\r\n\r\n\t/* 未验证提示 */\r\n\t.no-user-tip {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 50rpx 30rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.tip-icon {\r\n\t\tfont-size: 80rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.tip-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t.tip-buttons {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.tip-buttons .btn {\r\n\t\tflex: 1;\r\n\t\tmax-width: 200rpx;\r\n\t}\r\n\r\n\t/* 按钮样式 */\r\n\t.btn {\r\n\t\tpadding: 25rpx 50rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: none;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttext-align: center;\r\n\t\tdisplay: inline-block;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn:hover {\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.btn-primary {\r\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-secondary {\r\n\t\tbackground: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t/* 动画效果 */\r\n\t.fade-in {\r\n\t\tanimation: fadeInUp 0.6s ease-out;\r\n\t}\r\n\r\n\t@keyframes fadeInUp {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(30rpx);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 用户菜单弹窗 */\r\n\t.modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tz-index: 1000;\r\n\t\tanimation: fadeIn 0.3s ease-out;\r\n\t}\r\n\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t.user-menu-content {\r\n\t\tbackground: #ffffff;\r\n\t\tborder-radius: 20rpx;\r\n\t\twidth: 90%;\r\n\t\tmax-width: 500rpx;\r\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);\r\n\t\tanimation: slideInUp 0.3s ease-out;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t@keyframes slideInUp {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(50rpx);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.user-menu-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.menu-avatar {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-right: 20rpx;\r\n\t\tborder: 3rpx solid rgba(255, 255, 255, 0.3);\r\n\t}\r\n\r\n\t.menu-user-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.menu-username {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.menu-user-id {\r\n\t\tfont-size: 24rpx;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.user-menu-list {\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\r\n\t.menu-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 25rpx 30rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t}\r\n\r\n\t.menu-item:hover {\r\n\t\tbackground-color: #f8f9fa;\r\n\t}\r\n\r\n\t.menu-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.menu-item.logout-item {\r\n\t\tcolor: #f56c6c;\r\n\t}\r\n\r\n\t.menu-item.logout-item:hover {\r\n\t\tbackground-color: #fef0f0;\r\n\t}\r\n\r\n\t.menu-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\twidth: 40rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.menu-text {\r\n\t\tflex: 1;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.menu-arrow {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t.menu-item:hover .menu-arrow {\r\n\t\ttransform: translateX(5rpx);\r\n\t}\r\n</style>", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755088456754\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}